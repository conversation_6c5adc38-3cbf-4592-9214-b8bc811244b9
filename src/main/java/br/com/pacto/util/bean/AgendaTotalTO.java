/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;
import edu.emory.mathcs.backport.java.util.Arrays;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import br.com.pacto.controller.to.DefaultScheduleEvent;

/**
 *
 * <AUTHOR>
 */
public class AgendaTotalTO extends DefaultScheduleEvent{

    private String responsavel;
    private Integer codigoResponsavel;
    
    private String local;
    private Integer codigoLocal;
    
    private String tipo;
    private Integer codigotipo;
    
    private String nivel;
    private Integer codigoNivel;
    
    private Integer ocupacao;
    private Integer nrVagas;
    private Integer desmarcados;
    private Integer reposicoes;
    private Integer experimentais;
    private Integer marcacoes;
    private Integer confirmacoes;
    private Integer nrVagasPreenchidas;
    private Integer compareceram;
    private Integer diaristas;
    private Integer visitantes;
    private Integer desafios;

    private String cor;
    private String icone;
    private String corLinha;
    private String titulo;
    private String identificador;
    private String diaSemana;
    
    private Integer tolerancia;
    private String mensagem;
    private Double bonificacao;
    private Integer pontosBonus;
    private Double meta;
    
    private Boolean aulaColetiva;
    private Boolean permitirAulaExperimental;
    private List<Integer> favoritoscods;
    private List<Integer> acessaramcods;
    
    private boolean substituiuProfessor = false;
    private ProfessorSubstituido substituido;
    
    private Map<String, String> alunos;
    private boolean jaMarcouEuQuero;
    private Map<String, String> alunosConfirmados;
    private String fotoProfessor;
    private String fotoModalidade;
    List<AgendadoTO> alunosAgendados;

    private Date vigencia;
    private boolean integracaoSpivi = false;
    private boolean aulaExperimental = false;
    private String textoStyle;
    private boolean permiteAlunoOutraEmpresa = false;
    private Integer codigoTurma;
    private String situacao;
    private Date dataEntrouTurma;
    private Date dataSaiuTurma;
    private boolean alunoEstaNaAula = false;
    private List<TurmaVideoDTO> linkVideos;
    private boolean bloquearMatriculasAcimaLimite;
    private String mapaEquipamentos;
    private String nome;
    private Date dataInicioVigencia;

    public String getTip(){
        StringBuilder tip = new StringBuilder("<b>Horário: </b>");
        tip.append(getInicio()).append("-").append(getFim());
        tip.append("<br/><b>Professor: </b>").append(responsavel);
        tip.append("<br/><b>Modalidade: </b>").append(tipo);
        if(alunos == null){
            tip.append("<br/>Nenhum aluno adicionado.");
            return tip.toString();
        }
        tip.append("<br/><b>Alunos: </b>");
        List<String> nomes = Arrays.asList(alunos.keySet().toArray());
        Collections.sort(nomes);
        int cont = 0;
        for(String k : nomes){
            if(cont >= 15){
                tip.append("<br/>").append("E mais ").append(nomes.size()-15).append(" outros...");
                break;
            }
            tip.append("<br/>").append(alunos.get(k)).append(" - ").append(k);
            cont++;
        }
        return tip.toString();
                
    }

    public AgendaTotalTO() {
    }
    
    public Date getDateInicio(){
        return getStartDate();
    }

    public AgendaTotalTO(AgendaTotalJSON json) throws Exception {
        setId(json.getId());
        setStartDate(Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"));
        setEndDate(Uteis.getDate(json.getFim(), "dd/MM/yyyy HH:mm"));
        setTitle(json.getNrVagasPreenchidas()+ "/" + json.getNrVagas());
        this.titulo = json.getTitulo();
        this.codigoLocal = json.getCodigoLocal();
        this.codigoResponsavel = json.getCodigoResponsavel();
        this.local = json.getLocal();
        this.tipo = json.getTipo();
        this.codigoNivel = json.getCodigoNivel();
        this.nivel = json.getNivel();
        this.codigotipo = json.getCodigoTipo();
        this.responsavel = json.getResponsavel();
        this.nrVagas = json.getNrVagas();
        this.nrVagasPreenchidas = json.getNrVagasPreenchidas();
        this.identificador = json.getId()+"_"+Uteis.getData(getStartDate(), "ddMMyy");
        this.aulaColetiva = json.getAulaCheia();
        this.permitirAulaExperimental = json.getPermitirAulaExperimental();
        this.diaSemana = json.getDiaSemana();
        this.cor = json.getCor();
        setStyleClass(this.cor);
        this.textoStyle = json.getTextoCor();
        this.tolerancia = json.getTolerancia();
        this.mensagem = json.getMensagem();
        this.bonificacao = json.getBonificacao();
        this.pontosBonus = json.getPontosBonus();
        this.meta = json.getMeta();
        this.jaMarcouEuQuero = json.getJaMarcouEuQuero();
        this.ocupacao = json.getOcupacao();
        this.fotoProfessor = json.getFotoProfessor();
        this.fotoModalidade = json.getFotoModalidade();
        this.integracaoSpivi = json.isIntegracaoSpivi();
        this.permiteAlunoOutraEmpresa = json.isPermiteAlunoOutraEmpresa();
        this.codigoTurma = json.getCodigoTurma();
        this.alunoEstaNaAula = json.isAlunoEstaNaAula();
        this.linkVideos = json.getLinkVideos();
        this.vigencia = json.getFimVigencia();
        this.dataInicioVigencia = json.getInicioVigencia();
        List<AgendadoTO> lista = new ArrayList<>();
        if (json.getAlunos() != null) {
            for (AgendadoJSON j : json.getAlunos()) {
                lista.add(new AgendadoTO(j));
            }
        }
        this.alunosAgendados = lista;
        try{
            this.situacao = json.getSituacao();
            this.dataEntrouTurma = json.getDataEntrouTurma();
            this.dataSaiuTurma = json.getDataSaiuTurma();
        }catch(Exception ignore){}
        this.bloquearMatriculasAcimaLimite = json.isBloquearMatriculasAcimaLimite();
        this.mapaEquipamentos = json.getMapaEquipamentos();
    }
    
    public String getDia(){
        return Uteis.getData(getStartDate());
    }
    
    public String getInicio(){
        return Uteis.getDataAplicandoFormatacao(getStartDate(), "HH:mm");
    }
    
    public String getFim(){
        return Uteis.getDataAplicandoFormatacao(getEndDate(), "HH:mm");
    }
    
    @Override
    public int hashCode() { 
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AgendaTotalTO)) {
            return false;
        }
        return super.equals(obj);
    }

    public String getResponsavel() {
        return responsavel;
    }
    
    public String getResponsavelAbreviado() {
        return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(responsavel);
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getCodigoResponsavel() {
        return codigoResponsavel;
    }
    
    

    public void setCodigoResponsavel(Integer codigoResponsavel) {
        this.codigoResponsavel = codigoResponsavel;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Integer getCodigoLocal() {
        return codigoLocal;
    }

    public void setCodigoLocal(Integer codigoLocal) {
        this.codigoLocal = codigoLocal;
    }

    public Integer getNrVagas() {
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getNrVagasPreenchidas() {
        if(nrVagasPreenchidas == null){
            nrVagasPreenchidas = 0;
        }
        return nrVagasPreenchidas;
    }

    public void setNrVagasPreenchidas(Integer nrVagasPreenchidas) {
        this.nrVagasPreenchidas = nrVagasPreenchidas;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getCodigotipo() {
        return codigotipo;
    }

    public void setCodigotipo(Integer codigotipo) {
        this.codigotipo = codigotipo;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigoNivel() {
        return codigoNivel;
    }

    public void setCodigoNivel(Integer codigoNivel) {
        this.codigoNivel = codigoNivel;
    }

    public String getCorLinha() {
        if(nrVagas <= nrVagasPreenchidas){
            return "#F78A8A";
        }
        return corLinha;
    }

    public void setCorLinha(String corLinha) {
        this.corLinha = corLinha;
    }

    public Boolean getAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(Boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }
    
    public String getDiaApresentar() {
        return Calendario.getData(getStartDate(), "EEE, d MMM");
    }
    public String getHoraInicioApresentar() {
        return Calendario.getData(getStartDate(), Calendario.MASC_HORA);
    }
    public String getHoraFimApresentar() {
        return Calendario.getData(getEndDate(), Calendario.MASC_HORA);
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Integer getDesmarcados() {
        if(null == desmarcados){
            desmarcados = 0;
        }
        return desmarcados;
    }

    public void setDesmarcados(Integer desmarcados) {
        this.desmarcados = desmarcados;
    }

    public Integer getReposicoes() {
        if(null == reposicoes){
            reposicoes = 0;
        }
        return reposicoes;
    }

    public void setReposicoes(Integer reposicoes) {
        this.reposicoes = reposicoes;
    }

    public Integer getCompareceram() {
        return compareceram;
    }

    public void setCompareceram(Integer compareceram) {
        this.compareceram = compareceram;
    }

    public List<Integer> getFavoritoscods() {
        if(favoritoscods == null){
            favoritoscods = new ArrayList<Integer>();
        }
        return favoritoscods;
    }

    public void setFavoritoscods(List<Integer> favoritoscods) {
        this.favoritoscods = favoritoscods;
    }

    public Integer getMarcacoes() {
        if(null == marcacoes){
            marcacoes = 0;
        }
        return marcacoes;
    }

    public void setMarcacoes(Integer marcacoes) {
        this.marcacoes = marcacoes;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public boolean isSubstituiuProfessor() {
        return substituiuProfessor;
    }

    public void setSubstituiuProfessor(boolean substituiuProfessor) {
        this.substituiuProfessor = substituiuProfessor;
    }

    public ProfessorSubstituido getSubstituido() {
        return substituido;
    }

    public void setSubstituido(ProfessorSubstituido substituido) {
        this.substituido = substituido;
    }

    public Map<String, String> getAlunos() {
        if(alunos == null){
            alunos = new HashMap<String, String>();
        }
        return alunos;
    }

    public void setAlunos(Map<String, String> alunos) {
        this.alunos = alunos;
    }
    
    @Override
    public String getStyleClass(){
        if(this.getNrVagas() <= this.getNrVagasPreenchidas()){
            return "aulacheia";
        }
        return super.getStyleClass();
    }
    public String getStyleClass(Boolean linha){
        if(linha){
            if(this.getNrVagas() <= this.getNrVagasPreenchidas()){
                return "aulacheia";
            }
        }
        return super.getStyleClass();
    }

    public List<Integer> getAcessaramcods() {
        if(acessaramcods == null){
            acessaramcods = new ArrayList<Integer>();
        }
        return acessaramcods;
    }

    public void setAcessaramcods(List<Integer> acessaramcods) {
        this.acessaramcods = acessaramcods;
    }

    public boolean getJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public Integer getConfirmacoes() {
        return confirmacoes;
    }

    public void setConfirmacoes(Integer confirmacoes) {
        this.confirmacoes = confirmacoes;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public Integer getExperimentais() {
        if(null == experimentais){
            experimentais = 0;
        }
        return experimentais;
    }

    public void setExperimentais(Integer experimentais) {
        this.experimentais = experimentais;
    }

    public Map<String, String> getAlunosConfirmados() {
        if(alunosConfirmados == null){
           alunosConfirmados = new HashMap<String, String>();
        }
        return alunosConfirmados;
    }

    public void setAlunosConfirmados(Map<String, String> alunosConfirmados) {
        this.alunosConfirmados = alunosConfirmados;
    }
    
    public Integer getDiaristas() {
        if(null == diaristas){
            diaristas = 0;
        }
        return diaristas;
    }

    public void setDiaristas(Integer diaristas) {
        this.diaristas = diaristas;
    }

    public Integer getDesafios() {
        if(null == desafios){
            desafios = 0;
        }
        return desafios;
    }

    public void setDesafios(Integer desafios) {
        this.desafios = desafios;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getFotoModalidade() {
        return fotoModalidade;
    }

    public void setFotoModalidade(String fotoModalidade) {
        this.fotoModalidade = fotoModalidade;
    }

    public Integer getVisitantes() {
        if(null == visitantes){
            visitantes = 0;
        }
        return visitantes;
    }

    public void setVisitantes(Integer visitantes) {
        this.visitantes = visitantes;
    }

    public Date getVigencia() {
        return vigencia;
    }

    public void setVigencia(Date vigencia) {
        this.vigencia = vigencia;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(final boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public String getTextoStyle() {
        return textoStyle;
    }

    public void setTextoStyle(String textoStyle) {
        this.textoStyle = textoStyle;
    }

    public boolean isAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(boolean aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public List<AgendadoTO> getAlunosAgendados() {
        return alunosAgendados;
    }

    public void setAlunosAgendados(List<AgendadoTO> alunosAgendados) {
        this.alunosAgendados = alunosAgendados;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Integer getCodigoTurma() {
        return codigoTurma;
    }

    public void setCodigoTurma(Integer codigoTurma) {
        this.codigoTurma = codigoTurma;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurma(Date dataEntrouTurma) {
        this.dataEntrouTurma = dataEntrouTurma;
    }

    public Date getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurma(Date dataSaiuTurma) {
        this.dataSaiuTurma = dataSaiuTurma;
    }

    public boolean isAlunoEstaNaAula() {
        return alunoEstaNaAula;
    }

    public void setAlunoEstaNaAula(boolean alunoEstaNaAula) {
        this.alunoEstaNaAula = alunoEstaNaAula;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    public Date getDataInicioVigencia() {
        return dataInicioVigencia;
    }

    public void setDataInicioVigencia(Date dataInicioVigencia) {
        this.dataInicioVigencia = dataInicioVigencia;
    }
}
