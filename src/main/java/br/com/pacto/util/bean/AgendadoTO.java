/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.AgendadoJSON;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AgendadoTO implements Serializable{

    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer codigoContrato;
    private String idAgendamento;
    private String telefones;
    private Date inicio;
    private Date fim;
    private String nome;
    private String fotokey;
    private String matricula;
    private boolean desmarcado = false;
    private boolean reposto = false;
    private boolean reposicao = false;
    private AgendaTotalTO agendamento;
    private String horaAcesso;
    private boolean favorito = false;
    private Integer saldoCreditoTreino = 0;
    private Boolean usaSaldo = false;
    private Boolean usaTurma = false;
    private Date dataAcesso;
    private boolean confirmado = false;
    private String justificativa;
    private String situacaoContrato;
    private String situacao;
    private Integer qtdReposicoesFuturas = 0;
    private Boolean contratoFuturo = false;
    private List<Integer> modalidadesContratoFuturo = new ArrayList<Integer>();
    private Integer codContratoFuturo = 0;
    private Integer qtdCreditoFuturo = 0;
    private boolean gymPass = false;
    private boolean totalPass = false;
    private boolean diaria = false;
    private boolean experimental = false;
    private boolean visitante = false;
    private boolean integracaoSpivi = false;
    private Integer spiviSeat;
    private Integer spiviEventID;
    private Integer spiviClientID;
    private Boolean desafio = false;
    private String dataNascimento = null;
    private String email  = null;
    private String sexo  = null;
    private String cidade  = null;
    private boolean forcarAulaExperimentalAtivo = false;
    private boolean fixo = false;
    private Long horarioMarcacao;
    private Integer codigoPassivo;
    private Integer codigoIndicado;
    private boolean espera;
    private String equipamentoReservado;
    private String userIdSelfloops;

    private Boolean autorizadoGestaoRede = false;
    private String codAcessoAutorizado = "";
    private Integer matriculaAutorizado = 0;


    public AgendadoTO cloneTO() {
        AgendadoTO clone = new AgendadoTO();
        clone.codigoPessoa = this.codigoPessoa;
        clone.codigoCliente = this.codigoCliente;
        clone.codigoContrato = this.codigoContrato;
        clone.idAgendamento = this.idAgendamento;
        clone.telefones = this.telefones;
        clone.inicio = this.inicio;
        clone.fim = this.fim;
        clone.nome = this.nome;
        clone.fotokey = this.fotokey;
        clone.matricula = this.matricula;
        clone.desmarcado = this.desmarcado;
        clone.reposto = this.reposto;
        clone.reposicao  = this.reposicao;
        clone.agendamento = this.agendamento;
        clone.horaAcesso = this.horaAcesso;
        clone.horarioMarcacao = this.horarioMarcacao;
        clone.favorito = this.favorito;
        clone.saldoCreditoTreino = this.saldoCreditoTreino;
        clone.usaSaldo = this.usaSaldo;
        clone.usaTurma = this.usaTurma;
        clone.dataAcesso = this.dataAcesso;
        clone.confirmado = this.confirmado;
        clone.situacaoContrato = this.situacaoContrato;

        clone.situacao = this.situacao;
        clone.qtdReposicoesFuturas = this.qtdReposicoesFuturas;
        clone.contratoFuturo = this.contratoFuturo;
        clone.modalidadesContratoFuturo = this.modalidadesContratoFuturo;
        clone.codContratoFuturo = this.codContratoFuturo;
        clone.qtdCreditoFuturo = this.qtdCreditoFuturo;
        clone.diaria = this.diaria;
        clone.experimental = this.experimental;
        clone.visitante = this.visitante;

        clone.integracaoSpivi = this.integracaoSpivi;
        clone.spiviSeat = this.spiviSeat;

        clone.desafio = this.desafio;
        clone.dataNascimento = this.dataNascimento;
        clone.email  = this.email;
        clone.sexo  = this.sexo;
        clone.cidade  = this.cidade;
        clone.spiviClientID = this.spiviClientID;
        clone.gymPass = this.gymPass;
        clone.totalPass = this.totalPass;
        clone.fixo = this.fixo;
        clone.codigoPassivo = this.codigoPassivo;
        clone.codigoIndicado = this.codigoIndicado;
        clone.espera = this.espera;
        clone.equipamentoReservado = this.equipamentoReservado;
        clone.userIdSelfloops = this.userIdSelfloops;
        clone.autorizadoGestaoRede = this.autorizadoGestaoRede;
        clone.codAcessoAutorizado = this.codAcessoAutorizado;
        clone.matriculaAutorizado = this.matriculaAutorizado;
        return clone;
    }

    public Long getHorarioMarcacao() {
        return horarioMarcacao;
    }

    public void setHorarioMarcacao(Long horarioMarcacao) {
        this.horarioMarcacao = horarioMarcacao;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(final boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getDataAgendamentoPtbr(){
        return Uteis.getDataAplicandoFormatacao(getDataAgendamento(), "dd-MM-yyyy");
    }

    public Boolean getExperimental(){
        try {
            return reposicao && UteisValidacao.emptyNumber(codigoContrato);
        }catch (Exception e){
            return false;
        }
    }


    public Date getDataAgendamento(){
        return agendamento == null ? null : agendamento.getStartDate();
    }

    public String getHorario(){
        return agendamento == null ? null : agendamento.getInicio()+"-"+agendamento.getFim();
    }

    public String getTurma(){
        return agendamento == null ? null : agendamento.getTitulo();
    }

    public AgendadoTO() {
    }

    public AgendadoTO(AgendadoJSON json) throws Exception {
        this.codigoPessoa = json.getCodigoPessoa();
        this.codigoContrato = json.getCodigoContrato();
        this.nome = json.getNome();
        this.telefones = json.getTelefones();
        this.matricula = json.getMatricula();
        this.fotokey = json.getFotokey();
        this.codigoCliente = json.getCodigoCliente();
        this.idAgendamento = json.getId_agendamento();
        this.inicio = json.getInicio() == null || json.getInicio().length() < 10 ? null : Uteis.getDate(json.getInicio(), "dd/MM/yyyy");
        this.fim = json.getFim() == null || json.getFim().length() < 10 ? null : Uteis.getDate(json.getFim(), "dd/MM/yyyy");
        this.saldoCreditoTreino = json.getSaldoCreditoTreino();
        this.usaSaldo = json.getUsaSaldo();
        this.usaTurma = json.getUsaTurma();
        this.situacaoContrato = json.getSituacaoContrato();
        this.reposicao = json.isPresencaReposicao();
        this.situacao = json.getSituacao();
        this.confirmado = json.getConfirmado();
        this.qtdReposicoesFuturas = json.getQtdReposicoesFuturas();
        this.contratoFuturo = json.getContratoFuturo();
        this.modalidadesContratoFuturo = json.getModalidadesContratoFuturo();
        this.codContratoFuturo = json.getCodContratoFuturo();
        this.qtdCreditoFuturo = json.getQtdCreditoFuturo();
        this.situacao = json.getSituacao();
        this.confirmado = json.getConfirmado();
        this.gymPass = json.isGymPass();
        this.totalPass = json.isTotalPass();
        this.diaria = json.isDiaria();
        this.espera = json.isEspera();
        this.experimental = json.isExperimental();
        this.spiviSeat = json.getSpiviSeat();

        this.desafio = json.isDesafio();
        this.dataNascimento = json.getDataNascimento();
        this.spiviClientID = json.getSpiviClientID();
        this.sexo  = json.getSexo();
        this.cidade  = json.getCidade();
        this.email = json.getEmail();
        this.horarioMarcacao = json.getHorarioMarcacao();
        this.fixo = json.isFixo();
        this.codigoPassivo = json.getCodigoPassivo();
        this.codigoIndicado = json.getCodigoIndicado();
        this.equipamentoReservado = json.getEquipamentoReservado();
        this.userIdSelfloops = json.getUserIdSelfloops();
        this.autorizadoGestaoRede = json.getAutorizadoGestaoRede();
        this.codAcessoAutorizado = json.getCodAcessoAutorizado();
        this.matriculaAutorizado = json.getMatriculaAutorizado();
    }

    public AgendadoTO(AgendadoTO json, AgendaTotalTO agenda) {
        this.saldoCreditoTreino = json.getSaldoCreditoTreino();
        this.usaSaldo = json.getUsaSaldo();
        this.codigoPessoa = json.getCodigoPessoa();
        this.codigoContrato = json.getCodigoContrato();
        this.nome = json.getNome();
        this.matricula = json.getMatricula().toString();
        this.fotokey = json.getFotokey();
        this.codigoCliente = json.getCodigoCliente();
        this.idAgendamento = agenda.getIdentificador();
        this.inicio = agenda.getStartDate();
        this.fim = agenda.getEndDate();
        this.agendamento = agenda;
        this.usaTurma = json.getUsaTurma();
        this.dataAcesso = json.dataAcesso;
        this.horaAcesso = json.horaAcesso;
        this.confirmado = json.isConfirmado();
        this.situacaoContrato = json.getSituacaoContrato();
        this.situacao = json.getSituacao();
        this.qtdReposicoesFuturas = json.getQtdReposicoesFuturas();
        this.contratoFuturo = json.getContratoFuturo();
        this.modalidadesContratoFuturo = json.getModalidadesContratoFuturo();
        this.codContratoFuturo = json.getCodContratoFuturo();
        this.qtdCreditoFuturo = json.getQtdCreditoFuturo();
        this.diaria = json.isDiaria();
        this.experimental = json.isExperimental();
        this.desafio = json.getDesafio();
        this.dataNascimento = json.getDataNascimento();
        this.horarioMarcacao = json.getHorarioMarcacao();
        this.fixo = json.isFixo();
        this.codigoPassivo = json.getCodigoPassivo();
        this.codigoIndicado = json.getCodigoIndicado();
    }

    public AgendadoJSON toJson() {
        AgendadoJSON json = new AgendadoJSON();
        json.setCodigoPessoa(this.codigoPessoa);
        json.setNome(this.nome);
        json.setMatricula(this.matricula);
        json.setCodigoCliente(this.codigoCliente);
        json.setId_agendamento(this.idAgendamento);
        json.setInicio(Uteis.getDataAplicandoFormatacao(this.inicio, "dd/MM/yyyy"));
        json.setFim(Uteis.getDataAplicandoFormatacao(this.fim, "dd/MM/yyyy"));
        json.setSituacao(this.getSituacao());
        json.setConfirmado(this.confirmado);
        json.setContratoFuturo(this.contratoFuturo);
        json.setModalidadesContratoFuturo(this.modalidadesContratoFuturo);
        json.setCodContratoFuturo(this.codContratoFuturo);
        json.setDiaria(this.diaria);
        json.setExperimental(this.experimental);
        json.setDataNascimento(this.dataNascimento);
        return json;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeMinusculo() {
        return nome.toLowerCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getPrimeiroNomeComLetraSobrenome() {
        if (nome != null) {
            return Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome, true);
        }
        return "";
    }

    public String getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(String idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getTelefones() {
        return telefones;
    }
    public String getTipApresentar() {
        return "<b>" +nome + "</b>"+(telefones ==null ? ""
                : ("<br/>"+telefones.replaceAll(";", "<br/>")));
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public boolean isDesmarcado() {
        return desmarcado;
    }

    public void setDesmarcado(boolean desmarcado) {
        this.desmarcado = desmarcado;
    }

    public boolean isReposto() {
        return reposto;
    }

    public void setReposto(boolean reposto) {
        this.reposto = reposto;
    }

    public boolean isReposicao() {
        return reposicao;
    }

    public void setReposicao(boolean reposicao) {
        this.reposicao = reposicao;
    }

    public AgendaTotalTO getAgendamento() {
        return agendamento;
    }

    public void setAgendamento(AgendaTotalTO agendamento) {
        this.agendamento = agendamento;
    }

    public String getHoraAcesso() {
        return horaAcesso;
    }

    public void setHoraAcesso(String horaAcesso) {
        this.horaAcesso = horaAcesso;
    }

    public String getNomeAbreviadoMinusculo() {
        return getNomeAbreviado().toLowerCase();
    }
    public String getNomeAbreviado() {
        if (nome != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        } else {
            return "";
        }
    }

    public boolean isFavorito() {
        return favorito;
    }

    public void setFavorito(boolean favorito) {
        this.favorito = favorito;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Boolean getUsaSaldo() {
        return usaSaldo;
    }

    public void setUsaSaldo(Boolean usaSaldo) {
        this.usaSaldo = usaSaldo;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public Boolean getUsaTurma() {
        return usaTurma;
    }

    public void setUsaTurma(Boolean usaTurma) {
        this.usaTurma = usaTurma;
    }

    public boolean isConfirmado() {
        return confirmado;
    }

    public void setConfirmado(boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Integer getQtdReposicoesFuturas() {
        return qtdReposicoesFuturas;
    }

    public void setQtdReposicoesFuturas(Integer qtdReposicoesFuturas) {
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }
    public Boolean getContratoFuturo() {
        return contratoFuturo;
    }

    public void setContratoFuturo(Boolean contratoFuturo) {
        this.contratoFuturo = contratoFuturo;
    }

    public List<Integer> getModalidadesContratoFuturo() {
        return modalidadesContratoFuturo;
    }

    public void setModalidadesContratoFuturo(List<Integer> modalidadesContratoFuturo) {
        this.modalidadesContratoFuturo = modalidadesContratoFuturo;
    }

    public Integer getCodContratoFuturo() {
        return codContratoFuturo;
    }

    public void setCodContratoFuturo(Integer codContratoFuturo) {
        this.codContratoFuturo = codContratoFuturo;
    }

    public Integer getQtdCreditoFuturo() {
        return qtdCreditoFuturo;
    }

    public void setQtdCreditoFuturo(Integer qtdCreditoFuturo) {
        this.qtdCreditoFuturo = qtdCreditoFuturo;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public boolean isGymPass() {
        return gymPass;
    }

    public void setGymPass(boolean gymPass) {
        this.gymPass = gymPass;
    }

    public boolean isTotalPass() {
        return totalPass;
    }

    public void setTotalPass(boolean totalPass) {
        this.totalPass = totalPass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }


    public boolean isExperimental() {
        return experimental;
    }

    public void setExperimental(boolean experimental) {
        this.experimental = experimental;
    }

    public boolean isVisitante() {
        return visitante;
    }

    public void setVisitante(boolean visitante) {
        this.visitante = visitante;
    }

    public Integer getSpiviSeat() {
        return spiviSeat;
    }

    public void setSpiviSeat(Integer spiviSeat) {
        this.spiviSeat = spiviSeat;
    }

    public Integer getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(Integer spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public Boolean getDesafio() {
        return desafio;
    }

    public void setDesafio(Boolean desafio) {
        this.desafio = desafio;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public boolean isForcarAulaExperimentalAtivo() {
        return forcarAulaExperimentalAtivo;
    }

    public void setForcarAulaExperimentalAtivo(boolean forcarAulaExperimentalAtivo) {
        this.forcarAulaExperimentalAtivo = forcarAulaExperimentalAtivo;
    }

    public boolean isFixo() {
        return fixo;
    }

    public void setFixo(boolean fixo) {
        this.fixo = fixo;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Integer getCodigoIndicado() {
        return codigoIndicado;
    }

    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    public void setEspera(boolean espera) {
        this.espera = espera;
    }

    public boolean isEspera() {
        return espera;
    }

    public String getEquipamentoReservado() {
        return equipamentoReservado;
    }

    public void setEquipamentoReservado(String equipamentoReservado) {
        this.equipamentoReservado = equipamentoReservado;
    }

    public String getUserIdSelfloops() {
        return userIdSelfloops;
    }

    public void setUserIdSelfloops(String userIdSelfloops) {
        this.userIdSelfloops = userIdSelfloops;
    }

    public Boolean getAutorizadoGestaoRede() {
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }
}
