/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import br.com.pacto.util.impl.JSFUtilities;
import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import java.util.TimeZone;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

/**
 * Encapsular métodos de manipulação de datas
 *
 * <AUTHOR>
 */
public class Calendario {

    public static final String MASC_DATAHORA = "dd/MM/yyyy HH:mm:ss";
    public static final String MASC_DATA = "dd/MM/yyyy";
    public static final String MASC_HORA = "HH:mm";

    public static Locale getDefaultLocale() {
        return new Locale("pt", "BR");
    }
    public static Date dia = null;

    public static Date getDataFromSession() {
        if (dia != null) {
            return (Date) dia.clone();
        }
        Date dataSistema = (Date) JSFUtilities.getFromSession("dataSistema");
        if (dataSistema == null) {
            return new Date();
            //} else {
            //System.out.println("ATEN��O!!! Modo DESENVOLVIMENTO HABILITADO! DataSistema est� na SESS�O!");
        }
        return dataSistema;
    }

    public static Date ontem() {
        return Uteis.obterDataAnterior(hoje(), 1);
    }

    public static void setDataOnSession(Date dataSistema) {
        JSFUtilities.storeOnSession("dataSistema", dataSistema);
    }

    public static void imprimirConsoleTodosTimeZones() {
        Date today = new Date();

        // Get all time zone ids
        String[] zoneIds = TimeZone.getAvailableIDs();
        Map<String, String> mapa = new HashMap();

        // View every time zone
        for (int i = 0; i < zoneIds.length; i++) {
            // Get time zone by time zone id
            TimeZone tz = TimeZone.getTimeZone(zoneIds[i]);

            // Get the display name
            String shortName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.SHORT);
            String longName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.LONG);
            mapa.put(tz.getID(), tz.getID() + " - " + shortName + " - " + longName);

            // Get the number of hours from GMT
            int rawOffset = tz.getRawOffset();
            int hour = rawOffset / (60 * 60 * 1000);
            int min = Math.abs(rawOffset / (60 * 1000)) % 60;

            // Does the time zone have a daylight savings time period?
            boolean hasDST = tz.useDaylightTime();

            // Is the time zone currently in a daylight savings time?
            boolean inDST = tz.inDaylightTime(today);

        }
        ArrayList<String> lista = new ArrayList(mapa.values());

        Collections.sort(lista);
        for (String string : lista) {

            System.out.println(string);

        }
    }

    /**
     * Retorna uma data usando 'currentDate' data passada como argumento,
     * aplicando um TimeZone espec�fico.
     *
     * @param currentDate
     * @param timeZoneId
     * @return
     */
    public static Date getDateInTimeZone(Date currentDate, String timeZoneID) {
        String timeZoneDefault = timeZoneID.isEmpty() ? obterTimeZoneIDDefault() : timeZoneID;
        Calendar mbCal = new GregorianCalendar(TimeZone.getTimeZone(timeZoneDefault));
        mbCal.setTimeInMillis(currentDate.getTime());

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.set(Calendar.YEAR, mbCal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, mbCal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, mbCal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, mbCal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, mbCal.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, mbCal.get(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND, mbCal.get(Calendar.MILLISECOND));

        return cal.getTime();
    }

    /**
     * Procura um TimeZone default definido na sess�o do Sistema (http session),
     * ou retorna o TimeZone default de Brasilia (Brazil/East)
     *
     * <AUTHOR> Maciel
     * @return
     */
    public static String obterTimeZoneIDDefault() {
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null) {
            String tzID = (String) JSFUtilities.getFromSession("timeZoneID");

            if (tzID != null && !tzID.isEmpty()) {
                return tzID;
            } else {
                return "Brazil/East";
            }
        } else {
            return "Brazil/East";
        }

    }

    /**
     * Retorna a data e hora atual. Tamb�m encapsula cen�rios de sobreposi��o da
     * data/hora atual, se necess�rio. Deve ser utilizado em todo o sistema no
     * lugar de 'negocio.comuns.utilitarias.Calendario.hoje()'
     *
     * @return Date
     */
    public static Date hoje() {
        //////
        //Para utilizar o sistema numa data espec�fica, deve ser informada a data na tela 'inicio.jsp',
        //que alterar� o atributo 'data' estaticamente.
        //////

        String dataSistema = String.format("%s %s",
                new Object[]{
            Uteis.getData(getDataFromSession()),
            Calendario.agora("")});

        DateFormat df = new SimpleDateFormat(MASC_DATAHORA + " SSS a");
        Date dataAtual = null;
        try {
            dataAtual = df.parse(dataSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            df = null;
        }
        // dataBase � uma variavel em request que � utilizada no processo de negocia�ao do contrato.
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null) {
            HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
            if (request != null) {
                Date dataBase = (Date) request.getAttribute("dataBase");
                if (dataBase != null) {
                    return (Date) dataBase.clone();
                }
            }
        }
        return (Date) dataAtual.clone();
    }

    /**
     * Similar ao m�todo Calendario.hoje(), este retorna a data atual obedecendo
     * o TimeZone 'timeZoneID' passado como argumento.
     *
     * @return Date
     */
    public static Date hoje(String timeZoneID) {
        /**
         * Para utilizar o sistema numa data espec�fica, deve ser informada a
         * data na tela 'inicio.jsp', que alterar� o atributo 'data' da sess�o
         * HTTP
         */
        String dataSistema = String.format("%s %s",
                new Object[]{
            Uteis.getData(getDateInTimeZone(getDataFromSession(), timeZoneID)),
            Calendario.agora(timeZoneID)});

        DateFormat df = new SimpleDateFormat(MASC_DATAHORA + " SSS a");
        Date dataAtual = null;
        try {
            dataAtual = df.parse(dataSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            df = null;
        }
        // dataBase � uma variavel em request que � utilizada no processo de negocia�ao do contrato.
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null) {
            HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
            Date dataBase = (Date) request.getAttribute("dataBase");
            if (dataBase != null) {
                return (Date) dataBase.clone();
            }
        }
        return (Date) dataAtual.clone();
    }

    public static Date hojeSemRequest() {
        /**
         * Para utilizar o sistema numa data espec�fica, deve ser informada a
         * data na tela 'inicio.jsp', que alterar� o atributo 'data' da sess�o
         * HTTP
         */
        String dataSistema = String.format("%s %s",
                new Object[]{
            Uteis.getData(getDataFromSession()),
            Calendario.agora("")});

        DateFormat df = new SimpleDateFormat(MASC_DATAHORA + " SSS a");
        Date dataAtual = null;
        try {
            dataAtual = df.parse(dataSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        } finally {
            df = null;
        }
        return (Date) dataAtual.clone();
    }

    public static String agora(String timeZoneID) {
        DateFormat df = new SimpleDateFormat("HH:mm:ss SSS a", getDefaultLocale());
        Date hoje = getDateInTimeZone(new Date(), timeZoneID);
        return df.format(hoje);
    }

    /**
     * Retorna VERDADEIRO se a data passada como par�metro est� entre
     * <i>dataInicio</i> e <i>dataFim</i>
     *
     * @param dataQueEstaEntre data que ser� verifica entre dataInicio e dataFim
     * @param dataInicio
     * @param dataFim
     * @return <i>true</i> caso a <i>dataQueEstaEntre</i> esteja entre a das
     * duas outras datas,
     * <i>false</i> caso n�o estejam entre.
     * <AUTHOR> Maciel
     */
    public static boolean entre(final Date dataQueEstaEntre, final Date dataInicio, final Date dataFim) {
        //inicializar calend�rios, pois a compara��o after e before exige que sejam
        //inst�ncias de Calendar
        Calendar calDataQueEstaEntre = Calendario.getInstance();
        calDataQueEstaEntre.setTime(dataQueEstaEntre);

        Calendar calDataInicio = Calendario.getInstance();
        calDataInicio.setTime(dataInicio);

        Calendar calDataFim = Calendario.getInstance();
        calDataFim.setTime(dataFim);

        if ((calDataQueEstaEntre.after(calDataInicio) && (calDataQueEstaEntre.before(calDataFim))) ||
                (calDataQueEstaEntre.equals(calDataInicio) || calDataQueEstaEntre.equals(calDataFim))) {
            return true;
        } else {
            return false;
        }

    }

    public static boolean entreENaoIgual(final Date dataQueEstaEntre, final Date dataInicio, final Date dataFim) {
        Calendar calDataQueEstaEntre = Calendario.getInstance();
        calDataQueEstaEntre.setTime(dataQueEstaEntre);

        Calendar calDataInicio = Calendario.getInstance();
        calDataInicio.setTime(dataInicio);

        Calendar calDataFim = Calendario.getInstance();
        calDataFim.setTime(dataFim);

        if ((calDataQueEstaEntre.after(calDataInicio) && (calDataQueEstaEntre.before(calDataFim)))) {
            return true;
        } else {
            return false;
        }

    }

    public static boolean entreComHoraZerada(Date dataQueEstaEntre, Date dataInicio, Date dataFim) {
        dataQueEstaEntre = getDataComHoraZerada(dataQueEstaEntre);
        dataInicio = getDataComHoraZerada(dataInicio);
        dataFim = getDataComHoraZerada(dataFim);
        return entre(dataQueEstaEntre, dataInicio, dataFim);
    }

    /**
     * Retorna VERDADEIRO se a data passada como par�metro � menor que a data
     * comparada
     *
     * @param <i>dataQueDeveSerMenor</i> data que ser� verifica se � menor do
     * que a outra.
     * @param dataComparada
     * @return <i>true</i> caso a <i>dataQueDeveSerMenor</i> seja menor do que a
     * dataComparada,
     * <i>false</i> caso n�o seja menor.
     * <AUTHOR> Maciel
     */
    public static boolean menor(final Date dataQueDeveSerMenor, final Date dataComparada) {
        //inicializar calend�rios, pois a compara��o after e before exige que sejam
        //inst�ncias de Calendar
        Calendar calDataQueDeveSerMenor = Calendario.getInstance();
        calDataQueDeveSerMenor.setTime(getDataComHoraZerada(dataQueDeveSerMenor));

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(getDataComHoraZerada(dataComparada));


        if (calDataQueDeveSerMenor.before(calDataComparada)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * Retorna VERDADEIRO se a data passada como par�metro � maior que a data
     * comparada
     *
     * @param <i>dataQueDeveSerMaior</i> data que ser� verifica se � menor do
     * que a outra.
     * @param dataComparada
     * @return <i>true</i> caso a <i>dataQueDeveSerMaior</i> seja menor do que a
     * dataComparada,
     * <i>false</i> caso n�o seja menor.
     * <AUTHOR> Maciel
     */
    public static boolean maior(final Date dataQueDeveSerMaior, final Date dataComparada) {
        //inicializar calend�rios, pois a compara��o after e before exige que sejam
        //inst�ncias de Calendar
        Calendar calDataQueDeveSerMaior = Calendario.getInstance();
        calDataQueDeveSerMaior.setTime(getDataComHoraZerada(dataQueDeveSerMaior));

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(getDataComHoraZerada(dataComparada));


        if (calDataQueDeveSerMaior.after(calDataComparada)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * Retorna VERDADEIRO se a data passada como par�metro � igual (no formato
     * dd/MM/yyyy) a data comparada
     *
     * @param <i>dataQueDeveSerIgual</i> data que ser� verifica se � igual a
     * outra.
     * @param dataComparada
     * @return <i>true</i> caso as datas s�o iguais
     * <AUTHOR> Maciel
     */
    public static boolean igual(final Date dataQueDeveSerIgual, final Date dataComparada) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        String d1 = sdf.format(dataQueDeveSerIgual);
        String d2 = sdf.format(dataComparada);
        return d1.equals(d2);
    }

    public static boolean igualComHora(final Date dataQueDeveSerIgual, final Date dataComparada) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        String d1 = sdf.format(dataQueDeveSerIgual);
        String d2 = sdf.format(dataComparada);
        return d1.equals(d2);
    }


    /**
     * Retorna VERDADEIRO se a data passada como par�metro � menor ou igual (no
     * formato dd/MM/yyyy) a data comparada
     *
     * @param <i>dataQueDeveSerMenorOuIgual</i> data que ser� verifica se �
     * menor ou igual a outra.
     * @param dataComparada
     * @return <i>true</i> caso 'dataQueDeveSerMenorOuIgual' seja menor ou igual
     * a 'dataComparada'
     * <AUTHOR> Maciel
     */
    public static boolean menorOuIgual(final Date dataQueDeveSerMenorOuIgual, final Date dataComparada) {
        return Calendario.menor(dataQueDeveSerMenorOuIgual, dataComparada)
                || Calendario.igual(dataQueDeveSerMenorOuIgual, dataComparada);
    }

    /**
     * Retorna VERDADEIRO se a data passada como par�metro � maior ou igual (no
     * formato dd/MM/yyyy) a data comparada
     *
     * @param <i>dataQueDeveSerMaiorOuIgual</i> data que ser� verifica se �
     * maior ou igual a outra.
     * @param dataComparada
     * @return <i>true</i> caso 'dataQueDeveSerMenorOuIgual' seja menor ou igual
     * a 'dataComparada'
     * <AUTHOR> Maciel
     */
    public static boolean maiorOuIgual(final Date dataQueDeveSerMaiorOuIgual, final Date dataComparada) {
        return Calendario.maior(dataQueDeveSerMaiorOuIgual, dataComparada)
                || Calendario.igual(dataQueDeveSerMaiorOuIgual, dataComparada);
    }

    public static boolean horasMenor(final String horaQueDeveSerMenor, final String horaQueDeveSerMaior) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("kk:mm");
        Date d1 = formatter.parse(horaQueDeveSerMenor);
        Date d2 = formatter.parse(horaQueDeveSerMaior);
        return d1.getTime() < d2.getTime();
    }

    /**
     * Retorna a data passada como par�metro com campos de data e hora zerados
     *
     * @param <i>data</i> data que ter� as horas zeradas.
     * @return <i>Date</i>
     * <AUTHOR> Maciel
     */
    public static Date getDataComHoraZerada(Date data) {
        Calendar calendar = Calendario.getInstance();
        calendar.setTime(data);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date getDataComUltimaHora(Date data) {
        Calendar calendar = Calendario.getInstance(data);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    public static Date getDataComMaximoDeMilissegundo(Date data) {
        if (data == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        calendar.add(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * Retorna a data passada como par�metro com campos de data e hora
     * preenchidos com valores passados na string
     *
     * <AUTHOR> 13/07/2011
     */
    public static Date getDataComHora(Date data, String hora) {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(data);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), Integer.valueOf(hora.substring(0, 2)), Integer.valueOf(hora.substring(3, 5)));
        return cal.getTime();

    }

    /**
     * Retorna uma inst�ncia de Calendar j� no locale pt/BR, for�ando os
     * par�metros passados.
     *
     * @param ano
     * @param mes de 1 a 12
     * @param dia de 0 a 31, se tiver certeza que o mes tem 31 dias
     * @param horas de 0 a 23
     * @param minutos de 0 59
     * @param segundos de 0 59
     * @param milisegundos de 0 999
     * @return Calendar
     */
    public static Calendar getInstance(int ano, int mes, int dia,
            int horas, int minutos, int segundos, int milisegundos) {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(Calendario.hoje());

        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes - 1);//de 0-11
        cal.set(Calendar.YEAR, ano);
        cal.set(Calendar.HOUR_OF_DAY, horas);
        cal.set(Calendar.MINUTE, minutos);
        cal.set(Calendar.SECOND, segundos);
        cal.set(Calendar.MILLISECOND, milisegundos);
        return cal;
    }

    /**
     * Retorna uma inst�ncia de Calendar j� no locale pt/BR, for�ando os
     * par�metros passados, zerando campos de hora.
     *
     * @param ano
     * @param mes
     * @param dia
     * @return
     */
    public static Calendar getInstance(int ano, int mes, int dia) {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(Calendario.hoje());

        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes - 1);//de 0-11
        cal.set(Calendar.YEAR, ano);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    public static Calendar getInstance(Date data) {
        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(data);
        return cal;
    }

    /**
     * Similar a Calendar.getInstance, por�m, for�a o locale "pt/BR"
     *
     * @return
     */
    public static Calendar getInstance() {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTime(Calendario.hoje());

        return cal;
    }

    public static Calendar getInstance(final long millis) {

        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTimeInMillis(millis);

        return cal;
    }

    public static Date getInstanceDate(final long millis) {
        Calendar cal = Calendar.getInstance(getDefaultLocale());
        cal.setTimeInMillis(millis);
        return cal.getTime();
    }

    public static Calendar hojeCalendar(TimeZone tz) {
        Calendar mbCal = new GregorianCalendar(tz);
        mbCal.setTimeInMillis(hoje().getTime());

        Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
        cal.set(Calendar.YEAR, mbCal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, mbCal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, mbCal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, mbCal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, mbCal.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, mbCal.get(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND, mbCal.get(Calendar.MILLISECOND));
        return cal;
    }

    public static void validarHoras(String horaInicial, String horaFinal) throws Exception {

        if (horaInicial.length() == 5 & horaFinal.length() == 5) {
            Calendar cal1 = Calendario.getInstance();
            int horasInicio = Integer.parseInt(horaInicial.substring(0, 2));
            int minutosInicio = Integer.parseInt(horaInicial.substring(3, 5));
            cal1.set(Calendar.HOUR_OF_DAY, horasInicio);
            cal1.set(Calendar.MINUTE, minutosInicio);

            Calendar cal2 = Calendario.getInstance();
            int horasFinal = Integer.parseInt(horaFinal.substring(0, 2));
            int minutosFinal = Integer.parseInt(horaFinal.substring(3, 5));
            cal2.set(Calendar.HOUR_OF_DAY, horasFinal);
            cal2.set(Calendar.MINUTE, minutosFinal);
            if ((horasInicio < 00) | (horasInicio > 23) | (minutosInicio < 00) | (minutosInicio > 59)) {
                throw new Exception("A HORA INICIAL deve ser válida");
            }
            if ((horasFinal < 00) | (horasFinal > 23) | (minutosFinal < 00) | (minutosFinal > 59)) {
                throw new Exception("A HORA TÉRMINO deve ser válida");
            }
            if (cal2.before(cal1) | horaInicial.equals(horaFinal)) {
                throw new Exception("A HORA INICIAL deve ser menor que a  HORA TÉRMINO");
            }
        } else {
            throw new Exception("A HORA INICIAL e HORA TÉRMINO do horário de acesso deve estar no formato hh:mm");
        }
    }

    public static boolean validarSeEstaNoIntervaloHoras(String horasInicial1, String horasFinal1, String horasInicial2, String horasFinal2) {
        if (((horasInicial2.compareTo(horasInicial1) >= 0) && (horasInicial2.compareTo(horasFinal1)) <= 0)
                || (horasFinal2.compareTo(horasInicial1) >= 0 && horasFinal2.compareTo(horasFinal1) <= 0)
                || (horasInicial2.compareTo(horasInicial1) < 0 && horasFinal2.compareTo(horasFinal1) > 0)) {
            return true;
        }
        return false;
    }

    /**
     * Calcula quantos dia da semana existe no per�odo informado. Por exemplo:
     * de 28/08/2012 � 27/09/2012 quantas sextas-feiras possui? Use
     * contarDiasDaSemanaEntre(dateIni, dateFim, Calendar.FRIDAY)
     *
     * Obs.: Consdeira-se no total os extremos: dataInicial e datFinal
     *
     * @param dtInicio
     * @param dtFim
     * @param diaDaSemana
     * @return
     */
    public static int contarDiasDaSemanaEntre(final Date dtInicio, final Date dtFim, int diaDaSemana) {
        long dif = Uteis.nrDiasEntreDatas(dtInicio, dtFim);
        Calendar c = Calendario.getInstance();
        c.setTime(dtInicio);
        int cont = 0;
        for (int i = 0; i <= dif; i++) {
            if (c.get(Calendar.DAY_OF_WEEK) == diaDaSemana) {
                cont++;
            }
            c.add(Calendar.DAY_OF_MONTH, 1);
        }
        return cont;
    }

    /**
     * Retorna um Date no padr�o desejado por 'pattern'
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String getData(final Date date, final String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.format(date);
    }

    public static String getHora(final Date date, final String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.format(date);
    }

    /**
     * Retorna a Data Atual no padr�o desejado por 'pattern'
     *
     * @param pattern
     * @return
     */
    public static String getData(final String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.format(Calendario.hoje());
    }

    public static Date getDate(final String pattern, final String data) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return sdf.parse(data);
    }

    public static boolean horaEstaEntreIntervaloHoras(String horaComparacao, String horaInicio, String horaTermino) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        Date dAtual = formatter.parse(horaInicio);
        Date dProx = formatter.parse(horaTermino);
        Date dComp = formatter.parse(horaComparacao);
        return dAtual.getTime() <= dComp.getTime() && dComp.getTime() < dProx.getTime();
    }

    /**
     * Gera o hor�rio em String com o seguinte formato HH:mm
     *
     * @param horaFinal
     * @return
     */
    public static String gerarHorarioFinal(Time horaFinal) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(horaFinal);
        return calendar.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59")
                : "" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59");
    }

    /**
     * Gera o hor�rio em String com o seguinte formato HH:mm
     *
     * @param horaInicial
     * @return
     */
    public static String gerarHorarioInicial(Time horaInicial) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(horaInicial);
        return calendar.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":00")
                : "" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":00");
    }

    public static Date fimDoDia(Date dia) {
        if (dia == null) {
            return null;
        }
        Calendar c = getInstance();
        c.setTime(dia);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    public static Date proximoDiaUtil(final Date dataBase, final int nrDiasUteis) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        c.add(Calendar.DAY_OF_MONTH, nrDiasUteis);
        Date result = c.getTime();
        if (!isDiaUtil(result)) {
            result = proximoDiaUtil(c.getTime(), 1);
        }
        return result;
    }

    public static Date proximoDeveSerUtil(final Date dataBase, final int nrDiasUteis) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        c.add(Calendar.DAY_OF_MONTH, nrDiasUteis);
        Date result = c.getTime();
        while (!isDiaUtil(result)) {
            result = proximoDiaUtil(c.getTime(), 1);
        }        
        return result;
    }
    
    public static Date proximo(final int field, final Date dataBase) {
        Calendar c = getInstance();
        c.setTime((Date) dataBase.clone());
        c.add(field, 1);
        return c.getTime();
    }

    public static Date anterior(final int field, final Date dataBase) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        c.add(field, -1);
        return c.getTime();
    }

    public static Date proximoDiaSemana(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        int diaAtual = c.get(Calendar.DAY_OF_WEEK);
        boolean go = true;
        while (go) {
            c.setTime(proximo(Calendar.DAY_OF_MONTH, c.getTime()));
            go = c.get(Calendar.DAY_OF_WEEK) != diaAtual;
        }
        return c.getTime();
    }

    public static Date proximoDiaSemana(final int nextDayOfWeek, final Date dataBase) {
        Calendar c = getInstance(dataBase);
        boolean go = true;
        while (go) {
            c.setTime(proximo(Calendar.DAY_OF_MONTH, c.getTime()));
            go = c.get(Calendar.DAY_OF_WEEK) != nextDayOfWeek;
        }
        return c.getTime();
    }

    public static Date inicioSemana(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return getDataComHoraZerada(c.getTime());
    }

    public static Date inicioSemanaSegunda(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return getDataComHoraZerada(c.getTime());
    }

    public static Date fimSemana(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        return getDataComHoraZerada(c.getTime());
    }

    public static Date fimSemanaDomingo(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        return proximo(Calendar.DAY_OF_MONTH, getDataComHoraZerada(c.getTime()));
    }

    public static Date inicioMes(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return getDataComHoraZerada(c.getTime());
    }

    public static Date fimMes(final Date dataBase) {
        Calendar c = getInstance(dataBase);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return getDataComHoraZerada(c.getTime());
    }

    public static boolean isDiaUtil(final Date dataBase) {
        Calendar c = getInstance();
        c.setTime(dataBase);
        if ((c.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY)
                || (c.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY)) {
            return false;
        }
        return true;
    }

    public static String semanaAtualSQL(Date dataBase) {
        Calendar cInicio = getInstance(dataBase);

        Calendar cFim = getInstance(dataBase);
        cFim.add(Calendar.WEEK_OF_YEAR, 1);
        cFim.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);

        return String.format("'%s' and '%s' ",
                new Object[]{
            Uteis.getDataFormatoBD(cInicio.getTime()),
            Uteis.getDataFormatoBD(cFim.getTime())});
    }

    public static String periodoSQL(final Date dataInicial, final Date dataFinal) {

        return String.format("'%s' and '%s' ",
                new Object[]{
            Uteis.getDataFormatoBD(dataInicial),
            Uteis.getDataFormatoBD(dataFinal)});
    }
//    public static void main(String... args) throws InterruptedException, Exception {
//        TimeZone tz = TimeZone.getTimeZone(TimeZoneEnum.Brazil_West.getId());
//        Date today = Uteis.getDate("23/08/2011 16:19");
//
//        // Get the display name
//        String shortName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.SHORT);
//        String longName = tz.getDisplayName(tz.inDaylightTime(today), TimeZone.LONG);
//
//
//        // Get the number of hours from GMT
//        int rawOffset = tz.getRawOffset();
//        int hour = rawOffset / (60 * 60 * 1000);
//        int min = Math.abs(rawOffset / (60 * 1000)) % 60;
//
//        // Does the time zone have a daylight savings time period?
//        boolean hasDST = tz.useDaylightTime();
//
//        // Is the time zone currently in a daylight savings time?
//        boolean inDST = tz.inDaylightTime(today);
//
//        System.out.println(longName + ": " + hour + ":" + min);
//        System.out.println("    hasDST: " + hasDST);
//        System.out.println("    inDST: " + inDST);
//
//        System.out.println(getDateInTimeZone(today, tz.getID()));
//
//        Date d1 = Uteis.getDate("29/08/2012");
//        Date d2 = Uteis.getDate("03/09/2012");
//
//        System.out.println(Calendario.contarDiasDaSemanaEntre(d1, d2, Calendar.TUESDAY));
//
//        System.out.println(Calendario.getInstance().get(Calendar.DAY_OF_WEEK));
//
//        Calendar c = Calendario.getInstance(2013, 10, 21);
//        System.out.println(Uteis.obterDataAnterior(c.getTime(), 1));
//        System.out.println(proximoDiaUtil(Calendario.getInstance(2013, 1, 7).getTime(), 5));
//        System.out.println(proximoDiaSemana(Calendario.getInstance(2013, 2, 21).getTime()));
    /*System.out.println("Brazil/Acre - AMT - Amazon Time -> " + getDateInTimeZone(cal.getTime()));
     System.out.println("Brazil/DeNoronha - FNT - Fernando de Noronha Time -> " + getDateInTimeZone(cal.getTime()));
     System.out.println("Brazil/East - BRT - Brasilia Time -> " + getDateInTimeZone(cal.getTime()));
     System.out.println("Brazil/West - AMT - Amazon Time -> " + getDateInTimeZone(cal.getTime()));*/
//    }

    public static void main(String... args) {
        System.out.println(Calendario.fimMes(Calendario.hoje()));
    }

    public static boolean menorComHora(final Date dataHoraQueDeveSerMenor, final Date dataHoraComparada) {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMenor = Calendario.getInstance();
        calDataQueDeveSerMenor.setTime(dataHoraQueDeveSerMenor);

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(dataHoraComparada);

        return calDataQueDeveSerMenor.before(calDataComparada);

    }

    public static boolean maiorComHora(final Date dataHoraQueDeveSerMaior, final Date dataHoraComparada) {
        //inicializar calendários, pois a comparação after e before exige que sejam
        //instâncias de Calendar
        Calendar calDataQueDeveSerMaior = Calendario.getInstance();
        calDataQueDeveSerMaior.setTime(dataHoraQueDeveSerMaior);

        Calendar calDataComparada = Calendario.getInstance();
        calDataComparada.setTime(dataHoraComparada);

        return calDataQueDeveSerMaior.after(calDataComparada);

    }

    public static long diferencaEmMinutos(Date dataInicial, Date dataFinal){
        return  (((dataFinal.getTime() - dataInicial.getTime())/1000)/60);
    }

    public static Date dataTz(Date data, String timeZoneID) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);
        int ano = calendar.get(Calendar.YEAR);
        int mes = calendar.get(Calendar.MONTH);
        int dia = calendar.get(Calendar.DAY_OF_MONTH);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTimeZone(TimeZone.getTimeZone(timeZoneID));
        calendar2.set(ano, mes, dia, 0, 0, 0);
        return calendar2.getTime();
    }


    public static Date getDataComHora(Date data, Integer hora) {
        if (data == null) {
            return null;
        }
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataComHoraZerada(data));
        cal.add(Calendar.HOUR_OF_DAY, hora);
        return cal.getTime();

    }

    public static int getDiaMes(Date data) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    public static Boolean dataNoMesmoDiaMes(Date data1, Date data2) {
        if (Uteis.getMesData(data1) == Uteis.getMesData(data2)) {
            return Calendario.getDiaMes(data1) == Calendario.getDiaMes(data2);
        } else {
            return false;
        }
    }

    public static Long pegaHoraEmMilisegundos(String horaString) {
        final SimpleDateFormat conversorDataHora = new SimpleDateFormat("HH:mm");
        try {
            final Date hora = conversorDataHora.parse(horaString);
            final GregorianCalendar calendarioHorario = new GregorianCalendar();
            calendarioHorario.setTime(hora);

            return calendarioHorario.getTimeInMillis();
        } catch (ParseException ex) {
            Uteis.logar(ex, Calendario.class);
            return null;
        }
    }

    public static Integer getAno(Date data) {
        return Integer.parseInt(Calendario.getData(data, "yyyy"));
    }

    public static Boolean dataNoMesmoMesAno(Date data1 , Date data2) {
        if(Uteis.getAnoData(data1) == Uteis.getAnoData(data2)){
            if(Uteis.getMesData(data1) ==Uteis.getMesData(data2)){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }

    }

    public static Boolean dataNoMesmoMes(Date data1 , Date data2) {
        if (Uteis.getMesData(data1) == Uteis.getMesData(data2)) {
            return true;
        } else {
            return false;
        }

    }

    public static Date somarDias(Date data, int dias) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(data);
        cal.add(5, dias);
        return cal.getTime();
    }

    public static String somaMinutos(Date dateTime, Integer minutos, String formatacao) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dateTime);
        cal.add(Calendar.MINUTE, minutos);

        return Uteis.getDataAplicandoFormatacao(cal.getTime(), formatacao);
    }

}
