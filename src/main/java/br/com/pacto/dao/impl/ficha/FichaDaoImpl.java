/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.impl.ficha;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.serie.SerieTO;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaAjusteWriteJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaWriteJSON;
import br.com.pacto.controller.json.atividade.write.SerieWriteJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteJSON;
import br.com.pacto.controller.json.nivel.NivelWriteJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.ficha.FichaDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.FichaExcecoes;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.hibernate.Transaction;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Repository
public class FichaDaoImpl extends DaoGenericoImpl<Ficha, Integer> implements
        FichaDao {

    @Override
    public List<FichaResponseTO> consultarFichasPreDefinidas(String ctx,
                                                             Integer categoriaId,
                                                             String nomeFicha,
                                                             FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        int maxResults = 100;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? maxResults : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();

        }
        StringBuilder hql = new StringBuilder();
        HashMap<String, Object> params = new HashMap();
        List<FichaResponseTO> listaRet = new ArrayList<>();
        StringBuilder where = new StringBuilder();
        hql.append("SELECT obj FROM Ficha obj ");
        where.append(" where usarcomopredefinida = true ");
        if(filtroFichaPredefinidaJSON != null) {
            if ((filtroFichaPredefinidaJSON.getNome()) && (!filtroFichaPredefinidaJSON.getParametro().trim().equals(""))) {
                where.append(" AND upper(obj.nome) like CONCAT('%',:nome,'%')");
                params.put("nome", filtroFichaPredefinidaJSON.getParametro().toUpperCase());
            }
            if (filtroFichaPredefinidaJSON.getAtivo() != null) {
                where.append(" AND obj.ativo = :ativo)");
                params.put("ativo", filtroFichaPredefinidaJSON.getAtivo());
            }
        }else{
            where.append(" AND obj.ativo = true ");
        }
        if (!UteisValidacao.emptyNumber(categoriaId)) {
            where.append(" AND obj.categoria.codigo = :categoriaId ");
            params.put("categoriaId", categoriaId);
        }
        if (!UteisValidacao.emptyString(nomeFicha) && !nomeFicha.trim().equals("")) {
            where.append(" AND upper(obj.nome) like CONCAT('%',:nome,'%')");
            params.put("nome", nomeFicha.toUpperCase());
        }
        hql.append(where.toString());
        hql.append(paginadorDTO.getSQLOrderByUse());
        List<Ficha> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, params).longValue());
            }
            lista = findByParam(ctx, hql.toString(), params, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHAS, e);
        }
        if (lista != null) {
            try {
                refresh(ctx, lista);
            }catch (Exception e){
                Uteis.logar(e, FichaDaoImpl.class);
            }
            for (Ficha ficha : lista) {
                listaRet.add(new FichaResponseTO(ficha, null));
            }
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return listaRet;
    }

    @Override
    public List<String> obterMensagensRecomendadas(String ctx) throws ServiceException {
        try {
            List<String> ret = new ArrayList<String>();
            //ResultSet rs = createStatement(ctx, "select distinct upper(mensagemaluno) from ficha where coalesce(mensagemaluno, '') <> '' order by 1");
            StringBuilder sb = new StringBuilder();
            sb.append("select distinct trim(upper(translate(mensagemaluno,'áàãâäÁÀÃÂÄéèêëÉÈÊËíìîïÍÌÎÏóòõôöÓÒÕÔÖúùûüÚÙÛÜñÑçÇÿýÝ','aaaaaAAAAAeeeeEEEEiiiiIIIIoooooOOOOOuuuuUUUUnNcCyyY'))) msg ")
                    .append("from ficha where length(coalesce(mensagemaluno, '')) > 1 order by 1");
            try (ResultSet rs = createStatement(ctx, sb.toString())) {
                while (rs.next()) {
                    ret.add(rs.getString("msg"));
                }
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_MSG_RECOMENDADAS, e);
        }
    }

    public FichaWriteJSON consultarFichaPorIdApp(String ctx, Integer id) throws ServiceException {
        StringBuilder sbFicha = new StringBuilder();

        sbFicha.append("SELECT nome, mensagemaluno, categoria_codigo, usarcomopredefinida," +
                " versao, ativo, sexo, nivel_codigo FROM Ficha where codigo = ");
        sbFicha.append(id);

        FichaWriteJSON ficha = new FichaWriteJSON();
        try {
            try (ResultSet resultSet = createStatement(ctx, sbFicha.toString())) {
                while (resultSet.next()) {

                    ficha.setCodigo(id);
                    ficha.setNome(resultSet.getString("nome"));
                    ficha.setMensagemAluno(resultSet.getString("mensagemaluno"));
                    Integer codigoCategoriaFicha = resultSet.getInt("categoria_codigo");
                    ficha.setCategoria(codigoCategoriaFicha);
                    ficha.setUsarComoPredefinida(resultSet.getBoolean("usarcomopredefinida"));
                    ficha.setVersao(resultSet.getInt("versao"));
                    ficha.setAtivo(resultSet.getBoolean("ativo"));
                    StringBuilder sbCategoria = new StringBuilder();
                    sbCategoria.append("SELECT nome FROM categoriaficha where codigo = ");
                    if (codigoCategoriaFicha > 0) {
                        sbCategoria.append(codigoCategoriaFicha);
                        try (ResultSet resultSetCategoria = createStatement(ctx, sbCategoria.toString())) {
                            resultSetCategoria.next();
                            ficha.setCategoriaFicha(resultSetCategoria.getString("nome"));
                        }
                    }
                    Integer sexo = resultSet.getInt("sexo");
                    if (sexo == null || sexo == 0) {
                        ficha.setSexo(SexoEnum.N);
                    } else {
                        ficha.setSexo(SexoEnum.getFromId(sexo));
                    }
                    Integer nivelCod = resultSet.getInt("nivel_codigo");
                    StringBuilder sbNivel = new StringBuilder();
                    sbNivel.append("SELECT nome, ordem FROM Nivel where codigo = ");
                    sbNivel.append(nivelCod);
                    try (ResultSet rsNivel = createStatement(ctx, sbNivel.toString())) {
                        while (rsNivel.next()) {
                            NivelWriteJSON nivel = new NivelWriteJSON();
                            nivel.setCodigo(nivelCod);
                            nivel.setNome(rsNivel.getString("nome"));
                            nivel.setOrdem(rsNivel.getInt("ordem"));
                            ficha.setNivel(nivel);
                        }
                    }
                /* Get atividadeficha*/
                String sbatividade = "SELECT codigo, nome, ordem, atividade_codigo, metodoexecucao, setid " +
                        "FROM Atividadeficha where ficha_codigo = " +
                        id + " order by ordem";
                List<AtividadeFichaWriteJSON> atividades = new ArrayList<AtividadeFichaWriteJSON>();
                while (resultSet.next()) {
                    AtividadeFichaWriteJSON atividadeFicha = new AtividadeFichaWriteJSON();

                        Integer atividadeFichaCodigo = resultSet.getInt("codigo");
                        atividadeFicha.setCodigo(atividadeFichaCodigo);
                        atividadeFicha.setFicha(id);
                        Integer codigoAtividade = resultSet.getInt("atividade_codigo");
                        atividadeFicha.setAtividade(codigoAtividade);
                        atividadeFicha.setNomeAtividade(resultSet.getString("nome"));
                        atividadeFicha.setOrdem(resultSet.getInt("ordem"));
                        atividadeFicha.setMetodoExecucao(resultSet.getObject("metodoexecucao") != null ?
                                Objects.requireNonNull(MetodoExecucaoEnum.getFromOrdinal(resultSet.getInt("metodoexecucao"))).getId() : null);
                        atividadeFicha.setSetid(resultSet.getString("setid"));

                        StringBuilder sbAtividade = new StringBuilder();
                        sbAtividade.append("SELECT tipo FROM atividade where codigo = ");
                        sbAtividade.append(codigoAtividade);
                        try (ResultSet rsAtividade = createStatement(ctx, sbAtividade.toString())) {
                            while (rsAtividade.next()) {
                                atividadeFicha.setTipoAtividade(rsAtividade.getInt("tipo"));
                            }
                        }

                        List<SerieWriteJSON> series = new ArrayList<SerieWriteJSON>();
                        String sbserie = "SELECT codigo, cadencia, repeticao, repeticaocomp, carga, cargacomp, " +
                                "duracao, distancia, velocidade,complemento, descanso, ordem, atualizadoapp, cargaapp, repeticaoapp " +
                                "FROM Serie where atividadeficha_codigo = " +
                                atividadeFichaCodigo + " order by ordem";
                        try (ResultSet resultSetSerie = createStatement(ctx, sbserie)) {
                            while (resultSetSerie.next()) {
                                SerieWriteJSON serie = new SerieWriteJSON();
                                serie.setCodigo(resultSetSerie.getInt("codigo"));
                                serie.setAtividadeFicha(atividadeFichaCodigo);
                                serie.setCadencia(resultSetSerie.getString("cadencia"));
                                serie.setRepeticao(resultSetSerie.getInt("repeticao"));
                                serie.setRepeticaoComp(resultSetSerie.getString("repeticaocomp"));
                                serie.setCarga(resultSetSerie.getDouble("carga"));
                                serie.setCargaComp(resultSetSerie.getString("cargacomp"));
                                serie.setDuracao(resultSetSerie.getInt("duracao"));
                                serie.setDistancia(resultSetSerie.getInt("distancia"));
                                serie.setVelocidade(resultSetSerie.getDouble("velocidade"));
                                serie.setComplemento(resultSetSerie.getString("complemento"));
                                serie.setDescanso(resultSetSerie.getInt("descanso"));
                                serie.setOrdem(resultSetSerie.getInt("ordem"));
                                boolean atualizadoCargaApp = resultSetSerie.getBoolean("atualizadoapp");
                                if (atualizadoCargaApp) {
                                    serie.setCargaApp(resultSetSerie.getString("cargaapp"));
                                    serie.setRepeticaoApp(resultSetSerie.getString("repeticaoapp"));
                                    if (validarAtualizarNovamente(serie.getCargaApp(), serie.getRepeticaoApp(), serie.getCarga(), serie.getRepeticao())) {
                                        atualizarCargaApp(serie, ctx);
                                    }
                                } else {
                                    atualizarCargaApp(serie, ctx);
                                }

                                series.add(serie);
                            }
                        }

                        StringBuilder sbAjustesAtividade = new StringBuilder();
                        sbAjustesAtividade.append("select distinct aparelhoajuste.nome from aparelhoajuste inner join atividadeaparelho on aparelhoajuste.aparelho_codigo = atividadeaparelho.aparelho_codigo \n" +
                                "where atividadeaparelho.atividade_codigo = " + codigoAtividade);
                        List<String> ajustesAtividade;
                        try (ResultSet rsAjustesAtividade = createStatement(ctx, sbAjustesAtividade.toString())) {
                            ajustesAtividade = new ArrayList<>();
                            while (rsAjustesAtividade.next()) {
                                ajustesAtividade.add(rsAjustesAtividade.getString("nome"));
                            }
                        }

                        StringBuilder sbAjustes = new StringBuilder();
                        sbAjustes.append("SELECT codigo, nome, valor FROM atividadefichaajuste " +
                                "where atividadeficha_codigo = ");
                        sbAjustes.append(atividadeFichaCodigo);
                        List<AtividadeFichaAjusteWriteJSON> ajustes;
                        try (ResultSet rsAjustes = createStatement(ctx, sbAjustes.toString())) {
                            ajustes = new ArrayList<AtividadeFichaAjusteWriteJSON>();
                            while (rsAjustes.next()) {
                                AtividadeFichaAjusteWriteJSON ajuste = new AtividadeFichaAjusteWriteJSON();
                                ajuste.setAtividadeFicha(atividadeFichaCodigo);
                                ajuste.setCodigo(rsAjustes.getInt("codigo"));
                                ajuste.setNome(rsAjustes.getString("nome"));
                                ajuste.setValor(rsAjustes.getString("valor"));
                                ajustes.add(ajuste);

                                ajustesAtividade.remove(ajuste.getNome());
                            }
                        }
                        for (String ajustePredefinido : ajustesAtividade) {
                            AtividadeFichaAjusteWriteJSON ajuste = new AtividadeFichaAjusteWriteJSON();
                            ajuste.setAtividadeFicha(atividadeFichaCodigo);
                            ajuste.setNome(ajustePredefinido);
                            ajuste.setValor("");
                            ajustes.add(ajuste);
                        }
                        atividadeFicha.setAjustes(ajustes);
                        atividadeFicha.setSeries(series);
                        atividades.add(atividadeFicha);
                    }
                    ficha.setAtividadesFicha(atividades);
                    /* Get programatreinoficha*/
                    StringBuilder sbProgramaTreinoFicha = new StringBuilder();
                    sbProgramaTreinoFicha.append("SELECT codigo, tipoexecucao, programa_codigo " +
                            "FROM Programatreinoficha where ficha_codigo = ");
                    sbProgramaTreinoFicha.append(id);
                    List<ProgramaTreinoFichaJSON> programatreinojsons;
                    try (ResultSet rsProgramaFicha = createStatement(ctx, sbProgramaTreinoFicha.toString())) {
                        programatreinojsons = new ArrayList<ProgramaTreinoFichaJSON>();
                        while (rsProgramaFicha.next()) {
                            ProgramaTreinoFichaJSON programaFichaJson = new ProgramaTreinoFichaJSON();
                            programaFichaJson.setCodigo(rsProgramaFicha.getInt("codigo"));
                            programaFichaJson.setTipoExecucao(rsProgramaFicha.getInt("tipoexecucao"));
                            programaFichaJson.setFicha(id);
                            programaFichaJson.setPrograma(rsProgramaFicha.getInt("programa_codigo"));
                            programaFichaJson.setNomeFicha(ficha.getNome());
                            programatreinojsons.add(programaFichaJson);
                        }
                    }

                    for (ProgramaTreinoFichaJSON ptfj : programatreinojsons) {
                        if (ptfj.getTipoExecucao() == 1) {
                            StringBuilder sbProgramaTreinoFicha_diaSemana = new StringBuilder();
                            sbProgramaTreinoFicha_diaSemana.append("SELECT diasemana FROM programatreinoficha_diasemana WHERE programatreinoficha_codigo = ").append(ptfj.getCodigo());
                            List<String> diaSemana;
                            try (ResultSet rsDiaSemana = createStatement(ctx, sbProgramaTreinoFicha_diaSemana.toString())) {
                                diaSemana = new ArrayList();
                                while (rsDiaSemana.next()) {
                                    diaSemana.add(rsDiaSemana.getString("diasemana"));
                                }
                            }
                            ptfj.setDiaSemana(diaSemana);
                        }
                    }

                    ficha.setProgramasFicha(programatreinojsons);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHAS, e);
        }
        return ficha;
    }

    public static boolean validarAtualizarNovamente(String cargaApp, String repeticaoApp, Double carga, Integer repeticao) {
        if (cargaApp == null || repeticaoApp == null
        || (cargaApp.equals("") && !UteisValidacao.emptyNumber(carga))
        || (repeticaoApp.equals("") && !UteisValidacao.emptyNumber(repeticao) )){
            return true;
        }
        return cargaApp.startsWith(" -") || repeticaoApp.startsWith(" -");
    }

    public static void atualizarCargaApp(SerieWriteJSON serie, String ctx) {
        String[] cargaComps = serie.getCargaComp().split("\\s");
        String carga = String.valueOf(serie.getCarga().intValue());
        if (cargaComps.length == 0){
            serie.setCargaApp(serie.getCarga() == 0 ? "" : carga);
        } else if (carga.equals("0") && serie.getCargaComp().trim().isEmpty()){
            serie.setCargaApp("");
        } else if (!carga.equals(serie.getCargaComp()) && !carga.equals(cargaComps[0])){
            serie.setCargaApp(serie.getCarga() == 0 ? serie.getCargaComp() : carga + " - " + serie.getCargaComp());
        } else {
            serie.setCargaApp(serie.getCargaComp());
        }

        String[] repeticaoComps = serie.getRepeticaoComp().split("\\s");
        String repeticao = String.valueOf(serie.getRepeticao().intValue());
        if (repeticaoComps.length == 0){
            serie.setRepeticaoApp(serie.getRepeticao() == 0 ? "" : carga);
        } else if (repeticao.equals("0") && serie.getRepeticaoComp().trim().isEmpty()){
            serie.setRepeticaoApp("");
        } else if (!repeticao.equals(serie.getRepeticaoComp()) && !repeticao.equals(repeticaoComps[0])){
            serie.setRepeticaoApp(serie.getRepeticao() == 0 ? serie.getRepeticaoComp() : repeticao + " - " + serie.getRepeticaoComp());
        } else {
            serie.setRepeticaoApp(serie.getRepeticaoComp());
        }
        SerieDao serieDao = UtilContext.getBean(SerieDao.class);
        try {
            serieDao.executeNativeSQL(ctx, "UPDATE public.serie SET repeticaoapp='" + serie.getRepeticaoApp() +
                    "',cargaapp='" + serie.getCargaApp() +
                    "',atualizadoapp='1' WHERE codigo=" + serie.getCodigo());
        } catch (Exception ignored) {
        }
    }

    public static void atualizarCargaApp(SerieTO serie, String ctx) {
        String[] cargaComps = serie.getCargaComp().split("\\s");
        String carga = String.valueOf(serie.getCarga().intValue());
        if (cargaComps.length == 0){
            serie.setCargaApp(serie.getCarga() == 0 ? "" : carga);
        } else if (carga.equals("0") && serie.getCargaComp().trim().isEmpty()){
            serie.setCargaApp("");
        } else if (!carga.equals(serie.getCargaComp()) && !carga.equals(cargaComps[0])){
            serie.setCargaApp(serie.getCarga() == 0 ? serie.getCargaComp() : carga + " - " + serie.getCargaComp());
        } else {
            serie.setCargaApp(serie.getCargaComp());
        }

        String[] repeticaoComps = serie.getRepeticaoComp().split("\\s");
        String repeticao = String.valueOf(serie.getRepeticao().intValue());
        if (repeticaoComps.length == 0){
            serie.setRepeticaoApp(serie.getRepeticao() == 0 ? "" : carga);
        } else if (repeticao.equals("0") && serie.getRepeticaoComp().trim().isEmpty()){
            serie.setRepeticaoApp("");
        } else if (!repeticao.equals(serie.getRepeticaoComp()) && !repeticao.equals(repeticaoComps[0])){
            serie.setRepeticaoApp(serie.getRepeticao() == 0 ? serie.getRepeticaoComp() : repeticao + " - " + serie.getRepeticaoComp());
        } else {
            serie.setRepeticaoApp(serie.getRepeticaoComp());
        }
        SerieDao serieDao = UtilContext.getBean(SerieDao.class);
        try {
            serieDao.executeNativeSQL(ctx, "UPDATE public.serie SET repeticaoapp='" + serie.getRepeticaoApp() +
                    "',cargaapp='" + serie.getCargaApp() +
                    "',atualizadoapp='1' WHERE codigo=" + serie.getCodigo());
        } catch (Exception ignored) {
        }
    }

    @Override
    public Ficha obterPorCodigo(String ctx, Integer codigo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM Ficha WHERE codigo = ").append(codigo);

            Ficha ficha;
            try (ResultSet rs = createStatement(ctx, sql.toString())) {
                ficha = new Ficha();

                while (rs.next()) {
                    ficha.setCodigo(rs.getInt("codigo"));
                    ficha.setNome(rs.getString("nome"));
                    ficha.setVersao(rs.getInt("versao"));
                    if (!UteisValidacao.emptyNumber(rs.getInt("categoria_codigo"))) {
                        ficha.setCategoria(new CategoriaFicha(rs.getInt("categoria_codigo")));
                    }
                    AtividadeFichaDao atividadeFichaDao = (UtilContext.getBean(AtividadeFichaDao.class));
                    ficha.setAtividades(atividadeFichaDao.obterPorFicha(ctx, ficha.getCodigo()));
                    ProgramaTreinoFichaDao programaTreinoFichaDao = (UtilContext.getBean(ProgramaTreinoFichaDao.class));
                    ficha.setProgramas(programaTreinoFichaDao.obterPorFicha(ctx, ficha.getCodigo()));
                }
            }

            return ficha;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_BUSCAR_FICHA, ex);
        }
    }

    @Override
    public Ficha atualizarAlgunsCampos(String ctx, Ficha ficha) throws ServiceException {
        try {
            ficha = update(ctx, ficha);
            ProgramaTreinoFichaDao programaTreinoFichaDao = (UtilContext.getBean(ProgramaTreinoFichaDao.class));
            programaTreinoFichaDao.atualizarLista(ctx, ficha.getProgramas());
            return ficha;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_ALTERAR_FICHA, ex);
        }
    }

    @Override
    public Ficha inserir(String ctx, Ficha ficha) throws ServiceException {
        try {
            ficha = insert(ctx, ficha);
            ProgramaTreinoFichaDao programaTreinoFichaDao = (UtilContext.getBean(ProgramaTreinoFichaDao.class));
            programaTreinoFichaDao.atualizarLista(ctx, ficha.getProgramas());
            return ficha;
        } catch (Exception ex) {
            throw new ServiceException(FichaExcecoes.ERRO_INCLUIR_FICHA, ex);
        }
    }

    @Override
    public Integer obterCodigoProgramaFichaNativo(final String ctx, final Integer ficha) throws Exception {
        String sql = "SELECT ficha_codigo FROM programatreinoficha " +
                "WHERE ficha_codigo = " +
                ficha;
        try (ResultSet rs = createStatement(ctx, sql)) {
            if (rs.next()) {
                return rs.getInt("ficha_codigo");
            }
        }
        return null;
    }

}
