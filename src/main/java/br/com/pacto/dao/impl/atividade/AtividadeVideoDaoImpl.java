/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.AtividadeVideo;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeVideoDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeVideoDaoImpl extends DaoGenericoImpl<AtividadeVideo, Integer> implements
        AtividadeVideoDao {
    @Override
    public List<AtividadeVideo> obterLinkVideos(String ctx, Integer codigoAtividade) throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("atv", codigoAtividade);
        try {
            return findByParam(ctx, "select obj from AtividadeVideo obj where obj.atividade.codigo = :atv",
                    params);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
