/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface FichaDao extends DaoGenerico<Ficha, Integer> {

    List<FichaResponseTO> consultarFichasPreDefinidas(final String ctx,
                                                      Integer categoriaId,
                                                      String nomeFicha,
                                                      FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON, PaginadorDTO paginadorDTO) throws Exception;

    List<String> obterMensagensRecomendadas(String ctx) throws ServiceException;

    FichaWriteJSON consultarFichaPorIdApp(String ctx, Integer id) throws ServiceException;

    Ficha obterPorCodigo(String ctx, Integer codigo) throws ServiceException;

    Ficha atualizarAlgunsCampos(String ctx, Ficha ficha) throws Exception;

    Ficha inserir(String ctx, Ficha ficha) throws ServiceException;

    Integer obterCodigoProgramaFichaNativo(final String ctx, final Integer ficha) throws Exception;

}
