package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.agendamento.AlunoVinculoAulaEnum;
import br.com.pacto.controller.json.base.SuperJSON;

import java.util.ArrayList;
import java.util.List;

public class FilaDeEsperaDTO extends SuperJSON {

    private Integer codigoHorarioTurma;
    private String dia;
    private Integer codigoAluno;
    private Integer passivo;
    private Integer matricula;
    private String nomeAluno;
    private String situacaoAluno;
    private String situacaoContrato;
    private Integer codigoFila;
    private Integer ordem;
    private String vinculoComAula;
    private Integer codigoUsuario;
    private Integer origemSistema;

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Integer codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }
    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Integer getCodigoFila() {
        return codigoFila;
    }

    public void setCodigoFila(Integer codigoFila) {
        this.codigoFila = codigoFila;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(String vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getPassivo() {
        return passivo;
    }

    public void setPassivo(Integer passivo) {
        this.passivo = passivo;
    }
}
