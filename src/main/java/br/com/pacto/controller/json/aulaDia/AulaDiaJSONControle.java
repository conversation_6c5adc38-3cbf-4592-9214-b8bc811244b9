/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.crossfit.CrossfitJSONControle;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.dao.intf.aula.AulaDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.agenda.AulasColetivasModoBDServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.UsuarioExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.notificacao.NotificacaoAulaAgendadaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/aula")
public class AulaDiaJSONControle extends SuperControle {

    private final AulaService aulaService;
    private final ClienteSinteticoService clienteService;
    private final AgendaTotalService agendaService;
    private final ConfiguracaoSistemaService configService;
    private final EmpresaService empService;
    private final ProfessorSinteticoService profService;
    private final AgendaTotalService agendaTotalService;
    private final UsuarioService usuarioService;
    private final AgendaService agendaNovaService;
    private final PerfilService perfilService;
    private final AulasColetivasModoBDServiceImpl modoBDService;


    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private UsuarioService us;
    @Autowired
    private AulaDao aulaDao;
    @Autowired
    private NotificacaoService service;
    @Autowired
    private NotificacaoAulaAgendadaService notificacaoAulaAgendadaService;

    @Autowired
    public AulaDiaJSONControle(AulaService aulaService, ClienteSinteticoService clienteService,
                               AgendaTotalService agendaService, ConfiguracaoSistemaService configService,
                               EmpresaService empService, ProfessorSinteticoService profService,
                               AgendaTotalService agendaTotalService, UsuarioService usuarioService,
                               AgendaService ans,
                               PerfilService perfilService,
                               AulasColetivasModoBDServiceImpl modoBDService) {
        this.aulaService = aulaService;
        this.clienteService = clienteService;
        this.agendaService = agendaService;
        this.configService = configService;
        this.empService = empService;
        this.profService = profService;
        this.agendaTotalService = agendaTotalService;
        this.usuarioService = usuarioService;
        this.agendaNovaService = ans;
        this.perfilService = perfilService;
        this.modoBDService = modoBDService;
    }

    @RequestMapping(value = "{ctx}/consultarAulas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulas(@PathVariable String ctx,
            @RequestParam final Integer empresa,
            @RequestParam final String dia,
            @RequestParam(required = false) final String contrato,
            @RequestParam(required = false) final String matricula,
            @RequestParam(required = false) final String chaveOrigem) {
        ModelMap mm = new ModelMap();
        String modo = Aplicacao.getProp(Aplicacao.modoConsultaAgenda);
        if(modo.equals("webservice") || SuperControle.independente(ctx)){
            modoWS(ctx, empresa, dia, contrato, matricula, chaveOrigem, mm);
        } else {
            modoBDService.modoBD(ctx, empresa, dia, contrato, matricula, chaveOrigem, mm);
        }

        return mm;
    }

    private void modoWS(String ctx, Integer empresa, String dia, String contrato, String matricula, String chaveOrigem, ModelMap mm) {
        try {
            Integer contratoInt;
            try {
                contratoInt = Integer.valueOf(contrato);
            }catch (Exception e){
                contratoInt = null;
            }
            boolean outraUnidade = !UteisValidacao.emptyString(chaveOrigem) && !ctx.equals(chaveOrigem);
            List<String> identificadoresAlunoNaAula = new ArrayList<String>();
            //caso o aluno seja informado, buscar quais as proximas aulas marcadas dele
            if(!UteisValidacao.emptyString(matricula) && !outraUnidade){
                List<AgendaTotalJSON> aulasMarcadas = aulaService.obterAulasAluno(ctx, Integer.valueOf(matricula));
                //guardar na lista de proximas aulas
                for(AgendaTotalJSON a : aulasMarcadas){
                    identificadoresAlunoNaAula.add(a.getCodDia());
                }
            } else if(!UteisValidacao.emptyString(matricula) && outraUnidade){
                identificadoresAlunoNaAula.addAll(agendaTotalService.aulasAutorizado(ctx, Integer.valueOf(matricula), Calendario.getDate(Calendario.MASC_DATA, dia), chaveOrigem));
            }
            String timeZone = obterFusoHorarioEmpresa(ctx, empresa);

            ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_MOSTRAR_TOTEM);
            cfg.setValor(cfg.getValor().isEmpty() || cfg.getValor().equals("") ? "43200" : cfg.getValor());
            Date diaInicio = Calendario.getDate(Calendario.MASC_DATA, dia);
            Date diaLimite = Uteis.somarCampoData(Calendario.hoje(timeZone), Calendar.MINUTE, cfg.getValorAsInteger());

            Map<Integer, List<Date>> mapaAulasExcluidas = agendaService.obterAulasExcluidas(ctx, diaInicio, diaLimite);

            Map<Integer, String> mapaProfessores = agendaService.obterMapaProfessores(ctx, empresa, Boolean.FALSE, independente(ctx));
            Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = agendaService.obterProfessoresSubstituidos(ctx, null, diaInicio, diaLimite);

            List<AgendaTotalJSON> aulas = agendaService.obterAulasColetivas(ctx, diaInicio, diaInicio, empresa, independente(ctx));
            List<Modalidade> listaModalidades = agendaService.todasModalidades(ctx);
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();

            ConfiguracaoSistema cfgACNM = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_AULA_COLETIVA_NAO_PERTENCE_MODALIDADE);
            if(cfgACNM.getValorAsBoolean() && isNotBlank(matricula)) {
                ClienteSintetico cliente = clienteService.consultarAtivoPorMatricula(ctx, Integer.valueOf(matricula));
                String modalidades = clienteService.consultaModalidades(ctx, matricula);
                String[] modas = modalidades.split("\\|");
                try {
                    //TODO: MIGRAR ESSA INFO DE MODALIDADES DO TITULAR PRO CLIENTESINTETICO
                    if((modas.length == 0 || (modas.length == 1 && modas[0].equals("")))  && !cliente.getSituacao().equals("AT")){
                        String modalidadesTitular = agendaService.modalidadesTitular(ctx, Integer.valueOf(matricula));
                        modas = modalidadesTitular.split("\\|");
                    }
                }catch (Exception e){
                    Uteis.logar(e, AulaDiaJSONControle.class);
                }
                List<AgendaTotalJSON> aulas2 = new ArrayList<>(aulas);
                for (AgendaTotalJSON aula : aulas2) {
                    AgendaTotalTO agenda = new AgendaTotalTO(aula);
                    if (agenda.getAulaColetiva() && !aula.isNaoValidarModalidadeContrato() &&
                            Arrays.stream(modas).noneMatch(moda -> (isNotBlank(moda) && Integer.valueOf(moda).equals(agenda.getCodigotipo())))) {
                        aulas.remove(aula);
                    }
                }
            }

            agendaNovaService.verificarBloqueados(ctx,
                    diaInicio.getTime(),
                    diaInicio.getTime(),
                    empresa,
                    aulas);
            List<Integer> modalidadeFiltrar = new ArrayList<>();
            if(!UteisValidacao.emptyNumber(contratoInt)){
                modalidadeFiltrar = agendaService.modalidadesContrato(ctx, contratoInt, Integer.valueOf(matricula));
            }
            for (AgendaTotalJSON aula : aulas) {
                AgendaTotalTO agenda = new AgendaTotalTO(aula);
                if(!UteisValidacao.emptyNumber(contratoInt) && !modalidadeFiltrar.contains(aula.getCodigoTipo())){
                    continue;
                }
                for(Modalidade modalidade : listaModalidades) {
                    if (modalidade.getCodigoZW() != null && modalidade.getCodigoZW().equals(agenda.getCodigotipo())) {
                        agenda.setCor(modalidade.getCor().getCor());
                        aula.setCor(modalidade.getCor().getCor());
                        agenda.setTextoStyle(modalidade.getCor().getClasseCor());
                        aula.setTextoCor(modalidade.getCor().getClasseCor());
                        break;
                    }
                }
                List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
                if(datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate()))){
                    continue;
                }

                Map<Date, ProfessorSubstituido> mapaSubstitutos = mapaProfessoresSubstituidos.get(Integer.valueOf(agenda.getId()));
                if(mapaSubstitutos != null){
                    ProfessorSubstituido subs = mapaSubstitutos.get(Calendario.getDataComHoraZerada(agenda.getStartDate()));
                    if(subs != null){
                        Integer codigoProfessorSubstituto = subs.getCodigoProfessorSubstituto();
                        Usuario user = usuarioService.consultarProColaborador(ctx, Integer.valueOf(codigoProfessorSubstituto));
                        aula.setResponsavel(mapaProfessores.get(codigoProfessorSubstituto));
                        aula.setCodigoResponsavel(subs.getCodigoProfessorSubstituto());
                        aula.setFotoProfessor(mapaProfessores.get(-codigoProfessorSubstituto));
                        if (user != null && user.getFotoKeyApp() != null && !user.getFotoKeyApp().equals(""))
                            aula.setFotoProfessor(user.getFotoKeyApp());
                    }
                }

                Date inicio = Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm");
                Date inicioTolerancia = inicio;
                if (!UteisValidacao.emptyNumber(aula.getTipoTolerancia())) {
                    inicioTolerancia = aula.getTipoTolerancia().equals(TipoToleranciaAulaEnum.APOS_INICIO.getCodigo()) ?
                            Uteis.somarCampoData(inicio, Calendar.MINUTE, aula.getTolerancia()) :
                            Uteis.somarCampoData(inicio, Calendar.MINUTE, -aula.getTolerancia());
                }
                if(((inicioTolerancia.after(Calendario.hoje(timeZone)) && inicio.before(Calendario.hoje(timeZone)))
                        || inicio.before(diaLimite)) && inicioTolerancia.after(Calendario.hoje(timeZone))){
                    AulaDiaJSON aulaDiaJSON = new AulaDiaJSON(aula);
                    aulaDiaJSON.setAlunoEstaNaAula(identificadoresAlunoNaAula.contains(aulaDiaJSON.getCodDia()));
                    Empresa empresaLogada = empresaDao.findObjectByAttribute(ctx,"codzw", aula.getEmpresa() == null ? empresa : aula.getEmpresa());
                    aulaDiaJSON.setNomeEmpresa(empresaLogada == null ? "" : empresaLogada.getNome());
                    jsonArray.add(aulaDiaJSON);
                }


            }

            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }



    @RequestMapping(value = "{ctx}/consultarAulasDoProfessor", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulas(@PathVariable String ctx,
            @RequestParam final Integer empresa,
            @RequestParam final Integer codigo,
            @RequestParam final String dia) {
        ModelMap mm = new ModelMap();
        try {
            String timeZone = obterFusoHorarioEmpresa(ctx, empresa);

            ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_MOSTRAR_TOTEM);

            Date diaInicio = Calendario.getDate(Calendario.MASC_DATA, dia);
            Date diaLimite = Uteis.somarCampoData(Calendario.hoje(timeZone), Calendar.MINUTE, cfg.getValorAsInteger());

            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();

            ProfessorSintetico prof = profService.obterPorId(ctx, codigo);
            if (prof != null) {
                List<AgendaTotalJSON> aulas = agendaService.obterAulasDoProfessor(ctx, diaInicio, diaInicio, empresa, prof.getCodigoColaborador());
                agendaNovaService.verificarBloqueados(ctx,
                        diaInicio.getTime(),
                        diaInicio.getTime(),
                        empresa,
                        aulas);
                for (AgendaTotalJSON aula : aulas) {
                    Date inicio = Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm");
                    Date inicioTolerancia = Uteis.somarCampoData(inicio, Calendar.MINUTE, aula.getTolerancia());
                    if(((inicioTolerancia.after(Calendario.hoje(timeZone)) && inicio.before(Calendario.hoje(timeZone)))
                            || inicio.before(diaLimite)) && inicioTolerancia.after(Calendario.hoje(timeZone))){
                        jsonArray.add(new AulaDiaJSON(aula));
                    }
                }
            }

            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);


        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/alunosAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alunosAula(@PathVariable String ctx,
                           @RequestParam(required = false) final Integer empresa,
                           @RequestParam final Integer codigoHorarioTurma,
                           @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            if (empresa == null){
                throw new Exception("Não foi possível consultar as suas aulas. Código da empresa não informado. Por favor, atualize o APP!");
            }
            List<Map<String, String>> alunosAula = agendaService.fotosAlunosAula(
                    ctx, empresa, codigoHorarioTurma, data);
            mm.addAttribute("alunos", alunosAula);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/uy3EsurzFyvFRXlyiKPQqmtTcd9znch4", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alunosAulaCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            Integer empresa = o.has("empresa") && !o.isNull("empresa") ? o.optInt("empresa") : null;
            Integer codigoHorarioTurma = o.optInt("codigoHorarioTurma");
            String data = o.optString("data");

            if (empresa == null){
                throw new Exception("Não foi possível consultar as suas aulas. Código da empresa não informado. Por favor, atualize o APP!");
            }
            List<Map<String, String>> alunosAula = agendaService.fotosAlunosAula(
                    ctx, empresa, codigoHorarioTurma, data);
            mm.addAttribute("alunos", alunosAula);

            // Criptografar toda a resposta de sucesso
            JSONObject responseJson = new JSONObject();
            for (String key : mm.keySet()) {
                responseJson.put(key, mm.get(key));
            }
            mm.clear();
            mm.addAttribute(RETURN, Uteis.encryptUserData(responseJson.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarAulasProfessorDia", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasProfessorDia(@PathVariable String ctx,
            @RequestParam final Integer empresa,
            @RequestParam final String dia,
            @RequestParam final String professor) {
        ModelMap mm = new ModelMap();
        try {
            Date diaInicio = Calendario.getDate(Calendario.MASC_DATA, dia);
            List<AgendaTotalJSON> aulas = agendaService.obterAulas(ctx, diaInicio, diaInicio, empresa);
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            for (AgendaTotalJSON aula : aulas) {
                if(aula.getResponsavel().toLowerCase().contains(professor.toLowerCase())){
                    jsonArray.add(new AulaDiaJSON(aula));
                }
            }
            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/inserirNaFila", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap inserirNaFila(@PathVariable String ctx,
                                        @RequestParam final Integer codigoHorarioTurma,
                                        @RequestParam final String dia,
                                        @RequestParam final Integer codigoAluno) throws ServiceException {
        ModelMap mm = new ModelMap();

        Date today = new Date();
        Date tomorrow = new Date(today.getTime() + (1000 * 60 * 60 * 24));
        String tomorrowStr = null;

        SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");
        formatter = new SimpleDateFormat("dd/MM/yyyy");
        tomorrowStr = formatter.format(tomorrow);

        String retorno = "";
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfg = css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);

        try {
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            retorno = agendaService.inserirNaFilaDeEspera(ctx,codigoHorarioTurma, dia, codigoAluno);
            if(retorno.contains("Erro")){
                    return mm.addAttribute(STATUS_ERRO, retorno);
            } else {
                mm.addAttribute("Fila", retorno);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/mensagemAoEntrarNaAula", method = RequestMethod.POST)
    public @ResponseBody
    void mensagemAoEntrarNaAula(@PathVariable String ctx,
                       @RequestBody final String json) {
        try {
            JSONObject obj = new JSONObject(json);
            Notificacao notf = service.gerarNotificacao(ctx, obj.getInt("codigoAluno"), Calendario.hoje(),
                    obj.getString("mensagem"), obj.getString("mensagem"),
                    TipoNotificacaoEnum.ALUNO_DA_FILA_ENTROU_NA_AULA, "", true);

        } catch (Exception ex) {
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @RequestMapping(value = "{ctx}/removerDaFila", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap renoverDaFila(@PathVariable String ctx,
                           @RequestParam final Integer codigoHorarioTurma,
                           @RequestParam final String dia,
                           @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        String result = "";
        try {
            result = agendaService.removerDaFilaDeEspera(ctx,codigoHorarioTurma, dia, codigoAluno);

            if(result.contains("Erro")){
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/v2/removerDaFila", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap removerDaFilaV2(@PathVariable String ctx,
                             @RequestParam final Integer codigoHorarioTurma,
                             @RequestParam final String dia,
                             @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        String result = "";
        try {
            result = agendaService.removerDaFilaDeEsperaV2(ctx,codigoHorarioTurma, dia, codigoAluno);

            if(result.contains("Erro")){
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarFila", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarFila(@PathVariable String ctx,
                           @RequestParam final Integer codigoHorarioTurma,
                           @RequestParam final String dia,
                           @RequestParam final Integer matricula) throws ServiceException {
        ModelMap mm = new ModelMap();
        String retorno = "";
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfg = css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
        try {
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            retorno = agendaService.consultarFila(ctx,codigoHorarioTurma, dia, matricula);
            if(retorno.contains("Erro")){
                mm.addAttribute(STATUS_ERRO, retorno);
            }else{
                mm.addAttribute("Fila", retorno);
            }

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{contexto}/consultarAulasTurmaProfessorDia", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody  ModelMap consultarAulasTurmaProfessorDia(@PathVariable String contexto,
                                                    @RequestParam Integer empresa,
                                                    @RequestParam String dia,
                                                    @RequestParam(required = false) String professor,
                                                    @RequestParam(required = false) Integer codProfessor,
                                                                   @RequestParam(required = false) Integer codColaborador) {
        ModelMap mm = new ModelMap();
        try {
            empService.validarChave(contexto);
            Date diaInicio = Calendario.getDate(Calendario.MASC_DATA, dia);
            List<AgendaTotalJSON> aulas = independente(contexto) ?
                    agendaService.obterAulasColetivas(contexto, diaInicio, diaInicio, empresa, true) :
                    agendaService.obterAulas(contexto, diaInicio, diaInicio, empresa);
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            String timeZone = obterFusoHorarioEmpresa(contexto,empresa);

            ConfiguracaoSistema cfg = configService.consultarPorTipo(contexto, ConfiguracoesEnum.DIAS_MOSTRAR_TOTEM);
            Date diaLimite = Uteis.somarCampoData(Calendario.hoje(timeZone), Calendar.MINUTE, cfg.getValorAsInteger());
            Map<Integer, String> mapaProfessores = agendaService.obterMapaProfessores(contexto, empresa, Boolean.FALSE, independente(contexto));
            Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = agendaService.obterProfessoresSubstituidos(contexto, null, diaInicio, diaLimite);
            Map<Integer, List<Date>> mapaAulasExcluidas = agendaService.obterAulasExcluidas(contexto, diaInicio, Uteis.getDataHora2359(diaInicio));
            boolean permiteVisualizarTodas = false;
            Usuario usuarioL = null;
            if(codColaborador != null){
                codProfessor = codColaborador;
            }
            if (!UteisValidacao.emptyNumber(codProfessor)) {
                usuarioL = usuarioService.consultarProColaborador(contexto, codProfessor)
                ;
            } else {
                List<Usuario> usuarios = usuarioService.consultarPorNomeColaborador(contexto, professor);
                for (Usuario usuario : usuarios) {
                    if (usuario.getEmpresaZW() == empresa) {
                        usuarioL = usuario;
                        break;
                    }
                }
                if (usuarioL == null && usuarios.size() > 0) {
                    usuarioL = usuarios.get(0);
                }

            }
            if (usuarioL == null) {
                throw new ServiceException(UsuarioExcecoes.ERRO_USUARIO_NAO_ENCONTRADO);
            }
            Permissao permissao = perfilService.obterPermissaoPorPerfilRecursoNativo(contexto, usuarioL.getPerfil().getCodigo(), RecursoEnum.VISUALIZAR_TODAS_AULAS_APP);
            if (permissao != null){
                if(permissao.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL)){
                    permiteVisualizarTodas = true;
                }
            }
            for (AgendaTotalJSON aula : aulas) {

                List<TurmaVideoDTO> listaLinkVideosSalvos =  agendaService.obterListaTurmaVideo(contexto,aula.getCodigoTurma());
                aula.setLinkVideos(listaLinkVideosSalvos);
               List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(aula.getId()));
               if(datas != null && datas.contains(Calendario.getDataComHoraZerada(Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm")))){
                    continue;
               }

                if(!UteisValidacao.emptyString(aula.getSituacao()) && aula.getSituacao().equals("IN")){
                    continue;
                }
                Map<Date, ProfessorSubstituido> mapaSubstitutos = mapaProfessoresSubstituidos.get(Integer.valueOf(aula.getId()));
                ProfessorSubstituido subs = null;
                if (mapaSubstitutos != null)
                    subs = mapaSubstitutos.get(Calendario.getDataComHoraZerada(Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm")));
                if (subs != null) {
                    Integer codigo = subs.getCodigoProfessorSubstituto();
                    String responsavel = mapaProfessores.get(codigo);
                    if (responsavel != null && responsavel.toUpperCase().contains(
                            professor != null
                                    ? professor.toUpperCase()
                                    : usuarioL.getProfessor().getNome().toUpperCase())
                            || permiteVisualizarTodas) {
                        aula.setResponsavel(mapaProfessores.get(codigo));
                        aula.setCodigoResponsavel(subs.getCodigoProfessorSubstituto());
                        Usuario user = usuarioService.consultarProColaborador(contexto, Integer.valueOf(aula.getCodigoResponsavel()));
                        aula.setFotoProfessor(mapaProfessores.get(-codigo));
                        if (user!=null && user.getFotoKeyApp() !=null && !user.getFotoKeyApp().equals("")) {
                            aula.setFotoProfessor(user.getFotoKeyApp());
                        }
                        jsonArray.add(new AulaDiaJSON(aula));
                    }
                } else if (aula.getResponsavel().toUpperCase().contains(
                        professor != null
                        ? professor.toUpperCase()
                        : usuarioL.getProfessor().getNome().toUpperCase())
                        || permiteVisualizarTodas) {
                    Usuario user = usuarioService.consultarProColaborador(contexto, Integer.valueOf(aula.getCodigoResponsavel()));
                    if (user!=null && user.getFotoKeyApp() !=null && !user.getFotoKeyApp().equals("")) {
                        aula.setFotoProfessor(user.getFotoKeyApp());
                    }
                    jsonArray.add(new AulaDiaJSON(aula));
                }
            }

            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    /***************************************VERIFICANDO********************************************/
    @RequestMapping(value = "{ctx}/marcarPresencaOrigem", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarPresenca(@PathVariable String ctx,
            @RequestParam final Integer codigoAula,
            @RequestParam final String matricula,
            @RequestParam final String aulaExperimental,
            @RequestParam final String dia,
            @RequestParam(required = false) final String chaveAluno,
            @RequestParam final String origem) {
      return matricularAluno(ctx,codigoAula,matricula,aulaExperimental,dia,OrigemSistemaEnum.valueOf(origem), true, 0, chaveAluno);
    }

    @RequestMapping(value = "{ctx}/confirmarAlunoEmAula", method = {RequestMethod.POST, RequestMethod.GET} )
    public @ResponseBody
    ModelMap confirmarAlunoEmAula(@PathVariable String ctx,
            @RequestParam final Integer codigoAula,
            @RequestParam final String matricula,
            @RequestParam final String dia,
            @RequestParam(required = false) final String chaveOrigem) {
        ModelMap mm = new ModelMap();
        try {
            boolean alunoEmAula = aulaService.alunoEmAula(ctx, matricula, codigoAula, Calendario.getDate(Calendario.MASC_DATA, dia), chaveOrigem);
            mm.addAttribute("alunoNaAula", alunoEmAula);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getCause() == null ? ex.getMessage() : ex.getCause().getMessage()));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/marcarPresenca", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarPresenca(@PathVariable String ctx,
                            @RequestParam final Integer codigoAula,
                            @RequestParam final String matricula,
                            @RequestParam final String aulaExperimental,
                            @RequestParam final String dia,
                            @RequestParam(required = false) final String chaveAluno,
                            @RequestParam (required = false) final Integer codUsuario) {
        return matricularAluno(ctx,codigoAula,matricula,aulaExperimental,dia, OrigemSistemaEnum.APP_TREINO, false, codUsuario, chaveAluno);
    }

    public ModelMap matricularAluno(String ctx,
                                    Integer codigoAula,
                                    String matricula,
                                    String aulaExperimental,
                                    String dia,
                                    OrigemSistemaEnum origemSistemaEnum,
                                    Boolean validarMarcarAluno,
                                    Integer codUsuario,
                                    String chaveAluno){
        ModelMap mm = new ModelMap();
        try {

            if(codigoAula != null && (aulaService.aulaFoiExlcuida(codigoAula, ctx, dia) || !aulaService.horarioTurmaExiste(codigoAula, ctx))) {
                mm.addAttribute(STATUS_ERRO, "Não foi possível agendar a aula do dia " + dia + " pois ela foi excluída. Realize a atualização das aulas no App e tente um novo agendamento na nova aula.");
                return mm;
            }
            Date dateAula = Calendario.getDate(Calendario.MASC_DATA, dia);
            if(chaveAluno != null && !ctx.equals(chaveAluno)){
                Usuario usuario = codUsuario == null ? null : usuarioService.obterPorId(ctx, codUsuario);
                String retorno = agendaTotalService.inserirAutorizadoAulaCheiaMatricula(
                        ctx,
                        matricula,
                        chaveAluno,
                        codigoAula,
                        Calendario.getDataComHoraZerada(dateAula),
                        OrigemSistemaEnum.APP_TREINO,
                        usuario,
                        null);
                if(retorno.toLowerCase().startsWith("erro")) {
                    mm.addAttribute(STATUS_ERRO, retorno);
                } else {
                    AgendaTotalJSON aulaJson = JSONMapper.getObject(new JSONObject(retorno), AgendaTotalJSON.class);
                    AulaAlunoJSON alunoJson = new AulaAlunoJSON(aulaJson, null, null);
                    if(!UteisValidacao.emptyString(alunoJson.getAula().getMapaEquipamentos())) {
                        alunoJson.getAula().setListaMapaEquipamentoAparelho(aulaService.montarListaEquipamentoAparelho(ctx, alunoJson.getAula().getCodigo(), alunoJson.getAula().getMapaEquipamentos()));
                    }
                    if (usuario != null) {
                        try {
                            ClienteSintetico cliente = usuarioService.obterPorId(ctx, codUsuario, true).getCliente();
                            clienteService.enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(ctx, cliente, aulaJson.getInicio(), aulaJson.getTitulo(), codigoAula, alunoJson.getAula().getProfessor());
                        } catch (Exception e) {
                            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, "Erro ao enviar notificação de lembrete de aula", e);
                        }
                    }
                    mm.addAttribute(STATUS_SUCESSO, alunoJson);
                }
                return mm;
            }

            if (validarMarcarAluno){
                ConfiguracaoSistema cfTipo = configService.consultarPorTipo(ctx, ConfiguracoesEnum.ALUNO_MARCAR_PROPRIA_AULA);
                if (!cfTipo.getValorAsBoolean()) {
                    mm.addAttribute(STATUS_ERRO, "Não é permitido realizar essa operação. Para marcar esta aula, por favor, compareça a recepção.");
                    return mm;
                }
            }

            ClienteSintetico cliente = clienteService.consultarAtivoPorMatricula(ctx, Integer.valueOf(matricula));
            Empresa empresa = empService.obterPorIdZW(ctx, cliente.getEmpresa());
            if(cliente == null){
                cliente = aulaService.addAlunoAutomaticamente(ctx, matricula);
            }

            if(independente(ctx)){
                AulaAlunoJSON alunoJson = agendaNovaService.adicionarAluno(ctx, cliente.getCodigo(), dateAula, codigoAula);
                try {
                    clienteService.enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(ctx, cliente, alunoJson.getAula().getInicio(), alunoJson.getAula().getNome(), codigoAula, alunoJson.getAula().getProfessor());
                } catch (Exception e) {
                    Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, "Erro ao enviar notificação de lembrete de aula", e);
                }
                mm.addAttribute(STATUS_SUCESSO, alunoJson);
            }else{
                boolean usarAulaExperimental =  Boolean.parseBoolean(aulaExperimental);
                Usuario usuarioTreino;
                if (UteisValidacao.emptyNumber(codUsuario)){
                    usuarioTreino = null;
                } else {
                    usuarioTreino = usuarioService.consultaPorId(ctx, codUsuario);

                }
                String retorno = agendaService.inserirAlunoAulaCheia(ctx,
                        cliente.getCodigoCliente(), codigoAula, dateAula,
                        origemSistemaEnum, usuarioTreino, cliente.getEmpresa(), usarAulaExperimental, false, null);
                if(retorno.contains("AULAEXPERIMENTAL")){
                    mm.addAttribute("aulaExperimental", retorno.replaceAll("AULAEXPERIMENTAL", "").replaceAll("ERRO\\:", ""));
                }else if(retorno.toUpperCase().contains("CONTROLAR_FREE_PASS")){
                    mm.addAttribute(STATUS_ERRO, "Você não possui a modalidade, entre em contato com a recepção para agendar uma aula experimental.");
                }else if(retorno.toLowerCase().startsWith("erro")){
                    mm.addAttribute(STATUS_ERRO, retorno);
                }else{
                    AgendaTotalJSON aulaJson = JSONMapper.getObject(new JSONObject(retorno), AgendaTotalJSON.class);
                    AulaAlunoJSON alunoJson = new AulaAlunoJSON(aulaJson, empresa.getNome(), null);
                    if(!UteisValidacao.emptyString(alunoJson.getAula().getMapaEquipamentos())) {
                        alunoJson.getAula().setListaMapaEquipamentoAparelho(aulaService.montarListaEquipamentoAparelho(ctx, aulaJson.getCodigoTurma(), alunoJson.getAula().getMapaEquipamentos()));
                    }
                    if (usarAulaExperimental) {
                        cliente.setNrAulasExperimentais(alunoJson.getNrAulasExperimentais());
                        clienteService.alterar(ctx,cliente);
                    }
                    if(cliente.getUsuarios() != null && cliente.getUsuarios().size() > 0) {
                        try {
                            clienteService.enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(ctx, cliente, aulaJson.getInicio(), aulaJson.getTitulo(), codigoAula, alunoJson.getAula().getProfessor());
                        } catch (Exception e) {
                            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, "Erro ao enviar notificação de lembrete de aula", e);
                        }
                    }
                    mm.addAttribute(STATUS_SUCESSO, alunoJson);
                }
            }


        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getCause() == null ? ex.getMessage() : ex.getCause().getMessage()));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    public JSONArray urls(String chave) throws Exception {
        return new JSONArray();
    }

    @RequestMapping(value = "{ctx}/enviarRelatorio", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap enviarRelatorio(@PathVariable String ctx,
            @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            String executeRequest = ExecuteRequestHttpService.executeRequest(url+"/envioRelatorio?chave="+ctx+"&matricula="+matricula, null);
            mm.addAttribute(STATUS_SUCESSO, executeRequest.contains("ok") ? "ok" : executeRequest);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/relatorio", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap relatorio(@PathVariable String ctx,
            @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> aulas = aulaService.obterAulasAluno(ctx, Integer.valueOf(matricula));
            List<RelatorioJSON> jsonArray = new ArrayList<RelatorioJSON>();
            for (AgendaTotalJSON aula : aulas) {
                jsonArray.add(new RelatorioJSON(aula));
            }
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarAulasDoAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasDoAluno(@PathVariable String ctx,
      @RequestParam final String matricula,
      @RequestParam final String dia) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> aulas = aulaService.obterAulasAluno(ctx, Integer.valueOf(matricula), dia);
            List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            for (AgendaTotalJSON aula : aulas) {
              jsonArray.add(new AulaAlunoJSON(aula, cliente));
            }
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/consultarAlunosDeUmaAula", method = RequestMethod.POST)
    public ModelMap consultarAlunosDeUmaAula(@PathVariable final String contexto,
                                             @RequestParam final Integer codigoAula,
                                             @RequestParam final String dia,
                                             HttpServletRequest request) {

        final ModelMap modelMap = new ModelMap();
        ResultAlunoClienteSinteticoJSON result = new ResultAlunoClienteSinteticoJSON();
        try {
            final List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();
            final List<AgendadoJSON> alunos;
            List<ClienteSintenticoJson> clienteSintenticoJsonList = new ArrayList<>();
            if(independente(contexto)) {
                alunos = aulaDao.obterAlunosPorAulaHorario(contexto, Uteis.getDate(dia), codigoAula);
            } else {
                result = aulaService.obterAlunosDeUmaAula(contexto, codigoAula, Uteis.getDate(dia));
                for(AgendadoJSON agendado : result.getResultAgendado()){
                    Integer matricula = 0;
                    try {
                        matricula = Integer.valueOf(agendado.getMatricula());
                    } catch (NumberFormatException e) {
                        matricula = 0;
                    }
                    ClienteJSON cliJson = new ClienteJSON(agendado.getCodigoPessoa(), agendado.getCodigoCliente(), agendado.getCodigoContrato(),
                            agendado.getUrlFoto(), agendado.getNome(), matricula, agendado.getTelefones(), agendado.getSituacaoContrato(),
                            agendado.getSituacao(), agendado.getFotokey(), agendado.getDataNascimento(), agendado.getEmail(), agendado.getSexo());

                    try {
                        cliJson.setAveragePower(agendado.getAveragePower());
                        cliJson.setTempoDeAula(agendado.getTempoDeAula());
                        cliJson.setPosicaoRankingAluno(agendado.getPosicaoRankingAluno());
                        cliJson.setCalories(agendado.getCalories());
                    } catch (Exception e) {
                        // Caso não tenha os dados de power, tempo de aula, posicao ranking e calorias
                        // não irá quebrar a aplicação
                    }

                    for(ClienteSintenticoJson clienteSintenticoJson : result.getResultCliSintentico()){
                        if(clienteSintenticoJson.getCodigoClienteZW().equals(agendado.getCodigoCliente())){
                            cliJson.setCodigo(clienteSintenticoJson.getCodigo());
                            break;
                        }
                    }
                    jsonArray.add(new AulaAlunoJSON(agendado, cliJson));
                }
            }
            modelMap.addAttribute("alunos", jsonArray);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return modelMap;
    }


    @ResponseBody
    @RequestMapping(value = "{contexto}/alunosAulaNaoColetiva", method = RequestMethod.POST)
    public ModelMap consultarAlunosAulaNaoColetiva(@PathVariable final String contexto,
                                                   @RequestParam final Integer codigoAula,
                                                   @RequestParam final String dia,
                                                   HttpServletRequest request) {
        final ModelMap modelMap = new ModelMap();

        try {
            final List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();
            final ResultAlunoClienteSinteticoJSON result = aulaService.obterAlunosAulaNaoColetiva(contexto, codigoAula, Uteis.getDate(dia));
            final List<AgendadoJSON> alunos = result.getResultAgendado();
            final List<ClienteSintenticoJson> clienteSintenticoJsonList = result.getResultCliSintentico();

            popularAlunosAgendados(contexto, modelMap, jsonArray, alunos, clienteSintenticoJsonList, request);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return modelMap;
    }

    private void popularAlunosAgendados(String contexto, ModelMap modelMap, List<AulaAlunoJSON> jsonArray,
                                        List<AgendadoJSON> alunos, List<ClienteSintenticoJson> clienteSintenticoJsonList, HttpServletRequest request) throws ServiceException {
        final List<String> nomes = new ArrayList<String>();
        final List<Integer> matriculas = new ArrayList<Integer>();
        final Map<String, AgendadoJSON> alunosAgrupadosNomeMatricula = new HashMap<String, AgendadoJSON>();

        popularAlunosNomesMatriculas(alunos, nomes, matriculas, alunosAgrupadosNomeMatricula);
        if (CollectionUtils.isEmpty(nomes) && CollectionUtils.isEmpty(matriculas)) {
            return;
        }

        final Map<String, ClienteSintetico> clientesSinteticos = clienteService.consultarPorMatriculasOuNomes(
                contexto,
                null,
                null,
                matriculas,
                nomes,
                null,
                request);

        popularAlunosPeloNome(jsonArray, nomes, alunosAgrupadosNomeMatricula, clientesSinteticos);
        popularAlunosPelaMatricula(jsonArray, matriculas, alunosAgrupadosNomeMatricula, clientesSinteticos);
        varificationForSinteticos(jsonArray, clientesSinteticos, clienteSintenticoJsonList, alunos);
        modelMap.addAttribute("alunos", jsonArray);
    }

    @RequestMapping(value = "{ctx}/consultarAlunosDeUmaTurma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAlunosDeUmaAula(@PathVariable String ctx,
                                   @RequestParam final Integer empresa,
                                   @RequestParam final Integer horarioTurma,
                                   @RequestParam final String dia,
                                   @RequestParam(required = false)final Boolean coletiva) {
        ModelMap mm = new ModelMap();
        try {
            List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();
            if(coletiva != null && coletiva){
                ResultAlunoClienteSinteticoJSON result = aulaService.obterAlunosDeUmaAula(ctx, horarioTurma, Uteis.getDate(dia));
                List<AgendadoJSON> alunos = result.getResultAgendado();
                for (AgendadoJSON aluno : alunos) {
                    jsonArray.add(new AulaAlunoJSON(aluno));
                }
            }else{

            }

            mm.addAttribute("alunos", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarMatriculas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarMatriculas(@PathVariable String ctx,
      @RequestParam final String codigoAula,
      @RequestParam final String dia) {
        ModelMap mm = new ModelMap();
        try {
            List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();

            List<AulaAluno> lista = aulaService.obterMatriculasDaAulaNoDia(ctx, Integer.valueOf(codigoAula), Calendario.getDate(Calendario.MASC_DATA, dia));
            for (AulaAluno aa : lista) {
                jsonArray.add(new AulaAlunoJSON(aa));
            }

            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {

            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarAulasDoAlunoAPartirDe", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasDoAlunoAPartirDe(@PathVariable String ctx,
      @RequestParam final String matricula,
      @RequestParam final String dia) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> aulas = aulaService.obterAulasAluno(ctx, Integer.valueOf(matricula), dia);
            List<AulaAlunoJSON> jsonArray = new ArrayList<AulaAlunoJSON>();
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            for (AgendaTotalJSON aula : aulas) {
              jsonArray.add(new AulaAlunoJSON(aula, cliente));
            }
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarPontosAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarPontosAluno(@PathVariable String ctx, @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
           Integer pontos = aulaService.obterPontosAluno(ctx, Integer.valueOf(matricula));
           mm.addAttribute(RETURN, pontos);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

        @RequestMapping(value = "{ctx}/desmarcarAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarAula(@PathVariable String ctx,
            @RequestParam final Integer matricula,
            @RequestParam final Integer codigoHorarioTurma,
            @RequestParam final String data,
            @RequestParam(required = false) final String chaveOrigem) {
        ModelMap mm = new ModelMap();
        try {
            if(!UteisValidacao.emptyString(chaveOrigem) && !ctx.equals(chaveOrigem)){
                agendaService.excluirAlunoAulaCheiaOutraUnidade(ctx, matricula, codigoHorarioTurma, Uteis.getDate(data, "dd/MM/yyyy"), chaveOrigem);
                mm.addAttribute(STATUS_SUCESSO, "OK");
                return mm;
            }
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula.toString());
            String result = agendaService.excluirAlunoAulaCheia(ctx, cliente.getCodigoCliente(), null,
                    codigoHorarioTurma, Uteis.getDate(data, "dd/MM/yyyy"), OrigemSistemaEnum.APP_TREINO, 0, cliente.getEmpresa(), null);

            if(null != result && result.contains("ERRO:")){
                mm.addAttribute(STATUS_ERRO, result.replaceFirst("ERRO:", "").trim());
            }else{
                try {
                    notificacaoAulaAgendadaService.cancelaNotificacaoAulaAgendada(ctx, cliente, codigoHorarioTurma, Uteis.getDate(data, "dd/MM/yyyy"));
                } catch (Exception e) {
                    Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, "Erro ao cancelar notifica����o de aula agendada", e);
                }
                agendaService.deletarHorarioEquipamentoAluno(ctx, cliente.getEmpresa(), codigoHorarioTurma, cliente.getCodigoCliente(), Uteis.getDate(data, "dd/MM/yyyy"));
                mm.addAttribute(STATUS_SUCESSO, result);
            }


        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/confirmarAlunoAula", method = RequestMethod.POST)
    public ModelMap confirmarAlunoAula(@PathVariable final String contexto,
                                       @RequestParam final Integer horarioTurma,
                                       @RequestParam final String matricula,
                                       @RequestParam final String dia) {

        final ModelMap modelMap = new ModelMap();
        try {
            final Date data = Uteis.getDate(dia, Uteis.FORMATO_DATA_SEM_HORA);


            ClienteSintetico cliente = clienteService.consultarPorMatricula(contexto, matricula);
            if (cliente == null) {
               cliente = this.aulaService.addAlunoAutomaticamente(contexto, matricula);
            }
            final Boolean reposicao = aulaService.reposicaoNestaAula(contexto, horarioTurma, cliente.getCodigoCliente(), data);
            final String retorno = agendaTotalService.gravarPresenca(
                    contexto, cliente.getCodigoCliente(), data, horarioTurma, reposicao, false, null, OrigemSistemaEnum.APP_TREINO);

            if (retorno.toUpperCase().contains(STATUS_ERRO.toUpperCase())) {
                modelMap.addAttribute(STATUS_ERRO, retorno);
                Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, retorno);
                return modelMap;
            }

            modelMap.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return modelMap;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/confirmarAlunoAulaColetiva", method = RequestMethod.POST)
    public ModelMap confirmarAlunoAulaColetiva(@PathVariable final String contexto,
                                               @RequestParam final Integer horarioTurma,
                                               @RequestParam final Integer cliente,
                                               @RequestParam final String dia,
                                               @RequestParam final Integer usuario) {

        final ModelMap modelMap = new ModelMap();
        try {
            final String retorno = aulaService.confirmarAlunoAula(contexto, cliente, horarioTurma, dia, usuario);
            if (retorno.toUpperCase().contains(STATUS_ERRO.toUpperCase())) {
                modelMap.addAttribute(STATUS_ERRO, retorno);
                Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, retorno);
                return modelMap;
            }

            modelMap.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return modelMap;
    }

    @RequestMapping(value = "{ctx}/desmarcarAlunoAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarAlunoAula(@PathVariable String ctx,
                                  @RequestParam final Integer horarioTurma,
                                  @RequestParam final Integer cliente,
                                  @RequestParam final String dia,
                                  @RequestParam final Integer contrato,
                                  @RequestParam final Boolean coletiva) {
        ModelMap mm = new ModelMap();
        try {
            String retorno;
            Date diaDt = Uteis.getDate(dia);
            if (coletiva) {
                retorno = agendaService.excluirAlunoAulaCheia(ctx,
                        cliente, null,
                        horarioTurma, diaDt, OrigemSistemaEnum.APP_TREINO,
                        0, 0, null);

            } else {
                retorno = agendaService.desmarcarAluno(ctx,
                        cliente,
                        horarioTurma, diaDt, contrato,
                        OrigemSistemaEnum.APP_TREINO, 0, 0,false, null);

            }
            if(retorno.toUpperCase().contains(STATUS_ERRO.toUpperCase())){
                throw new Exception(retorno);
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/aulasDesmarcadas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap aulasDesmarcadas(@PathVariable String ctx,
                                @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> consultarAulasDesmarcadas = agendaService.consultarAulasDesmarcadas(ctx, matricula, null);
            consultarAulasDesmarcadas = Ordenacao.ordenarLista(consultarAulasDesmarcadas, "dia");
            mm.addAttribute(RETURN, consultarAulasDesmarcadas);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/reporAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap reporAula(@PathVariable String ctx,
                                @RequestParam final Integer cliente,
                                @RequestParam final Integer contrato,
                                @RequestParam final Integer codigoHorarioTurma,
                                @RequestParam final Integer codigoUsuarioProfessor,
                                @RequestParam final Integer codigoEmpresa,
                                @RequestParam final Integer aulaDesmarcada,
                                @RequestParam final String diaAulaDesmarcada,
                                @RequestParam final String dia
                       ) {
        ModelMap mm = new ModelMap();
        try {
            String retorno = agendaService.reporAula(ctx, cliente,
                    aulaDesmarcada,
                    Integer.valueOf(codigoHorarioTurma),
                    contrato,
                    OrigemSistemaEnum.APP_PROFESSOR,
                    codigoUsuarioProfessor,
                    codigoEmpresa,
                    Uteis.getDate(diaAulaDesmarcada),
                    Uteis.getDate(dia),
                    false);
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    private void popularAlunosPelaMatricula(List<AulaAlunoJSON> jsonArray, List<Integer> matriculas,
                                            Map<String, AgendadoJSON> alunosAgrupadosNomeMatricula,
                                            Map<String, ClienteSintetico> clientesSinteticos) {
        for (final Integer matricula : matriculas) {
            final ClienteSintetico clienteSintetico = clientesSinteticos.get(String.valueOf(matricula));
            if (clienteSintetico != null) {
                jsonArray.add(new AulaAlunoJSON(alunosAgrupadosNomeMatricula.get(String.valueOf(matricula)), clienteSintetico));
            }
        }
    }

    private void popularAlunosPeloNome(List<AulaAlunoJSON> jsonArray, List<String> nomes,
                                       Map<String, AgendadoJSON> alunosAgrupadosNomeMatricula,
                                       Map<String, ClienteSintetico> clientesSinteticos) {
        for (final String nome : nomes) {
            final ClienteSintetico clienteSintetico = clientesSinteticos.get(nome);
            if (clienteSintetico != null) {
                jsonArray.add(new AulaAlunoJSON(alunosAgrupadosNomeMatricula.get(nome), clienteSintetico));
            }
        }
    }

    private void varificationForSinteticos(List<AulaAlunoJSON> jsonArray, Map<String,
            ClienteSintetico> clientesSinteticos, List<ClienteSintenticoJson> clienteSintenticoJsonList, List<AgendadoJSON> alunos) {
        if (clienteSintenticoJsonList == null ) return;

        Set<String> keys = clientesSinteticos.keySet();
        if (jsonArray.size() != alunos.size()) {
            List<AgendadoJSON> listAlunos = new ArrayList<>();
            for (AgendadoJSON aluno : alunos) {
                for(String key : keys) {
                    ClienteSintetico clienteSintetico = clientesSinteticos.get(key);
                    if (aluno.getCodigoCliente().intValue() == clienteSintetico.getCodigoCliente().intValue()) {
                        listAlunos.add(aluno);
                        break;
                    }
                }
            }

            alunos.removeAll(listAlunos);
            for (AgendadoJSON aluno : alunos) {
                for (ClienteSintenticoJson cli : clienteSintenticoJsonList) {
                    if (cli.getCodigoClienteZW().intValue() == aluno.getCodigoCliente().intValue() ){
                        jsonArray.add(new AulaAlunoJSON(aluno, cli));
                        break;
                    }
                }
            }
        }
    }

    private void popularAlunosNomesMatriculas(List<AgendadoJSON> alunos, List<String> nomes, List<Integer> matriculas,
                                              Map<String, AgendadoJSON> alunosAgrupadosNomeMatricula) {
        for (final AgendadoJSON aluno : alunos) {
            if (NumberUtils.isDigits(aluno.getMatricula()) && Integer.valueOf(aluno.getMatricula()) != 0) {
                final Integer matricula = Integer.valueOf(aluno.getMatricula());
                matriculas.add(matricula);
                alunosAgrupadosNomeMatricula.put(String.valueOf(matricula), aluno);
            } else if (isNotBlank(aluno.getNome())) {
                nomes.add(aluno.getNome());
                alunosAgrupadosNomeMatricula.put(aluno.getNome(), aluno);
            }
        }
    }

    private String obterFusoHorarioEmpresa(String ctx, Integer empresa) throws ServiceException {
        String timeZone = null;
        if(UteisValidacao.emptyNumber(empresa)){ // pega o menor hora para verificação. Isso porque o retira ficha não avalia empresa
            List<Empresa> listaEmpresas = empService.obterTodos(ctx);
            for(Empresa empresaVO: listaEmpresas){
                if(timeZone == null){
                    timeZone = empresaVO.getTimeZoneDefault();
                } else{
                    if(empresaVO.getTimeZoneDefault() != null){
                        if(Calendario.menorComHora(Calendario.hoje(empresaVO.getTimeZoneDefault()), Calendario.hoje(timeZone))){
                            timeZone = empresaVO.getTimeZoneDefault();
                        }
                    }
                }
            }

        } else {
            timeZone = EntityManagerFactoryService.getTimeZoneEmpresa(ctx,empresa);
            if(timeZone == null) {
                Empresa empresaZW = empService.obterPorIdZW(ctx, empresa);
                if(empresaZW.getTimeZoneDefault() != null && !empresaZW.getTimeZoneDefault().isEmpty()){
                    timeZone = empresaZW.getTimeZoneDefault();
                    EntityManagerFactoryService.setTimeZoneEmpresa(ctx,empresa, timeZone);
                }

            }
        }
        return timeZone == null ? TimeZoneEnum.Brazil_East.getId() : timeZone;
    }


    @RequestMapping(value = "{ctx}/simularocupacao", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap simularocupacao(@PathVariable String ctx,
                             @RequestParam(required = false) final String dia) {
        ModelMap mm = new ModelMap();
        try {
            Date data;
            if(dia == null){
                data = Calendario.hoje();
            }else{
                data = Uteis.getDate(dia, "dd_MM_yyyy");
            }
            agendaNovaService.gerarOcupacao(ctx, data);
            mm.addAttribute(RETURN, "simulado");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarCodAcessoAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarCodAcessoAluno(@PathVariable String ctx, @RequestParam String matricula) throws ServiceException {
        ModelMap mm = new ModelMap();
        ClienteSintetico aluno = null;
        try{
            aluno = clienteService.consultarPorMatricula(ctx, matricula);
            String retorno = aluno.getCodigoAcesso();
            mm.addAttribute(RETURN, retorno);
        }catch(Exception ex){
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ResultAlunoClienteSinteticoJSON.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

}
