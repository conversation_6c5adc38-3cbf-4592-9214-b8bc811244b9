package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON>;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.exportar.ExportarPDF;
import br.com.pacto.controller.json.exportar.ExportarXLS;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.cliente.TiposAcompanhamentoEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gestao.BITreinoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Ramos
 * @since 29/11/2018
 */

@Controller
@RequestMapping("/psec/treino-bi")
public class BITreinoController extends SuperController {

    private final BITreinoService biService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;


    @Autowired
    public BITreinoController(BITreinoService bi) {
        Assert.notNull(bi, "O serviço O serviço do BI do treino não foi injetado corretamente");
        this.biService = bi;
    }


    @ResponseBody
    @RequestMapping(value = "/dados", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterListaWods(
            @RequestParam("idProfessor") Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(biService.gerarBI(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/atualizar", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarIndicadores(
            @RequestParam("idProfessor") Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam("codigoPessoa") Integer codigoPessoa) {
        try {
            if (idProfessor == (-1)) {
                idProfessor = 0;
            } else {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            DashboardBI dash = biService.atualizarDash(idProfessor, empresaId);
            return ResponseEntityFactory.ok(dash);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/dash", params = {"idProfessor"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterDashboard(
            @RequestParam("idProfessor") Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam("idPessoa") Integer idPessoa) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, idPessoa);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.obterDash(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/carteira", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> carteira(
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.biCarteira(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processar-dados", params = {"professorId"},
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> procesardados(
            @RequestParam("professorId") Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            biService.processar(idProfessor, empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/treinamento",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinamento(
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            return ResponseEntityFactory.ok(biService.treinamento(idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/agenda", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agenda(
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, null);
            if (idProfessor == (-1)) {
                idProfessor = 0;
            }
            BITreinoAgendaDTO biTreinoAgendaDTO = biService.agenda(idProfessor, empresaId);
            return ResponseEntityFactory.ok(biTreinoAgendaDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/lista-alunos-execucao-treino-ultimos-dias/{empresaId}/{dia}/{periodo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listAlunosExecucaoTreinoUltimosDias(
            @PathVariable("empresaId") Integer empresaId,
            @PathVariable("dia") Integer dia,
            @PathVariable("periodo") String periodo,
            @RequestParam(value = "filters", required = false) JSONObject filter,
            PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(biService.listAlunosExecucaoTreinoUltimosDias(0, empresaId, dia, periodo, filter, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // ---------------- métodos de listagem de alunos dos BIs

    private ResponseEntity<EnvelopeRespostaDTO> listaGenerica(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter) {
        try {
            if (paginadorDTO.getSort() != null) {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, paginadorDTO, filter), paginadorDTO);
            } else if (paginadorDTO.getSize() == null) {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, null, filter), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(biService.listaBI(idProfessor, empresaId, indicador, paginadorDTO, filter), paginadorDTO);
            }
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter lista BI " + indicador, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private ResponseEntity<EnvelopeRespostaDTO> listaGenericaRequest(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(biService.listaBIRequest(idProfessor, empresaId, indicador, paginadorDTO, filter, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter lista BI " + indicador, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private ResponseEntity<EnvelopeRespostaDTO> listaDisponibilidades(Integer idProfessor, Integer empresaId, StatusAgendamentoEnum statusAg, TipoAgendamentoEnum tipoAg, String filter) {
        try {
            IndicadorDashboardEnum indicadorSelecionado = statusAg == null ? IndicadorDashboardEnum.DISPONIBILIDADES : statusAg.getIndicador();
            return ResponseEntityFactory.ok(biService.listaDisponibilidades(empresaId, idProfessor, indicadorSelecionado, statusAg, tipoAg, filter));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    //    /treino-bi/alunos-ativos-treino
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos-treino/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativostreino(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId,
                                                                  @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_COM_TREINO, paginadorDTO, filter);
    }

    //    /treino-bi/alunos-ativos/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAtivosTreinoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                          @RequestParam(value = "filtros", required = false) String filter,
                                                                          @RequestParam(value = "format", required = false) String format,
                                                                          @RequestParam(value = "sortField", required = false) String sortField,
                                                                          @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                          @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                          HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Ativos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-totaltreino-bi/alunos-total/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-total/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTotalTreinoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                         @RequestParam(value = "filtros", required = false) String filter,
                                                                         @RequestParam(value = "format", required = false) String format,
                                                                         @RequestParam(value = "sortField", required = false) String sortField,
                                                                         @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                         @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                         HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TOTAL_ALUNOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Total Alunos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-inativos/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-inativos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosInativosTreinoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                            @RequestParam(value = "filtros", required = false) String filter,
                                                                            @RequestParam(value = "format", required = false) String format,
                                                                            @RequestParam(value = "sortField", required = false) String sortField,
                                                                            @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                            @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.INATIVOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Inativos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-visitantes/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-visitantes/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVisitantesTreinoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                              @RequestParam(value = "filtros", required = false) String filter,
                                                                              @RequestParam(value = "format", required = false) String format,
                                                                              @RequestParam(value = "sortField", required = false) String sortField,
                                                                              @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                              @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                              HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.VISITANTES, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos Visitantes");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-treino-vencido/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-vencido/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoVencidosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                            @RequestParam(value = "filtros", required = false) String filter,
                                                                            @RequestParam(value = "format", required = false) String format,
                                                                            @RequestParam(value = "sortField", required = false) String sortField,
                                                                            @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                            @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.VENCIDOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com treino vencido");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-ativo-sem-treino/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-ativo-sem-treino/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAtivosSemTreinoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                             @RequestParam(value = "filtros", required = false) String filter,
                                                                             @RequestParam(value = "format", required = false) String format,
                                                                             @RequestParam(value = "sortField", required = false) String sortField,
                                                                             @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                             @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                             HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_SEM_TREINO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos ativos sem treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-treino-a-renovar/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoRenovarExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                           @RequestParam(value = "filtros", required = false) String filter,
                                                                           @RequestParam(value = "format", required = false) String format,
                                                                           @RequestParam(value = "sortField", required = false) String sortField,
                                                                           @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                           HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos a renovar treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-treino-em-dia/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-em-dia/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoEmDiaExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                         @RequestParam(value = "filtros", required = false) String filter,
                                                                         @RequestParam(value = "format", required = false) String format,
                                                                         @RequestParam(value = "sortField", required = false) String sortField,
                                                                         @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                         @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                         HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.EM_DIA, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com treino em dia");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-ativos-treino/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos-treino/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosTreinoAtivosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                          @RequestParam(value = "filtros", required = false) String filter,
                                                                          @RequestParam(value = "format", required = false) String format,
                                                                          @RequestParam(value = "sortField", required = false) String sortField,
                                                                          @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                          @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                          HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_COM_TREINO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos ativos com treino");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-vencer-30-dias/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-vencer-30-dias/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVencer30DiasExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                          @RequestParam(value = "filtros", required = false) String filter,
                                                                          @RequestParam(value = "format", required = false) String format,
                                                                          @RequestParam(value = "sortField", required = false) String sortField,
                                                                          @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                          @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                          HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos com contrato a vencer em 30 dias");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-renovaram/exportar
    @ResponseBody
    @RequestMapping(value = "/alunos-renovaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosRenovaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                       @RequestParam(value = "filtros", required = false) String filter,
                                                                       @RequestParam(value = "format", required = false) String format,
                                                                       @RequestParam(value = "sortField", required = false) String sortField,
                                                                       @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                       @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                       HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.RENOVARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Alunos que renovaram");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamentos/exportar
    @ResponseBody
    @RequestMapping(value = "/agendamentos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                    @RequestParam(value = "filtros", required = false) String filter,
                                                                    @RequestParam(value = "format", required = false) String format,
                                                                    @RequestParam(value = "sortField", required = false) String sortField,
                                                                    @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                    HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.AGENDAMENTOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-executaram/exportar
    @ResponseBody
    @RequestMapping(value = "/agendamento-executaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosExecutaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                              @RequestParam(value = "filtros", required = false) String filter,
                                                                              @RequestParam(value = "format", required = false) String format,
                                                                              @RequestParam(value = "sortField", required = false) String sortField,
                                                                              @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                              @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                              HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.COMPARECERAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos executados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-faltaram/exportar

    @ResponseBody
    @RequestMapping(value = "/agendamento-faltaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosFaltaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                            @RequestParam(value = "filtros", required = false) String filter,
                                                                            @RequestParam(value = "format", required = false) String format,
                                                                            @RequestParam(value = "sortField", required = false) String sortField,
                                                                            @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                            @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                            HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.FALTARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos com falta");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/agendamento-cancelaram/exportar

    @ResponseBody
    @RequestMapping(value = "/agendamento-cancelaram/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentosCancelaramExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                              @RequestParam(value = "filtros", required = false) String filter,
                                                                              @RequestParam(value = "format", required = false) String format,
                                                                              @RequestParam(value = "sortField", required = false) String sortField,
                                                                              @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                              @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                              HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.CANCELARAM, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Agendamentos cancelados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/disponibilidades/professores/exportar
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/professores/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadeProfessoresExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                  @RequestParam(value = "filtros", required = false) String filter,
                                                                                  @RequestParam(value = "format", required = false) String format,
                                                                                  @RequestParam(value = "sortField", required = false) String sortField,
                                                                                  @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                  @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                  HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.PROFESSORES, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Professores");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/percentual-ocupacao/exportar
    @ResponseBody
    @RequestMapping(value = "/percentual-ocupacao/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> percOcupacaoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                    @RequestParam(value = "filtros", required = false) String filter,
                                                                    @RequestParam(value = "format", required = false) String format,
                                                                    @RequestParam(value = "sortField", required = false) String sortField,
                                                                    @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                    HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.OCUPACAO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Ocupação");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/disponibilidades/treinos-novos/exportar
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/treinos-novos/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadeNovosTreinosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                   @RequestParam(value = "filtros", required = false) String filter,
                                                                                   @RequestParam(value = "format", required = false) String format,
                                                                                   @RequestParam(value = "sortField", required = false) String sortField,
                                                                                   @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                   @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                   HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_TREINOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Treinos novos");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/treinos-renovados/exportar
    @ResponseBody
    @RequestMapping(value = "/treinos-renovados/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosRenovadosExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                        @RequestParam(value = "filtros", required = false) String filter,
                                                                        @RequestParam(value = "format", required = false) String format,
                                                                        @RequestParam(value = "sortField", required = false) String sortField,
                                                                        @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                        @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                        HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_RENOVADOS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Treinos renovados");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                         @RequestParam(value = "filtros", required = false) String filter,
                                                                         @RequestParam(value = "format", required = false) String format,
                                                                         @RequestParam(value = "sortField", required = false) String sortField,
                                                                         @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                         @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                         HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES_FISICAS, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Avaliações fisicas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas-nao-realizado/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-nao-realizado/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliFisicasNaoRealizadoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                                @RequestParam(value = "filtros", required = false) String filter,
                                                                                @RequestParam(value = "format", required = false) String format,
                                                                                @RequestParam(value = "sortField", required = false) String sortField,
                                                                                @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                                @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                                HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.SEM_AVALIACAO, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Não realizadas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/avaliacoes-fisicas-realizado/exportar
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-realizado/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaFisicasRealizadoExportar(@RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                           @RequestParam(value = "filtros", required = false) String filter,
                                                                           @RequestParam(value = "format", required = false) String format,
                                                                           @RequestParam(value = "sortField", required = false) String sortField,
                                                                           @RequestParam(value = "sortDirection", required = false) String sortDirection,
                                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO,
                                                                           HttpServletRequest request) {
        try {
            paginadorDTO.setSort(sortField + "," + sortDirection);
            paginadorDTO.setPage((long) 0);
            Integer idProfessor = Integer.parseInt(new JSONObject(filter).get("professorId").toString());
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            List<LinkedHashMap<String, Object>> lista = biService.listaBILinkedMap(idProfessor, empresaId, IndicadorDashboardEnum.COM_AVALIACAO_FISICA, paginadorDTO, filter);
            String url = "";
            url = processarUrl(request, url, format, urlTreino, lista, "Realizadas");
            return ResponseEntityFactory.ok(url);
        } catch (Exception e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

    //    /treino-bi/alunos-treino-em-dia
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-em-dia/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoemdia(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                 @RequestParam(value = "filters", required = false) String filter,
                                                                 @RequestHeader("empresaId") Integer empresaId,
                                                                 @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.EM_DIA, paginadorDTO, filter);
    }

    ///treino-bi/alunos-treino-vencido
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-vencido/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinovencido(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                   @RequestParam(value = "filters", required = false) String filter,
                                                                   @RequestHeader("empresaId") Integer empresaId,
                                                                   @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.VENCIDOS, paginadorDTO, filter);
    }

    ///treino-bi/alunos-treino-a-renovar
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoarenovar(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
    }

    ///treino-bi/alunos-treino-a-renovar-30-dias
    @ResponseBody
    @RequestMapping(value = "/alunos-treino-a-renovar-30-dias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostreinoarenovar30dias(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                          @RequestParam(value = "filters", required = false) String filter,
                                                                          @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_A_VENCER, paginadorDTO, filter);
    }

    ///treino-bi/alunos-ativo-sem-treino
    @ResponseBody
    @RequestMapping(value = "/alunos-ativo-sem-treino/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativosemtreino(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId,
                                                                    @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS_SEM_TREINO, paginadorDTO, filter);
    }

    ///treino-bi/alunos-acessos
    @ResponseBody
    @RequestMapping(value = "/alunos-acessos",
            method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosacessos(
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @RequestParam(value = "filters", required = false) String filter,
            @RequestParam("dia") Long dia,
            @RequestParam("tipo") String tipo,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            idProfessor = verificarFiltroProfessor(idProfessor, filter);
            return ResponseEntityFactory.ok(biService.listaAcessos(idProfessor, empresaId, dia, tipo));
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    //    /treino-bi/agendamentos
    @ResponseBody
    @RequestMapping(value = "/agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                            @RequestParam(value = "filters", required = false) String filter,
                                                            @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AGENDAMENTOS, paginadorDTO, filter);
    }

    ///treino-bi/agendamento-executaram
    @ResponseBody
    @RequestMapping(value = "/agendamento-executaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentoexecutaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                     @RequestParam(value = "filters", required = false) String filter,
                                                                     @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.COMPARECERAM, paginadorDTO, filter);
    }

    ///treino-bi/agendamento-faltaram
    @ResponseBody
    @RequestMapping(value = "/agendamento-faltaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentofaltaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                   @RequestParam(value = "filters", required = false) String filter,
                                                                   @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.FALTARAM, paginadorDTO, filter);
    }

    ///treino-bi/agendamento-cancelaram
    @ResponseBody
    @RequestMapping(value = "/agendamento-cancelaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendamentocancelaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                     @RequestParam(value = "filters", required = false) String filter,
                                                                     @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.CANCELARAM, paginadorDTO, filter);
    }

    ///treino-bi/disponibilidades/aguardando-confirmacao
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/aguardando-confirmacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aguardandoconfirmacao(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                                     @RequestParam(value = "filters", required = false) String filter,
                                                                     @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    ///treino-bi/disponibilidades/confirmaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/confirmaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> confirmaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                           @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.CONFIRMADO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    ///treino-bi/disponibilidades/executaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/executaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> executaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                          @RequestParam(value = "filters", required = false) String filter,
                                                          @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.EXECUTADO, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    ///treino-bi/disponibilidades/faltaram
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/faltaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> faltaram(@RequestParam(value = "professorId", required = false) Integer idProfessor, @RequestHeader("empresaId") Integer empresaId,
                                                        @RequestParam(value = "filters", required = false) String filter,
                                                        @RequestParam("tipo") String tipo) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaDisponibilidades(idProfessor, empresaId, StatusAgendamentoEnum.FALTOU, TipoAgendamentoEnum.valueOf(tipo), filter);
    }

    ///treino-bi/disponibilidades/professores
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/professores/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professores(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                           @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId,
                                                           @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.PROFESSORES, paginadorDTO, filter);
    }

    ///treino-bi/disponibilidades/horas-executadas
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/horas-executadas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horasexecutadas(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                               @RequestParam(value = "filters", required = false) String filter,
                                                               @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.HRS_ATENDIMENTO, paginadorDTO, filter);
    }

    @ResponseBody
    @RequestMapping(value = "/disponibilidades/horas-disponibilidade", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horasdisponibilidade(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.HRS_DISPONIBILIDADE, paginadorDTO, filter);
    }

    ///treino-bi/treinos-novos
    @ResponseBody
    @RequestMapping(value = "/disponibilidades/treinos-novos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosnovos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                            @RequestParam(value = "filters", required = false) String filter,
                                                            @RequestHeader("empresaId") Integer empresaId,
                                                            @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_TREINOS, paginadorDTO, filter);
    }

    ///treino-bi/percentual-ocupacao
    @ResponseBody
    @RequestMapping(value = "/percentual-ocupacao/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> percentualocupacao(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId,
                                                                  @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.OCUPACAO, paginadorDTO, filter);
    }

    ///treino-bi/treinos-renovados
    @ResponseBody
    @RequestMapping(value = "/treinos-renovados/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> treinosrenovados(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                @RequestParam(value = "filters", required = false) String filter,
                                                                @RequestHeader("empresaId") Integer empresaId,
                                                                @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TREINOS_RENOVADOS, paginadorDTO, filter);
    }

    ///treino-bi/avaliacoes-fisicas
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesfisicas(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                 @RequestParam(value = "filters", required = false) String filter,
                                                                 @RequestHeader("empresaId") Integer empresaId,
                                                                 @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES_FISICAS, paginadorDTO, filter);
    }

    ///treino-bi/avaliacoes-fisicas-nao-realizado
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-nao-realizado/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasSemAgendamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                               @RequestParam(value = "filters", required = false) String filter,
                                                                               @RequestHeader("empresaId") Integer empresaId,
                                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.SEM_AVALIACAO, paginadorDTO, filter);
    }

    ///treino-bi/avaliacoes-fisicas-realizado
    @ResponseBody
    @RequestMapping(value = "/avaliacoes-fisicas-realizado/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacoesFisicasComAgendamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                               @RequestParam(value = "filters", required = false) String filter,
                                                                               @RequestHeader("empresaId") Integer empresaId,
                                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.COM_AVALIACAO_FISICA, paginadorDTO, filter);
    }

    //    /treino-bi/alunos-total
    @ResponseBody
    @RequestMapping(value = "/alunos-total/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunostotal(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                           @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId,
                                                           @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TOTAL_ALUNOS, paginadorDTO, filter);
    }

    ///treino-bi/alunos-ativos
    @ResponseBody
    @RequestMapping(value = "/alunos-ativos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosativos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                            @RequestParam(value = "filters", required = false) String filter,
                                                            @RequestHeader("empresaId") Integer empresaId,
                                                            @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ATIVOS, paginadorDTO, filter);
    }

    ///treino-bi/alunos-inativos
    @ResponseBody
    @RequestMapping(value = "/alunos-inativos/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosinativos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                              @RequestParam(value = "filters", required = false) String filter,
                                                              @RequestHeader("empresaId") Integer empresaId,
                                                              @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.INATIVOS, paginadorDTO, filter);
    }

    ///treino-bi/alunos-sem-acompanhamento
    @ResponseBody
    @RequestMapping(value = "/alunos-sem-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosSemAcompanhamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO,
                                                                       HttpServletRequest request) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenericaRequest(idProfessor, empresaId, IndicadorDashboardEnum.SEM_ACOMPANHAMENTO, paginadorDTO, filter, request);
    }

    ///treino-bi/alunos-em-acompanhamento
    @ResponseBody
    @RequestMapping(value = "/alunos-em-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosEmAcompanhamento(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO,
                                                                       HttpServletRequest request) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenericaRequest(idProfessor, empresaId, IndicadorDashboardEnum.EM_ACOMPANHAMENTO, paginadorDTO, filter, request);
    }

    ///treino-bi/iniciar-acompanhamento
    @ResponseBody
    @RequestMapping(value = "/alunos-sem-acompanhamento/{codigoPessoa}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> iniciarAcompanhamento(@RequestHeader("empresaId") Integer empresaId,
                                                                     @PathVariable Integer codigoPessoa,
                                                                     @RequestBody Integer idProfessor,
                                                                     @RequestParam (required = false, defaultValue = "1") Integer tipoAcompamanhento) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TiposAcompanhamentoEnum tipoAcompanhamentoEnum = TiposAcompanhamentoEnum.getFromCodigo(tipoAcompamanhento);
            if (tipoAcompanhamentoEnum == null) {
                return ResponseEntityFactory.erroInterno("TIPO_ACOMPANHAMENTO_INVALIDO", "Tipo de acompanhamento inválido");
            }
            if (idProfessor != 0) {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoPessoa);
            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, idProfessor);
            clienteSinteticoService.iniciarAcompanhamento(ctx, cliente, professor, tipoAcompanhamentoEnum);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/iniciar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> iniciarAcompanhamentoV2(@RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoCliente,
                                                                       @PathVariable Integer codigoProfessor,
                                                                       @RequestParam (required = false, defaultValue = "1") Integer tipoAcompamanhento) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TiposAcompanhamentoEnum tipoAcompanhamentoEnum = TiposAcompanhamentoEnum.getFromCodigo(tipoAcompamanhento);

            if (tipoAcompanhamentoEnum == null) {
                return ResponseEntityFactory.erroInterno("TIPO_ACOMPANHAMENTO_INVALIDO", "Tipo de acompanhamento inválido");
            }

            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoCliente);
            if (cliente == null) {
                throw new ServiceException("ALUNO_INEXISTENTE", "Aluno inexistente");
            }

            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, codigoProfessor);
            if (professor == null) {
                throw new ServiceException("PROFESSOR_INEXISTENTE", "Professor inexistente");
            }

            clienteSinteticoService.iniciarAcompanhamento(ctx, cliente, professor, tipoAcompanhamentoEnum);

            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName())
                    .log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(
                    e.getChaveExcecao(),
                    e.getMessage()
            );
        }
    }

    ///treino-bi/iniciar-acompanhamento
    @ResponseBody
    @RequestMapping(value = "/alunos-com-acompanhamento/{codigoPessoa}/{programaId}/{fichaId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAcompanhamento(@RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoPessoa,
                                                                       @PathVariable Integer programaId,
                                                                       @PathVariable Integer fichaId,
                                                                       @RequestBody Integer idProfessor) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (idProfessor != 0) {
                idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
            }
            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoPessoa);
            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, idProfessor);
            clienteSinteticoService.finalizarAcompanhamento(ctx, cliente, professor, programaId, fichaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar iniciar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/finalizar-acompanhamento-aluno/v2/{codigoCliente}/{codigoProfessor}/{programaId}/{fichaId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAcompanhamentoV2(@RequestHeader("empresaId") Integer empresaId,
                                                                         @PathVariable Integer codigoCliente,
                                                                         @PathVariable Integer codigoProfessor,
                                                                         @PathVariable Integer programaId,
                                                                         @PathVariable Integer fichaId) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, codigoCliente);
            if (cliente == null) {
                throw new ServiceException("ALUNO_INEXISTENTE", "Aluno inexistente");
            }

            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, codigoProfessor);
            if (professor == null) {
                throw new ServiceException("PROFESSOR_INEXISTENTE", "Professor inexistente");
            }

            clienteSinteticoService.finalizarAcompanhamento(ctx, cliente, professor, programaId, fichaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BITreinoController.class.getName())
                    .log(Level.SEVERE, "Erro ao tentar finalizar acompanhamento", e);
            return ResponseEntityFactory.erroInterno(
                    e.getChaveExcecao(),
                    e.getMessage()
            );
        }
    }

    ///treino-bi/alunos-inativos
    @ResponseBody
    @RequestMapping(value = "/alunos-visitantes/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosVisitantes(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                @RequestParam(value = "filters", required = false) String filter,
                                                                @RequestHeader("empresaId") Integer empresaId,
                                                                @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.VISITANTES, paginadorDTO, filter);
    }

    ///treino-bi/alunos-renovaram
    @ResponseBody
    @RequestMapping(value = "/alunos-renovaram/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosrenovaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                               @RequestParam(value = "filters", required = false) String filter,
                                                               @RequestHeader("empresaId") Integer empresaId,
                                                               @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.RENOVARAM, paginadorDTO, filter);
    }

    ///treino-bi/alunos-nao-renovaram
    @ResponseBody
    @RequestMapping(value = "/alunos-nao-renovaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosnaorenovaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NAO_RENOVARAM, paginadorDTO, filter);
    }

    ///treino-bi/alunos-vencer-30-dias
    @ResponseBody
    @RequestMapping(value = "/alunos-vencer-30-dias/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosvencer30dias(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                  @RequestParam(value = "filters", required = false) String filter,
                                                                  @RequestHeader("empresaId") Integer empresaId,
                                                                  @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
    }

    ///treino-bi/entrada-carteira/novos
    @ResponseBody
    @RequestMapping(value = "/entrada-carteira/novos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> entradacarteiranovos(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                    @RequestParam(value = "filters", required = false) String filter,
                                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_CARTEIRA, paginadorDTO, filter);
    }

    ///treino-bi/entrada-carteira/trocaram
    @ResponseBody
    @RequestMapping(value = "/entrada-carteira/trocaram", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> entradacarteiratrocaram(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.NOVOS_CARTEIRA_TROCARAM, paginadorDTO, filter);
    }

    ///treino-bi/saida-carteira
    @ResponseBody
    @RequestMapping(value = "/saida-carteira", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saidacarteira(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                             @RequestParam(value = "filters", required = false) String filter,
                                                             @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        idProfessor = verificarFiltroProfessor(idProfessor, filter);
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.TROCARAM_CARTEIRA, paginadorDTO, filter);
    }

    @ResponseBody
    @RequestMapping(value = "/avaliacao-treino/{tipoBusca}/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoTreino(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                               @RequestParam(value = "filters", required = false) String filter,
                                                               @RequestHeader("empresaId") Integer empresaId,
                                                               @PathVariable("codigoPessoa") Integer codigoPessoa,
                                                               @PathVariable Integer tipoBusca, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }

        switch (tipoBusca) {
            case 0:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.AVALIACOES, paginadorDTO, filter);
            case 1:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_1, paginadorDTO, filter);
            case 2:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_2, paginadorDTO, filter);
            case 3:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_3, paginadorDTO, filter);
            case 4:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_4, paginadorDTO, filter);
            default:
                return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ESTRELAS_5, paginadorDTO, filter);
        }
    }

    ///treino-bi/alunos-vencer-30-dias
    @ResponseBody
    @RequestMapping(value = "/lista-alunos-vencer-30-dias/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaAlunosVencer30Dias(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                                       @RequestParam(value = "filters", required = false) String filter,
                                                                       @RequestHeader("empresaId") Integer empresaId,
                                                                       @PathVariable Integer codigoPessoa, PaginadorDTO paginadorDTO) {
        Integer[] filtro = verificarFiltroProfessorDash(filter);
        idProfessor = filtro[0] != null ? filtro[0] : 0;
        codigoPessoa = filtro[1] == 0 ? codigoPessoa : filtro[1];
        if (idProfessor != 0) {
            idProfessor = verificarIdProfessor(idProfessor, empresaId, codigoPessoa);
        }
        return listaGenerica(idProfessor, empresaId, IndicadorDashboardEnum.ALUNOS_A_VENCER, paginadorDTO, filter);
    }

    private Integer verificarIdProfessor(Integer idProfessor, Integer empresa, Integer pessoa) {
        try {
            return biService.codigoProfessor(idProfessor, empresa, pessoa);
        } catch (ServiceException e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return 0;
    }

    @ResponseBody
    @RequestMapping(value = "/verificar-pendencias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> verificarPendencia(
            @RequestParam(value = "idProfessor") Integer idProfessor,
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "codigoPessoa") Integer codigoPessoa) {

        try {

            boolean temPendencias = verificarPendencias(idProfessor, empresaId, codigoPessoa);
            EnvelopeRespostaDTO resposta = new EnvelopeRespostaDTO();
            resposta.setContent(temPendencias);
            return ResponseEntityFactory.ok(resposta);

        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return ResponseEntityFactory.erroInterno("erro.verificar.pendencias", "Erro ao verificar pendências");
    }

    @ResponseBody
    @RequestMapping(value = "/importar-aluno-fora-treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarAlunoForaTreino(
            @RequestParam(value = "idProfessor") Integer idProfessor,
            @RequestHeader(value = "empresaId") Integer empresaId) {

        try {
            biService.importarAlunosForaTreino(idProfessor, empresaId);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return ResponseEntityFactory.erroInterno("erro.importar.aluno.fora.treino", "Erro ao iniciar processor para importar alunos fora do treino!");
    }

    private Integer verificarFiltroProfessor(Integer idProfessor, String filter) {
        try {
            if (idProfessor == null && filter != null && filter.contains("professorId")) {
                JSONObject objJson = new JSONObject(filter);
                idProfessor = objJson.getInt("professorId");
                if (idProfessor == (-1)) {
                    idProfessor = 0;
                }
                if (idProfessor != 0) {
                    idProfessor = biService.codigoProfessor(idProfessor, null, null);
                }
                return idProfessor;
            }
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return null;
    }

    private Integer[] verificarFiltroProfessorDash(String filter) {
        Integer[] retorno = new Integer[2];
        Integer codigoPessoa;
        Integer idProfessor;
        try {
            if (filter != null && filter.contains("professorId") && filter.contains("codigoPessoa")) {
                JSONObject objJson = new JSONObject(filter);
                idProfessor = objJson.getInt("professorId");
                codigoPessoa = objJson.getInt("codigoPessoa");
                if (idProfessor == (-1)) {
                    idProfessor = 0;
                }
                if (codigoPessoa == (-1)) {
                    codigoPessoa = 0;
                }
                retorno[0] = idProfessor;
                retorno[1] = codigoPessoa;
            } else {
                retorno[0] = 0;
                retorno[1] = 0;
            }
            return retorno;
        } catch (Exception e) {
            Uteis.logar(e, BITreinoController.class);
            e.printStackTrace();
        }
        return null;
    }

    public String processarUrl(HttpServletRequest request, String url, String format, String urlTreino, List<LinkedHashMap<String, Object>> lista, String tituloRel) throws Exception {
        String key = sessaoService.getUsuarioAtual().getChave();
        if (format.equals("PDF")) {
            ExportarPDF geradorPDF = new ExportarPDF();
            url = geradorPDF.visualizarRelatorioPDF(key, lista, request,
                    tituloRel, "relatorio-pdf", urlTreino);
        } else if (format.equals("XLS")) {
            ExportarXLS exportarXLS = new ExportarXLS();
            url = exportarXLS.visualizarRelatorioExcel(key, lista, request,
                    tituloRel, "relatorio-excel", urlTreino);
        }
        return url;
    }

    public boolean verificarPendencias(Integer idProfessor, Integer empresaId, Integer codigoPessoa) {
        JSONObject filtroJson = new JSONObject();
        try {
            filtroJson.put("professorId", idProfessor != null ? idProfessor : JSONObject.NULL);
            filtroJson.put("codigoPessoa", codigoPessoa != null ? codigoPessoa : JSONObject.NULL);
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
        String filtro = filtroJson.toString();

        ResponseEntity<EnvelopeRespostaDTO> respostaTreinosVencidos = alunostreinovencido(idProfessor, filtro, empresaId, codigoPessoa, new PaginadorDTO());
        ResponseEntity<EnvelopeRespostaDTO> respostaAlunosSemTreino = alunosativosemtreino(idProfessor, filtro, empresaId, codigoPessoa, new PaginadorDTO());

        boolean temTreinosVencidos = verificaSeTemResposta(respostaTreinosVencidos);
        boolean temAlunosSemTreino = verificaSeTemResposta(respostaAlunosSemTreino);

        return temTreinosVencidos || temAlunosSemTreino;

    }

    private static boolean verificaSeTemResposta(ResponseEntity<EnvelopeRespostaDTO> response) {
        if (response != null && response.getBody() != null && response.getBody().getContent() != null) {
            Object content = response.getBody().getContent();
            if (content instanceof Collection<?>) {
                return !((Collection<?>) content).isEmpty();
            } else if (content.getClass().isArray()) {
                return Array.getLength(content) > 0;
            }
            return false;
        }
        return false;
    }
}
