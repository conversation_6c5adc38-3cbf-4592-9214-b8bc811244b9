package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.ficha.FichaDTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/fichas")
public class FichaController {

    private final FichaService fichaService;
    private final ProgramaTreinoService programaTreinoService;

    @Autowired
    public FichaController(FichaService fichaService, ProgramaTreinoService programaTreinoService){
        Assert.notNull(fichaService, "O serviço de ficha não foi injetado corretamente");
        this.fichaService = fichaService;
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
    }
    @Autowired
    private HttpServletRequest request;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/pre-definido", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarFichasPreDefinidas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                           @RequestParam(value = "categoriaId", required = false) Integer categoriaId,
                                                                           @RequestParam(value = "nome", required = false) String nome,
                                                                           PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON = null;
            if(filtros != null){
                filtroFichaPredefinidaJSON = new FiltroFichaPredefinidaJSON(filtros);
            }
            return ResponseEntityFactory.ok(fichaService.consultarFichasPreDefinidas(filtroFichaPredefinidaJSON, categoriaId, nome, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/mensagens-recomendadas-ficha", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterMensagensRecomendadas() {
        try {
            return ResponseEntityFactory.ok(fichaService.obterMensagensRecomendadas());
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as mensagens recomendadas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarFicha(@RequestParam(value = "preDefinidoId", required = false) Integer preDefinidoId,
                                                          @RequestParam(value = "chaveOrigemFicha", required = false) String chaveOrigemFicha,
                                                          @RequestParam(value = "nomeDaFicha", required = false) String nomeDaFicha,
                                                          @RequestParam(value = "programaId", required = true) Integer programaId) {
        try {
            return ResponseEntityFactory.ok(fichaService.criarFicha(preDefinidoId, programaId, request, chaveOrigemFicha, nomeDaFicha));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao criar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarFicha(@PathVariable("id") Integer id,
                                                           @RequestBody FichaDTO fichaTO) {
        try {
            fichaTO.setId(id);
            return ResponseEntityFactory.ok(fichaService.editarFicha(fichaTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao alterar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/criarPredefinida", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarPredefinida(@RequestBody FichaDTO fichaTO) {
        try {
            return ResponseEntityFactory.ok(fichaService.criarPredefinida(fichaTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao alterar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}/ordenar-atividades", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reordenarAtividades(@PathVariable("id") Integer id,
                                                                   @RequestParam("atividadesIds") List<Integer> atividadesIds) {
        try {
            return ResponseEntityFactory.ok(fichaService.reordenarAtividades(id, atividadesIds));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao reordenar as atividades da ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

//    @ResponseBody
//    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
//    @RequestMapping(value = "/{id}/aplicar-padrao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<EnvelopeRespostaDTO> reordenarAtividades(@PathVariable("id") Integer id,
//                                                                   @RequestBody AtividadeFichaPadraoSeriesTO padroSeriesTO) {
//        try {
//            return ResponseEntityFactory.ok(fichaService.aplicarPadraoSeries(id, padroSeriesTO));
//        } catch (ServiceException e) {
//            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao aplicar padrao as series da ficha", e);
//            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
//        }
//    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirFicha(@PathVariable("id") final Integer id){
        try {
            fichaService.excluirPorId(id, request);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_FICHAS)
    @RequestMapping(value = "/categorias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarCategoriaFichas() {
        try {
            return ResponseEntityFactory.ok(fichaService.carregarCategoriaFichas());
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao carregar categoria de fichas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}/aplicar-padrao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> padronizarSeriesDaAtividadeFicha(
            @PathVariable("id") Integer fichaId,
            @RequestBody AtividadeFichaEndpointTO atividadeFichaEndpointTO
            ) {
        try {
            programaTreinoService.padronizarSeries(fichaId, atividadeFichaEndpointTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao padronizar series das atividades da ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}/tornar-predefinida", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tornarFichaPreDefinida(@PathVariable("id") final Integer id) {
        try {
            fichaService.criarFichaPredefinida(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar colocar o ficha como pré-definida", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarStituacaoAtividade(@PathVariable("id") final Integer id,
                                                                           @RequestBody  FichaDTO fichaTO) {
        try {
            fichaTO.setId(id);
            fichaService.atualizarSituacaoFichaPredefinida(fichaTO, id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar ficha pré-definida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "obterFichaPredefinida/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterFichaPredefinida(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(fichaService.obterFichaPredefinidaById(id));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar ficha pré-definida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "grupos-musculares/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterGruposMuscularesPorFicha(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(fichaService.consultarGruposMuscularesPorFichaTreino(id));
        } catch (Exception e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar grupos musculares por ficha", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}
