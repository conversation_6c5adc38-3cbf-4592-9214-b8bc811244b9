/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.scheduling.treino.TaskCompromissoTreino;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.*;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.atividade.AtividadeJSONControle;
import br.com.pacto.controller.json.atividade.TemaAtividade;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.base.EndpointDocController;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.ficha.read.FichaVersaoJSON;
import br.com.pacto.controller.json.programa.read.*;
import br.com.pacto.controller.json.programa.write.ProgramaWriteAppJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteJSON;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.intf.cliente.ClienteRedeEmpresaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.PerfilAlunoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.sincronizacao.SincronizacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONException;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import br.com.pacto.service.intf.programa.PrescricaoService;
/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/programa")
public class ProgramaTreinoJSONControle extends SuperControle {

    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private ProgramaTreinoAndamentoDao programaTreinoAndamentoDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private ProgramaTreinoDao programatreinoDao;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private SerieService ss;
    @Autowired
    private UsuarioService us;
    @Autowired
    private ObjetivoPredefinidoService ops;
    @Autowired
    private SincronizacaoService syncs;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private ClienteRedeEmpresaService clienteRedeEmpresaService;
    @Autowired
    private PrescricaoService prescricaoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private  PerfilAlunoService perfilAlunoService;
    private static final ConcurrentHashMap<String,String> mapaExec = new ConcurrentHashMap();
    private ProgramaTreinoAndamento programaTreinoAndamento = new ProgramaTreinoAndamento();

    public static ProgramaTreinoJSON preencherProgramaJSON(ProgramaTreino programa, final String ctx,
                                                           final Ficha fichaUnica, Boolean agruparSeries, String ordenarFicha, OrigemEnum origemEnum) throws ServiceException {
        ProgramaTreinoJSON programaJSON = new ProgramaTreinoJSON();
        boolean treinoIndependente = false;
        try {
            treinoIndependente = SuperControle.independente(ctx);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String urlBase = "";
        try {
            urlBase = TemaAtividade.getURLBase(ctx);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            programaJSON.setCod(programa.getCodigo());
            programaJSON.setNome(programa.getNome());
            programaJSON.setNomeMetodo("");
            programaJSON.setDescricaoMetodo("");
            programaJSON.setVersao(programa.getVersao().toString());
            programaJSON.setDataInicio(programa.getDataInicio());
            programaJSON.setDataTerminoPrevisto(programa.getDataTerminoPrevisto());
            String revisado = programa.isRevisado() ? "Sim" : "Não";
            programaJSON.setRevisado(revisado);
            programaJSON.setDataProximaRevisao(programa.getDataProximaRevisao());
            programaJSON.setProfessorCarteira(programa.getProfessorCarteira() == null
                    ? "<SEM PROFESSOR>" : programa.getProfessorCarteira().getNome());
            programaJSON.setCref(programa.getProfessorCarteira() == null
                    ? "<SEM CREF>" : programa.getProfessorCarteira().getCref());
            programaJSON.setCrefProfessorMontou(programa.getProfessorMontou() == null
                    ? "<SEM CREF>" : programa.getProfessorMontou().getCref());
            programaJSON.setProfessorMontou(programa.getProfessorMontou() == null ?
                    "<SEM PROFESSOR>" : programa.getProfessorMontou().getNome());

            programaJSON.setUsarNovaMontagemFicha(programa.getUsarNovaMontagemFicha());
            programaJSON.setGeradoPorIA((programa.getGeradoPorIA()));
            programaJSON.setEmRevisaoProfessor(programa.getEmRevisaoProfessor());

            StringBuilder objetivos = new StringBuilder();
            for (ObjetivoPrograma obj : programa.getObjetivos()) {
                objetivos.append(obj.getObjetivo().getNome()).append("\n");
            }
            programaJSON.setObjetivos(objetivos.toString());


            StringBuilder restricoes = new StringBuilder();
            for (RestricoesEnum r : programa.getRestricoesTreino()) {
                restricoes.append(r.getNome()).append("\n");
            }
            programaJSON.setRestricoes(restricoes.toString());


            Map<Integer, AtividadeJSON> mapaTodasAtividades = new HashMap<Integer, AtividadeJSON>();

            if (!UteisValidacao.emptyString(ordenarFicha)) {
                try {
                    Ordenacao.ordenarLista(programa.getProgramaFichas(), ordenarFicha);
                } catch (Exception ignored) {
                }
            }

            if(!UteisValidacao.emptyNumber(programa.getOrigem())) {
                programaJSON.setOrigem(OrigemProgramaTreinoEnum.fromCodigo(programa.getOrigem()));
            }

            loopFichas:
            for(ProgramaTreinoFicha programaFicha : programa.getProgramaFichas()) {
                if(programaFicha.getFicha() != null){
                    programaJSON.getFichas().add(FichaJSON.obterJSON(treinoIndependente, programa, programaFicha, fichaUnica, urlBase, mapaTodasAtividades, agruparSeries, ctx, origemEnum));
                }
                if (fichaUnica != null) {
                    break loopFichas;
                }
            }
            if (!mapaTodasAtividades.isEmpty()) {
                programaJSON.setAtividades(new ArrayList(mapaTodasAtividades.values()));
            }
            mapaTodasAtividades = null;
        } catch (Exception e) {
            if (e.getClass() == NullPointerException.class) {
                e.printStackTrace();
            }
            throw new ServiceException(e);
        }
        return programaJSON;
    }

    public static ProgramaVersaoJSON preencherVersaoProgramaJSON(ProgramaTreino programa,
            final String ctx) throws ServiceException {
        ProgramaVersaoJSON programaVersaoJSON = new ProgramaVersaoJSON();
        try {
            programaVersaoJSON.setCodPrograma(programa.getCodigo());
            programaVersaoJSON.setVersao(programa.getVersao());
            for (ProgramaTreinoFicha progFicha : programa.getProgramaFichas()) {
                programaVersaoJSON.getFichas().add(new FichaVersaoJSON(
                        progFicha.getFicha().getCodigo(), progFicha.getVersao(), progFicha.getFicha().getUltimaExecucao()));
            }
        } catch (Exception e) {
            if (e.getClass() == NullPointerException.class) {
                e.printStackTrace();
            }
            throw new ServiceException(e);
        }
        return programaVersaoJSON;
    }

    public JSONObject chamadaZW(String ctx, String endpoint, Integer empresa, Integer pessoa) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        params.add(new BasicNameValuePair("pessoa", pessoa.toString()));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private void setAtual(ClienteSintetico cliente, String ctx, ModelMap mm, ConfiguracaoSistema configSeriesSet, String ordenarFicha, OrigemEnum origemEnum, Boolean filtraEmRevisaoProfessor) throws Exception {
        if (cliente != null) {
                ProgramaVersaoJSON progJSON = ps.obterVersaoUltimoProgramaVigente(
                        ctx, cliente.getCodigo(), Calendario.hoje());
                Integer quantidadeExecucoes = null;

            if (progJSON != null) {
                ProgramaTreino programa = ps.obterPorId(ctx, progJSON.getCodPrograma());
                programa = verificarAulasPrevistas(ctx, programa);

                if (programa.getGeradoPorIA()) {
                    boolean obrigatoriedadeAprovacaoProfessor = Optional.ofNullable(configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR))
                            .map(ConfiguracaoSistema::getValor)
                            .map(Boolean::parseBoolean)
                            .orElse(false);

                    int tempoAprovacao = Optional.ofNullable(configService.consultarPorTipo(ctx, ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA))
                            .map(ConfiguracaoSistema::getValor)
                            .filter(valor -> !valor.isEmpty())
                            .map(Integer::valueOf)
                            .orElse(0);

                    // Se a aprovação do professor for obrigatória, zera o tempo de aprovação automática
                    if (obrigatoriedadeAprovacaoProfessor) {
                        tempoAprovacao = 0;
                    }

                    // Correção 1: Formatação de datas com precisão de milissegundos
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
                    LocalDateTime dataCriacao = programa.getDataLancamento().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime dataAprovacaoAutomatica = dataCriacao.plusMinutes(tempoAprovacao);

                    long diferencaEmMinutos = ChronoUnit.MINUTES.between(programa.getDataLancamento().toInstant(), Calendario.hoje().toInstant());
                    boolean podeAprovarAutomaticamente = tempoAprovacao > 0 && diferencaEmMinutos >= tempoAprovacao;

                    Map<String, Object> iaStatus = new HashMap<>();
                    iaStatus.put("vaiSerAprovadoAutomaticamente", !obrigatoriedadeAprovacaoProfessor && podeAprovarAutomaticamente);
                    iaStatus.put("professorPodeValidar", programa.getEmRevisaoProfessor());
                    iaStatus.put("obrigatorioProfValidar", obrigatoriedadeAprovacaoProfessor);
                    iaStatus.put("dataCriacao", dataCriacao.format(formatter)); // Usando formatter
                    iaStatus.put("dataAprovacaoAutomatica", dataAprovacaoAutomatica.format(formatter)); // Usando formatter

                    if (!obrigatoriedadeAprovacaoProfessor && podeAprovarAutomaticamente && programa.getEmRevisaoProfessor()) {
                        ProgramaTreino programaTreino = programatreinoDao.obterPorId(ctx, programa.getCodigo());
                        if (programaTreino == null) {
                            throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                        }

                        programaTreino.setEmRevisaoProfessor(false);
                        programatreinoDao.alterar(ctx, programaTreino);

                        // Correção 2: Remove as mensagens de erro e status nesse caso específico
                        // Não adiciona nada no mm

                    } else if (obrigatoriedadeAprovacaoProfessor && programa.getEmRevisaoProfessor()) {
                        mm.put("erro", "O treino está aguardando aprovação do professor.");
                        mm.put("iaStatus", iaStatus);
                    } else {
                        if (!obrigatoriedadeAprovacaoProfessor && programa.getEmRevisaoProfessor()) {
                            mm.put("erro", "Treino está sendo processado e será aprovado automaticamente.");
                            mm.put("iaStatus", iaStatus);
                        }
                    }
                }

                programaTreinoAndamento = ((ProgramaTreinoAndamentoDao) UtilContext.getBean(ProgramaTreinoAndamentoDao.class))
                        .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{programa.getCodigo()}, "codigo");
                atualizarSeriesPorTipoAtividade(programa);
                    obterUltimaExecucaoFicha(ctx, programa);
                    ps.ordenarFichas(ctx, programa);
                    if (programa.getNrTreinosRealizados() != null
                            && programa.getTotalAulasPrevistas() != null
                            && programa.getNrTreinosRealizados() >= programa.getTotalAulasPrevistas()) {
                        ConfiguracaoSistema cfgEmitirExecutados = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES);
                        if (cfgEmitirExecutados.getValorAsBoolean()) {
                            throw new ServiceException(getViewUtils().getMensagem("todasexecucoes"));
                        }
                    }
                    if (Calendario.menor(programa.getDataTerminoPrevisto(), Calendario.hoje())) {
                        ConfiguracaoSistema cfgEmitirVencidos = configService.consultarPorTipo(ctx, ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO);
                        if (!cfgEmitirVencidos.getValorAsBoolean()) {
                            throw new ServiceException(getViewUtils().getMensagem("mobile.programatreinovencido"));
                        }
                    }
                    ConfiguracaoSistema conf = configService.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_NOVA_MONTAGEM);
                    programa.setUsarNovaMontagemFicha(Boolean.valueOf(conf.getValor()));
                    ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), ordenarFicha, origemEnum);
                    ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                    if(UteisValidacao.emptyNumber(programa.getTotalAulasPrevistas())){
                        try {
                            ps.calcularAulasPrevistas(programa);
                        }catch (Exception e){
                            Uteis.logar(e, ProgramaTreinoJSONControle.class);
                            programa.setTotalAulasPrevistas(0);
                        }
                    }
                    obterClienteMensagemAviso(ctx, programa, programaJSON);
                    verificaFichaDoDiaAtual(ctx, programaJSON, cliente.getMatricula());
                    mm.addAttribute("programa", programaJSON);
                    if (andamento == null) {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(String.format("0/%s dias", programa.getTotalAulasPrevistas()), 0.0);
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(cliente.getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    } else {
                        quantidadeExecucoes = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, programa.getCodigo());
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                String.format("%s/%s dias", quantidadeExecucoes,
                                programa.getTotalAulasPrevistas()),
                                programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, programa.getTotalAulasPrevistas()));

                        acompanhamentoSimplesJSON.setFrequenciaSemanal(cliente.getFrequenciaSemanal());

                        acompanhamentoSimplesJSON.setNrTreinos(quantidadeExecucoes);

                        acompanhamentoSimplesJSON.setAulasPrevistas(programa.getTotalAulasPrevistas());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    }
                } else {
                    if (ps.clienteTemProgramaVencido(ctx, cliente)) {
                        List<ProgramaTreino> obterProgramasPorCliente = ps.obterProgramasPorCliente(ctx, cliente.getCodigo(), null, null, null, null);
                        ProgramaTreino programa = obterProgramasPorCliente == null || obterProgramasPorCliente.isEmpty() ?
                                new ProgramaTreino() : obterProgramasPorCliente.get(0);
                        if(filtraEmRevisaoProfessor && programa.getEmRevisaoProfessor()) {
                            throw new ServiceException(getViewUtils().getMensagem("mobile.programaemrevisao"));
                        }

                        if (Calendario.menor(programa.getDataTerminoPrevisto(), Calendario.hoje())) {
                            ConfiguracaoSistema cfgEmitirVencidos = configService.consultarPorTipo(ctx, ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO);
                            if (!cfgEmitirVencidos.getValorAsBoolean()) {
                                throw new ServiceException(getViewUtils().getMensagem("mobile.programatreinovencido"));
                            }
                        }

                        ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), ordenarFicha, origemEnum);
                        ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                        obterClienteMensagemAviso(ctx, programa, programaJSON);
                        mm.addAttribute("programa", programaJSON);
                        if (andamento == null) {
                            mm.addAttribute("acompanhamento", new AcompanhamentoSimplesJSON(String.format("0/%s dias",
                                    programa.getTotalAulasPrevistas()), 0.0));
                        } else {
                            AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                    String.format("%s/%s dias", quantidadeExecucoes,
                                    programa.getTotalAulasPrevistas()),
                                    programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, programa.getTotalAulasPrevistas()));
                            acompanhamentoSimplesJSON.setNrTreinos(quantidadeExecucoes);
                            acompanhamentoSimplesJSON.setAulasPrevistas(programa.getTotalAulasPrevistas());
                            mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                        }
                        mm.addAttribute("frequenciaSemanal",cliente.getFrequenciaSemanal());
                    } else {
                        throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                    }

                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
    }

    private ProgramaTreino verificarAulasPrevistas(String ctx, ProgramaTreino programa) throws Exception {
        if (programa.getDiasPorSemana() == null || programa.getTotalAulasPrevistas() == null) {
            List<ProgramaTreinoFicha> fichas = programaTreinoFichaDao.obterPorProgramaTreino(ctx, programa.getCodigo());
            programa.setDiasPorSemana(fichas.size());

            if (programa.getDataInicio() != null && programa.getDataTerminoPrevisto() != null) {
                long diferenca = programa.getDataTerminoPrevisto().getTime() - programa.getDataInicio().getTime();
                int semanas = (int) (diferenca / Uteis.calcularMillisPorSemana());
                programa.setTotalAulasPrevistas(semanas * programa.getDiasPorSemana());
            }

            programatreinoDao.update(ctx, programa);
        }

        return programa;
    }

    private void verificaFichaDoDiaAtual(String ctx, ProgramaTreinoJSON programaJSON, Integer matricula) throws ServiceException {
        FichaDoDiaDTO fichaDoDiaDTO = perfilAlunoService.fichaDoDia(matricula, false, ctx);
        if (fichaDoDiaDTO != null && fichaDoDiaDTO.getId() != null) {
            for(FichaJSON fichaJSON : programaJSON.getFichas()){
                if(fichaJSON.getCod().equals(fichaDoDiaDTO.getId())){
                    fichaJSON.setFichaDoDiaAtual(true);
                } else {
                    fichaJSON.setFichaDoDiaAtual(false);
                }
            }
        }
    }

    private void obterClienteMensagemAviso(String ctx, ProgramaTreino programa, ProgramaTreinoJSON programaJSON) throws ServiceException {
        ConfiguracaoSistema confMsgAviso = configService.consultarPorTipo(ctx, ConfiguracoesEnum.VISUALIZAR_MENSAGEM_AVISO);
        if(Boolean.parseBoolean(confMsgAviso.getValor())){
            if(programa != null && programa.getCliente() != null && programa.getCliente().getCodigo() != null && programa.getCliente().getCodigo() != 0) {
                programaJSON.setMensagemAviso(cs.obterClienteMensagem(ctx, programa.getCliente().getCodigo(), "AM"));
            }
        }
    }

    public void atualizarSeriesPorTipoAtividade(ProgramaTreino programa) {
        for (ProgramaTreinoFicha ptf : programa.getProgramaFichas()) {
            for (AtividadeFicha af : ptf.getFicha().getAtividades()) {
                if (af.getAtividade().getSeriesApenasDuracao() != null && af.getAtividade().getSeriesApenasDuracao()) {
                    for (Serie serie : af.getSeries()) {
                        serie.setRepeticao(0);
                        serie.setRepeticaoApp("0");
                        serie.setCarga(0.0);
                        serie.setCargaApp("0.0");
                        serie.setVelocidade(0.0);
                        serie.setDistancia(0);
                    }
                } else if (af.getAtividade().getTipo() == TipoAtividadeEnum.ANAEROBICO) {
                    for (Serie serie : af.getSeries()) {
                        serie.setVelocidade(0.0);
                        serie.setDistancia(0);
                        serie.setDuracao(0);
                    }
                } else if (af.getAtividade().getTipo() == TipoAtividadeEnum.AEROBICO) {
                    for (Serie serie : af.getSeries()) {
                        serie.setRepeticao(0);
                        serie.setRepeticaoApp("0");
                        serie.setCarga(0.0);
                        serie.setCargaApp("0.0");
                    }
                }
            }
        }
    }

    private void obterUltimaExecucaoFicha(String ctx, ProgramaTreino programa) throws ServiceException {
        for(ProgramaTreinoFicha f : programa.getProgramaFichas()){
            if(f.getFicha() != null){
                TreinoRealizado ultimo = getProgramaTreinoService().obterUltimoTreinoRealizadoFicha(ctx, f.getFicha().getCodigo());
                if(ultimo != null){
                    f.getFicha().setUltimaExecucao(ultimo.getDataInicio());
                }
            }
        }
    }

    @RequestMapping(value = "{ctx}/atual", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterAtual(@PathVariable String ctx,
                        @RequestParam(required = false) String username,
                        @RequestParam(required = false) String ordenarFicha,
                        @RequestParam(required = false) String matricula,
                        @RequestParam(required = false) String cpf,
                        @RequestParam(required = false) String origem,
                        @RequestParam (required = false, defaultValue = "false") Boolean professorEstaConsultando,
                        HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("obterAtual");
            OrigemEnum origemEnum = null;
            if (origem != null){
                origemEnum = OrigemEnum.valueOf(origem);
            }
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            Usuario usuario = null;
            if(username != null && username.startsWith("VIP_")){
                JSONObject programaEmRede = clienteRedeEmpresaService.programaEmRede(ctx, username, request);
                if(programaEmRede != null && programaEmRede.has("programa")){
                    mm.addAttribute("programa", programaEmRede.getJSONObject("programa").toMap());
                    if(programaEmRede.has("acompanhamento")){
                        mm.addAttribute("acompanhamento", programaEmRede.getJSONObject("acompanhamento").toMap());
                    }
                    return mm;
                }
            }
            if(username != null && username.startsWith("C0LAB_")){
                Integer codigoColaborador = Integer.valueOf(username.split("\\_")[1].trim());
                prescricaoService.setAtualColaborador(codigoColaborador, ctx, mm, configSeriesSet, ordenarFicha, getViewUtils(), origemEnum);
                return mm;
            }
            if(!UteisValidacao.emptyString(cpf)){
                usuario = us.consultarPorCpf(ctx, cpf);
            } else if(UteisValidacao.emptyString(matricula)){
                usuario = us.obterPorAtributo(ctx, "username", username, true);
            } else {
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }

            ClienteSintetico cliente = null;
            if(usuario == null){
                try {
                    cliente = cs.consultarPorMatricula(ctx, matricula == null ? username : matricula);
                }catch (Exception e){
                    e.printStackTrace();
                }
            } else {
                cliente = usuario.getCliente();
            }
            ConfiguracaoSistema configParcelaVencida = configService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_BUSCAR_PROGRAMA_PARCELA_VENCIDA);
            if(!independente(ctx) && configParcelaVencida.getValorAsBoolean() &&
                    chamadaZW(ctx, "/prest/treino/aluno-parcela-vencida", cliente.getEmpresa(),
                            cliente.getCodigoPessoa()).getBoolean("parcelaVencida")){
                throw new ServiceException(getViewUtils().getMensagem("mobile.programaalunoparcelavencido"));
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(cliente, ctx, mm, configSeriesSet, ordenarFicha, origemEnum, filtraEmRevisaoProfessor);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            /*Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);*/
            Uteis.logarDebug(ex.getMessage());
        }  catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualPorMatricula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualPorMatricula(@PathVariable String ctx,
                               @RequestParam Integer matricula,
                               @RequestParam(required = false) String ordenarFicha,
                               @RequestParam(required = false) String origemEnum,
                               @RequestParam (required = false, defaultValue = "false") Boolean professorEstaConsultando) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualPorMatricula");
            Usuario usuario = us.consultarPorMatricula(ctx, matricula);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            OrigemEnum origem = null;
            if (origemEnum != null){
                origem = OrigemEnum.valueOf(origemEnum);
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(usuario == null ? null : usuario.getCliente(), ctx, mm, configSeriesSet, ordenarFicha, origem, filtraEmRevisaoProfessor);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ehIYzrr2mfO10T8rw4zqaSmm9IuurDBqwOOSQj2C8os", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualPorMatriculaCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualPorMatriculaCrypt");
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            Integer matricula = o.optInt("matricula");
            String ordenarFicha = o.optString("ordenarFicha", null);
            String origemEnum = o.optString("origemEnum", null);
            Boolean professorEstaConsultando = o.optBoolean("professorEstaConsultando", false);

            Usuario usuario = us.consultarPorMatricula(ctx, matricula);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            OrigemEnum origem = null;
            if (origemEnum != null && !origemEnum.isEmpty()){
                origem = OrigemEnum.valueOf(origemEnum);
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(usuario == null ? null : usuario.getCliente(), ctx, mm, configSeriesSet, ordenarFicha, origem, filtraEmRevisaoProfessor);

            // Criptografar toda a resposta de sucesso
            JSONObject responseJson = new JSONObject();
            for (String key : mm.keySet()) {
                responseJson.put(key, mm.get(key));
            }
            mm.clear();
            mm.addAttribute(RETURN, Uteis.encryptUserData(responseJson.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atual/versao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterVersaoProgramaAtual(@PathVariable String ctx, @RequestParam String username) {
        ModelMap mm = new ModelMap();
        try {
            acao("oberVersaoProgramaAtual");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && usuario.getCliente() != null) {
                ProgramaVersaoJSON programaJSON = ps.obterVersaoUltimoProgramaVigente(
                        ctx, usuario.getCliente().getCodigo(), Calendario.hoje());
                if (programaJSON != null) {
                    ProgramaTreino programa = ps.obterPorId(ctx, programaJSON.getCodPrograma());
                    ps.ordenarFichas(ctx, programa);
                    programaJSON = preencherVersaoProgramaJSON(programa, ctx);
                    mm.addAttribute("programa", programaJSON);

                    ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                    if (andamento == null) {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(String.format("0/%s dias", programa.getTotalAulasPrevistas()), 0.0);
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(usuario.getCliente().getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    } else {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                String.format("%s/%s dias", andamento.getNrTreinos(),
                                        programa.getTotalAulasPrevistas()), andamento.getPercentualFrequenciaAteHoje());
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(usuario.getCliente().getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    }
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/submitserie", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitSerie(@PathVariable String ctx, @RequestParam String username,
                            @RequestParam String idPrograma,
                            @RequestParam String idFicha,
                            @RequestParam String inicio,
                            @RequestParam String fim,
                            @RequestParam String idAtividade,
                            @RequestParam String valor1,
                            @RequestParam String valor2,
                            @RequestParam Integer idSerie,
                            @RequestParam(required = false) String atualizar,
                            @RequestParam(required = false) String matricula
                         ) {
        ModelMap mm = new ModelMap();
        try {
//            super.init(ctx, token);
//            Uteis.logar(null, "teste  - submit serie - " + username);
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = us.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                acao("submitSerie.inserirSerieRealizada");
                ConfiguracaoSistema cfgForcarCarga = configService.consultarPorTipo(ctx, ConfiguracoesEnum.MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA);
                SerieRealizada serieRealExistente = ps.obterSerieRealizadaHoje(ctx, idSerie);
                if (serieRealExistente == null) {
                    ProgramaTreinoFicha programaTreinoFicha = ps.obterProgramaTreinoFicha(ctx,
                            Integer.valueOf(idPrograma), Integer.valueOf(idFicha));
                    Serie s = ss.obterPorId(ctx, Integer.valueOf(idSerie));
                    SerieRealizada sr = ps.inserirTreinoRealizado(ctx, usuario.getUserName(),
                            programaTreinoFicha.getPrograma(),
                            idFicha, inicio, fim,
                            idAtividade, s, s.getAtividadeFicha(), OrigemExecucaoEnum.SMARTPHONE, programaTreinoFicha);
                    sr.setForcarAtualizacaoFicha(cfgForcarCarga.getValorAsBoolean());
                    sr.verificarSeDeveAtualizarDadosNaFicha(valor1, valor2);
                    if (sr.getSerieAtualizar() != null) {
                        ps.atualizarSerieComBaseNaSerieRealizada(ctx, sr);
                    }
                } else {
                    serieRealExistente.setForcarAtualizacaoFicha(cfgForcarCarga.getValorAsBoolean());
                    serieRealExistente.verificarSeDeveAtualizarDadosNaFicha(valor1, valor2);
                    ps.atualizarSerieRealizada(ctx, idSerie, valor1, valor2, null,
                            cfgForcarCarga.getValorAsBoolean());
                }
                mm.addAttribute(STATUS, STATUS_SUCESSO);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
            }

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/submittreinoCommentAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submittreinoCommentAPP(@PathVariable String ctx,
                                    @RequestParam String username,
                                    @RequestParam(required = false) String matricula,
                                    @RequestParam String idPrograma,
                                    @RequestParam Integer idFicha,
                                    @RequestParam String dia,
                                    @RequestParam String nota,
                                    @RequestParam Integer tempo,
                                    @RequestParam(required = false) String comentario,
                                    HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submittreinoCommentAPP");
            ProgramaTreino programa = ps.obterPorId(ctx, Integer.valueOf(idPrograma));
            try {
                AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,"app", null, null, request), ctx, programa);
            }catch (Exception ignored) { }

            Usuario usuario = null;
            if(!UteisValidacao.emptyString(matricula)){
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }

            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, usuario == null ? username : usuario.getUserName(), idPrograma,
                    idFicha, 0, nota, tempo, comentario, null), ctx, programa);
            try {
                Date dataExecucao = Uteis.getDate(dia, "yyyy-MM-dd HH:mm:ss");
                fichaService.atualizarUltimaExecucao(ctx, dataExecucao, idFicha);
            } catch (Exception ignored) {
            }
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/submittreinoComment", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreinoComment(@PathVariable String ctx,
                          @RequestParam String username,
                          @RequestParam String idPrograma,
                          @RequestParam Integer idFicha,
                          @RequestParam Integer dia,
                          @RequestParam String nota,
                          @RequestParam Integer tempo,
                          @RequestParam(required = false) String comentario,
                          HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submitTreinoComment");
            try {
                AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,
                        "app", null, null, request), ctx, null);
            }catch (Exception ignored) { }
            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx,username,idPrograma,
                    idFicha, dia,nota,tempo,comentario, null), ctx, null);
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/v2/submittreinoCommentAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submittreinoCommentAPPV2(@PathVariable String ctx,
                                      @RequestParam String username,
                                      @RequestParam(required = false) String matricula,
                                      @RequestParam String idPrograma,
                                      @RequestParam Integer idFicha,
                                      @RequestParam String dia,
                                      @RequestParam String nota,
                                      @RequestParam Integer tempo,
                                      @RequestParam(required = false) String comentario,
                                      HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submittreinoCommentAPP");
            Date dataExecucao = Uteis.getDate(dia, "yyyy-MM-dd HH:mm:ss");

            if (dataExecucao.after(Calendario.hoje())) {
                throw new ServiceException("Data de execução não pode ser maior que a data atual");
            }

            ProgramaTreino programa = ps.obterPorId(ctx, Integer.valueOf(idPrograma));
            if (programa == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
            }
            //se o dia da datainicio do programa for anterior a data de execução do treino, então não deixa executar
            if (programa.getDataInicio().after(dataExecucao)) {
                throw new ServiceException("Data de execução do treino não pode ser menor que a data de início do programa");
            }

            if (Calendario.dataNoMesmoDiaMes(dataExecucao, Calendario.hoje())) {
                try {
                    AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,
                            "app", null, null, request), ctx, null);
                } catch (Exception ignored) {
                }
                AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, username, idPrograma,
                        idFicha, 0, nota, tempo, comentario, null), ctx, null);
                mm.addAttribute(RETURN, acomp);
            } else {
                System.out.println("Programa: " + programa.getNome());
                Usuario usuario = null;
                if (!UteisValidacao.emptyString(matricula)) {
                    usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
                }

                System.out.println("Usuario: " + usuario);
                AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, usuario == null ? username : usuario.getUserName(), idPrograma,
                        idFicha, 0, nota, tempo, comentario, dataExecucao), ctx, programa);
                try {
                    fichaService.atualizarUltimaExecucao(ctx, dataExecucao, idFicha);
                } catch (Exception ignored) {
                }
                mm.addAttribute(RETURN, acomp);
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/submittreino", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreino(@PathVariable String ctx, @RequestBody final TreinoRealizadoJSON treinoRealizado) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logar(null, treinoRealizado.toJSON());
            acao("submittreino");
            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, treinoRealizado.getUsername(), treinoRealizado.getIdPrograma(),
                    treinoRealizado.getIdFicha(), treinoRealizado.getDia(), treinoRealizado.getNota(), treinoRealizado.getTempo(),"", null), ctx, null);
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/executarFicha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap executarFichaRetorno(@PathVariable String ctx,
                                  @RequestParam(required = false) final String username,
                                  @RequestParam(required = false) final String unidadeExecucao,
                                  @RequestParam(required = false) final String chaveExecucao,
                                  final String dataAtual, final String idPrograma, final String idFicha,
                                  boolean fichaConcluida,
                                  HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if(username != null && username.startsWith("VIP_")){
                JSONObject execucaoFichaEmRede = clienteRedeEmpresaService.executarFichaEmRede(ctx,
                        dataAtual, idPrograma, idFicha,  fichaConcluida, username, request);
                if(execucaoFichaEmRede != null && execucaoFichaEmRede.has(RETURN)){
                    mm.addAttribute(RETURN, execucaoFichaEmRede.getJSONObject(RETURN).toMap());
                    return mm;
                }
            }
            if (!mapaExec.containsKey(ctx)) {
                mapaExec.put(ctx, idPrograma);
                acao("executarFichaRetorno");
                AcompanhamentoSimplesJSON acomp = preencherAndamento(
                        ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha,
                                Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual), fichaConcluida,
                                "retiraFicha", chaveExecucao, unidadeExecucao, request), ctx, null);
                mm.addAttribute(RETURN, acomp);
            } else {
                final String msg = String.format("Já possui um programa %s sendo processado para empresa %s. Por favor aguarde...", idPrograma, ctx);
                mm.put(STATUS_ERRO, msg);
                Uteis.logar(null, msg);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            mapaExec.remove(ctx);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualizarNrTreinos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarNrTreinos(@PathVariable String ctx, @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualizarNrTreinos");
            mm.addAttribute(RETURN, ps.atualizarAndamentoAluno(ctx, matricula));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/selecaoFicha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarNrTreinos(@PathVariable String ctx,
                                @RequestParam final Integer idFicha,
                                @RequestParam final String usuario,
                                @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            acao("selecaoFicha");
            mm.addAttribute(RETURN, ps.marcarTreinoRealizadoFichaAvulsa(ctx, idFicha,usuario, data));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/objPredef", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap objPredef(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            acao("objPredef");
            mm.addAttribute(RETURN, ops.obterTodos(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/objPredef/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap objPredef_sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        try {
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<ObjetivoPredefinido> objetivos = syncs.obterLista(ctx,
                    TipoClassSincronizarEnum.ObjetivoPredefinido, dataBase);
            mm.addAttribute(RETURN, objetivos);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private AcompanhamentoSimplesJSON obterAndamento(final String ctx, ProgramaTreino programa) {
        try {
            ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
            return preencherAndamento(andamento, ctx, null);
        } catch (ServiceException ex) {
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return new AcompanhamentoSimplesJSON("0/0 dias", 0.0);
    }

    private AcompanhamentoSimplesJSON preencherAndamento(ProgramaTreinoAndamento andamento, String ctx, ProgramaTreino programa) {
        if (andamento != null) {
            Integer quantidadeExecucoes = null;
            try {
                Integer quantidadeExecucoesTreinoRealizados = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, andamento.getPrograma().getCodigo());
                quantidadeExecucoes = quantidadeExecucoesTreinoRealizados == null ?
                        0 : quantidadeExecucoesTreinoRealizados;
            } catch (ServiceException e) {
                e.printStackTrace();
            }

            return new AcompanhamentoSimplesJSON(quantidadeExecucoes + "/"
                    + andamento.getPrograma().getTotalAulasPrevistas() + " dias",
                    andamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, andamento.getPrograma().getTotalAulasPrevistas()));
        }
        return new AcompanhamentoSimplesJSON("0/0 dias", 0.0);
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/ultimos", method = RequestMethod.POST)
    public ModelMap obterUltimosProgramas(@PathVariable final String contexto,
                                          @RequestParam final String username,
                                          @RequestParam final Integer codigoCliente) {

        ModelMap modelMap = new ModelMap();
        List<ProgramaTreino> programasTreino = null;

        try {
            acao("ultimos");

            final Usuario usuario = us.obterPorAtributo(contexto, "username", username);
            if (usuario == null || TipoUsuarioEnum.ALUNO.equals(usuario.getTipo())) {
                String mensagemErro = getViewUtils().getMensagem("mobile.usuarioinvalido");
                modelMap.addAttribute(STATUS_ERRO, mensagemErro);
                Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, mensagemErro);
                return modelMap;
            }

            programasTreino = ps.obterProgramasPorCliente(
                    contexto, codigoCliente, null, null, null, 3);

            ps.refresh(contexto, programasTreino);

        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        List<ProgramaWriteJSON> programasWriteJSON = new ArrayList<ProgramaWriteJSON>();
        for (ProgramaTreino programaTreino : programasTreino) {
            ProgramaWriteJSON programaWriteJSON = new ProgramaWriteJSON(programaTreino);
            programaWriteJSON.preencherObjetivosPrograma(programaTreino);
            programaWriteJSON.preencherProgramaTreinoFichas(programaTreino);
            programaWriteJSON.setUsername(username);
            programaWriteJSON.setFrequencia(obterAndamento(contexto, programaTreino));
            programasWriteJSON.add(programaWriteJSON);
        }

        return modelMap.addAttribute(RETURN, programasWriteJSON);
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/ultimosZW", method = RequestMethod.POST)
    public ModelMap obterUltimosProgramas(@PathVariable final String contexto,
                                          @RequestParam final Integer codigoCliente) {

        ModelMap modelMap = new ModelMap();
        List<ProgramaTreino> programasTreino = null;
        try {
            programasTreino = ps.obterProgramasPorClienteZW(
                    contexto, codigoCliente, null, null, null, 1);
        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        for (ProgramaTreino programaTreino : programasTreino) {
            ProgramaWriteJSON programaWriteJSON = new ProgramaWriteJSON(programaTreino);
            programaWriteJSON.preencherObjetivosPrograma(programaTreino);
            programaWriteJSON.preencherProgramaTreinoFichas(programaTreino);
            programaWriteJSON.setFrequencia(obterAndamento(contexto, programaTreino));
            return modelMap.addAttribute(RETURN, programaWriteJSON);
        }

        return modelMap.addAttribute(RETURN, "empty");
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/incluirProgramaAoAluno", method = RequestMethod.POST)
    public String incluirProgramaAoAluno(@PathVariable final String contexto,
                                          @RequestParam final Integer empresa,
                                          @RequestParam final Integer codigoPrograma,
                                          @RequestParam final String inicio,
                                          @RequestParam final String fim,
                                          @RequestParam final Integer codigoCliente) {
        try {
            ClienteSintetico cliente = cs.obterPorCodigoCliente(contexto, codigoCliente);
            if(cliente == null){
                return "Cliente não encontrado";
            }
            ProgramaTreino programa = ps.obterPorId(contexto, codigoPrograma);
            if(programa == null){
                return "Programa não encontrado";
            }

            programa.setDataInicio(Uteis.getDate(inicio, "dd/MM/yyyy"));
            programa.setDataTerminoPrevisto(Uteis.getDate(fim, "dd/MM/yyyy"));
            ps.criarProximoProgramaTreinoJSON(contexto, empresa, cliente, programa);
            return "OK";
        }catch (Exception ex){
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/alterarPrograma", method = RequestMethod.POST)
    public String alterarPrograma(@PathVariable final String contexto,
                                         @RequestParam final Integer empresa,
                                         @RequestParam final Integer codigoPrograma,
                                         @RequestParam final String novaDataFim) {
        try {
            ProgramaTreino programa = ps.obterPorId(contexto, codigoPrograma);
            if(programa == null){
                return "Programa não encontrado";
            }

            if(programa.getDataInicio().after(Uteis.getDate(novaDataFim, "dd/MM/yyyy"))){
                return "Data de término não pode ser menor que a data de início";
            }

            if(programa.getDataInicio().before(Calendario.hoje())){
                return "O programa já iniciou";
            }

            programa.setDataTerminoPrevisto(Uteis.getDate(novaDataFim, "dd/MM/yyyy"));
            ps.alterarProgramaTreinoJSON(contexto, empresa, programa);
            return "OK";
        }catch (Exception ex){
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @RequestMapping(value = "{ctx}/default", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarProgramaDefault(@PathVariable String ctx,
            @RequestParam final String username, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("default");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.INCLUIR);
                ClienteSintetico cli = cs.obterPorId(ctx, codigoCliente);
                if (cli != null) {
                    ProgramaTreino programa = ps.gerarProgramaDefault(ctx, cli, usuario, null, null);
                    ProgramaWriteJSON json = new ProgramaWriteJSON(programa);
                    json.setUsername(username);
                    mm.addAttribute(STATUS_SUCESSO, json);
                } else {
                    throw new ValidacaoException("cliente.naoencontrado.treino");
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/persistir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistir(@PathVariable String ctx, @RequestBody final ProgramaWriteJSON programaGravar) {
        ModelMap mm = new ModelMap();
        try {
            acao("persistir");
            Usuario usuario = us.obterPorAtributo(ctx, "username", programaGravar.getUsername());
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                ProgramaTreino p = ps.prepararPersistenciaJSON(ctx, programaGravar);
                if(!p.getCliente().getProfessorSintetico().getCodigo().equals(p.getProfessorCarteira().getCodigo())){
                    ProfessorSintetico profAntesAlteracao = p.getCliente().getProfessorSintetico();
                    p.getCliente().setProfessorSintetico(p.getProfessorCarteira());
                    cs.alterar(ctx,  p.getCliente());
                    cs.inserirLogAlteracaoProfessorAluno(ctx, profAntesAlteracao, p.getCliente().getProfessorSintetico(), p.getCliente().getMatricula(), "AÇÃO ProgramaTreinoJSONControle");
                }
                p = ps.gravarProgramaSemTratarExcecao(ctx, p, usuario.getProfessor());
                ProgramaWriteJSON json = new ProgramaWriteJSON(p);
                json.preencherObjetivosPrograma(p);
                json.preencherProgramaTreinoFichas(p);
                json.setUsername(programaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", "") );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importar-programas-by-dto", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarProgramas(@PathVariable String ctx, @RequestBody final List<ProgramaFichaJSON> programaGravar) {
        ModelMap mm = new ModelMap();
        try {
            String ret = ps.importarProgramasJson(ctx, programaGravar);
            mm.addAttribute(STATUS_SUCESSO, ret);
        } catch (ValidacaoException vex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", ""));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/excluir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap excluir(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if (p != null) {
                    ps.excluir(ctx, p, username, true);
                    mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                } else {
                    throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/revisar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap revisar(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma, @RequestParam final String dataProximaRevisao,
            @RequestParam final String justificativa) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                p.manterAntesAlteracao();
                Date proximaRevisao = Calendario.getDate(Calendario.MASC_DATA, dataProximaRevisao);
                ps.gravarHistoricoRevisoes(ctx, p, justificativa, proximaRevisao, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.revisado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ultimoPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimoPrograma(@PathVariable final String ctx,
            @RequestParam final String username, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("ultimoPrograma");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)) {
                List<ProgramaTreino> lista = ps.obterProgramasPorCliente(ctx,
                        codigoCliente, null, null, null, 1);
                ProgramaTreino prog = lista != null && !lista.isEmpty() ? lista.get(0) : null;
                if (prog != null) {
                    ProgramaWriteJSON json = new ProgramaWriteJSON(prog);
                    json.preencherObjetivosPrograma(prog);
                    json.preencherProgramaTreinoFichas(prog);
                    json.setUsername(username);
                    json.setFrequencia(obterAndamento(ctx, prog));
                    mm.addAttribute(RETURN, json);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarUltimoPrograma", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarUltimoProgramaApp(@PathVariable final String ctx,
                            @RequestParam final String userName, @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        try {
            acao("ultimoPrograma");
            Usuario usuario = us.obterPorAtributo(ctx, "username", userName);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)) {
                List<ProgramaTreino> lista = ps.obterProgramasPorCliente(ctx,
                        codigoAluno, null, null, null, 1);
                ProgramaTreino prog = lista != null && !lista.isEmpty() ? lista.get(0) : null;
                if (prog != null) {
                    ps.atualizarNrTreinosRealizados(ctx, prog.getCodigo());
                    ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(prog);
                    json.preencherObjetivosPrograma(prog);
                    final String urlBase = TemaAtividade.getURLBase(ctx);
                    json.preencherProgramaTreinoFichas(prog, urlBase);
                    json.setUsername(userName);
                    json.setFrequencia(obterAndamento(ctx, prog));
                    mm.addAttribute("sucesso", json);
                }
                lista = null;
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/historicoExecucoes", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historicoExecucoes(@PathVariable final String ctx,
            @RequestParam final Integer codigoCliente,
            @RequestParam final Integer maxResults,
            @RequestParam final Integer index) {
        ModelMap mm = new ModelMap();
        try {
            if (codigoCliente != null && codigoCliente > 0) {
                List<TreinoRealizado> treinosRealizados = ps.obterTreinosRealizado(ctx, null,
                        Calendario.anterior(Calendar.YEAR, Calendario.hoje()),
                        Calendario.proximo(Calendar.DAY_OF_MONTH, Calendario.hoje()),
                        codigoCliente, maxResults, index);
                List<TreinoRealizadoHistoricoJSON> arrJSON = new ArrayList<TreinoRealizadoHistoricoJSON>();
                for (TreinoRealizado tr : treinosRealizados) {
                    TreinoRealizadoHistoricoJSON trHist = new TreinoRealizadoHistoricoJSON(
                            tr.getCodigo(),
                            tr.getProgramaTreinoFicha().getPrograma().getNome(),
                            tr.getDataInicio(), tr.getProgramaTreinoFicha().getFicha().getNome(),
                            (tr.getProfessor() != null ? tr.getProfessor().getNome() : ""), tr.getProgramaTreinoFicha().getFicha().getAtividades().size());
                    arrJSON.add(trHist);
                }
                mm.addAttribute(RETURN, arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ultimaAtualizacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimaAtualizacao(@PathVariable String ctx,
            @RequestParam final String dataAtual,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
            p.setDataUltimaAtualizacao(Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual));
            ps.alterar(ctx, p);
            mm.addAttribute(RETURN, STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/lembreteCompromisso", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembreteCompromisso(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            TaskCompromissoTreino task = new TaskCompromissoTreino(ctx);
            task.execute(null);
            mm.addAttribute(RETURN, "Processo de lembrete treino executado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/renovar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap renovar(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                ps.renovarProgramaTreino(ctx, p, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.renovado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, vex.getMensagens() );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/programaultimasexecucoes", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimasExcucoesFichasPrograma(@PathVariable final String ctx, @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Map<Integer, String> array = ps.obterUltimosTreinosPrograma(ctx, codigoPrograma);
            mm.addAttribute(STATUS_SUCESSO, array);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/andamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap andamentoPrograma(@PathVariable final String ctx, @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            ProgramaTreinoAndamento andamento = ps.consultarAndamentoSimples(ctx, codigoPrograma);
            mm.addAttribute("nrTreinos", andamento.getNrTreinos());
            mm.addAttribute("totalAulasPrevistas", andamento.getTotalAulasPrevistas());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        List<ProgramaTreinoJSON> arrJSON = new ArrayList();

        try {
            acao("ProgramaTreinoWriteJSON.sync");
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<ProgramaTreino> programas = syncs.obterLista(ctx, TipoClassSincronizarEnum.ProgramaTreino, dataBase);
            for (ProgramaTreino programaTreino : programas) {
                if (programaTreino.getCliente() != null) {
                    Usuario usuario = us.consultarPorCliente(ctx, programaTreino.getCliente().getCodigo());
                    ProgramaTreino programaVigente = ps.obterUltimoProgramaVigente(ctx, programaTreino.getCliente());
                    ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programaVigente, ctx, null, configSeriesSet.getValorAsBoolean(), "", null);
                    programaJSON.setUserName(usuario.getUserName());
                    obterClienteMensagemAviso(ctx, programaTreino, programaJSON);
                    arrJSON.add(programaJSON);
                } else {
                    ps.excluirHistoricoRevisaoSincronizacao(ctx, programaTreino);
                }
            }
            mm.addAttribute(RETURN, arrJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    public ProgramaTreinoService getProgramaTreinoService() {
        return (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
    }

    @RequestMapping(value = "{ctx}/app/consultarProgramaBase", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarProgramaBaseApp(@PathVariable String ctx,
                                  @RequestParam final String userName, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("default");
            Usuario usuario = us.obterPorAtributo(ctx, "username", userName);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.INCLUIR);
                ClienteSintetico cli = cs.obterPorId(ctx, codigoCliente);
                if (cli != null) {
                    ProgramaTreino programa = ps.gerarProgramaDefault(ctx, cli, usuario, null, null);
                    ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(programa);
                    json.setUsername(userName);
                    mm.addAttribute("sucesso", json);
                } else {
                    throw new ValidacaoException("cliente.naoencontrado.treino");
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarHistoricoExecs", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarHistoricoExecsApp(@PathVariable final String ctx,
                                @RequestParam final Integer codigoCliente,
                                @RequestParam final Integer maxResults,
                                @RequestParam final Integer index) {
        ModelMap mm = new ModelMap();
        try {
            if (codigoCliente != null && codigoCliente > 0) {
                List<TreinoRealizado> treinosRealizados = ps.obterTreinosRealizado(ctx, null,
                        Calendario.anterior(Calendar.YEAR, Calendario.hoje()),
                        Calendario.proximo(Calendar.DAY_OF_MONTH, Calendario.hoje()),
                        codigoCliente, maxResults, index);
                List<TreinoRealizadoHistoricoAppJSON> arrJSON = new ArrayList<TreinoRealizadoHistoricoAppJSON>();

                for (TreinoRealizado tr : treinosRealizados) {
                    Integer codigo = null;
                    String nomePrograma = null;
                    String nomeFicha = null;
                    String nomeProfessor = null;
                    Integer nrAtividades = null;
                    Integer nrAtividdesConcluidas = null;
                    String nomeNivel = null;
                    Timestamp datainIcio = null;
                    if(tr != null)
                    {
                        codigo = tr.getCodigo();
                        nomeProfessor = tr.getNomeProfessor();
                        datainIcio = tr.getDataInicio() != null ? new Timestamp(tr.getDataInicio().getTime()) : null;
                        if(tr.getProgramaTreinoFicha() != null)
                        {
                            nomePrograma = tr.getProgramaTreinoFicha().getPrograma() != null ?
                                    tr.getProgramaTreinoFicha().getPrograma().getNome() : null;
                            nomeFicha = tr.getProgramaTreinoFicha().getNomeFicha();

                            if(tr.getProgramaTreinoFicha().getFicha() != null) {
                                Ficha ficha = tr.getProgramaTreinoFicha().getFicha();
                                List<AtividadeFicha> atividadeFichas = fichaService.obterAtividadesFicha(ctx, ficha.getCodigo());
                                nrAtividades = atividadeFichas.size();
                                nomeNivel = ficha.getNivel() != null ? ficha.getNivel().getNome() : null;
                            }
                        }
                        nrAtividdesConcluidas = ps.obterNumeroAtividadesRealizadas(ctx, codigo);
                    }

                    TreinoRealizadoHistoricoAppJSON trHist = new TreinoRealizadoHistoricoAppJSON(
                            codigo,
                            nomePrograma,
                            nomeFicha,
                            nomeProfessor,
                            nrAtividades,
                            nrAtividdesConcluidas,
                            nomeNivel,
                            datainIcio);

                    arrJSON.add(trHist);
                }
                mm.addAttribute("sucesso", arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/removerPrograma", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap removerProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if (p != null) {
                    ps.excluir(ctx, p, username, true);
                    mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                } else {
                    throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/revisaoPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap revisaoProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma, @RequestParam final String justificativa) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if(p == null)
                {
                    throw new ServiceException("Programa não encontrado");
                }
                p.manterAntesAlteracao();
                ps.gravarHistoricoRevisoes(ctx, p, justificativa, null, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.revisado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/persistirPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistirProgramaApp(@PathVariable String ctx, @RequestBody final ProgramaWriteAppJSON programaGravar,
                                  @RequestParam (required = false, defaultValue = "1") Integer origem) {
        ModelMap mm = new ModelMap();
        try {
            acao("persistir");
            Usuario usuario = us.obterPorAtributo(ctx, "username", programaGravar.getUsername());
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);

                ProgramaTreino p = ps.prepararPersistenciaAppJSON(ctx, programaGravar, origem);
                if(p == null)
                {
                    throw new ServiceException("Programa de treino não localizado");
                }
                if(p.getCliente().getProfessorSintetico() != null && !p.getCliente().getProfessorSintetico().getCodigo().equals(p.getProfessorCarteira().getCodigo())){
                    ProfessorSintetico profAntesAlteracao = p.getCliente().getProfessorSintetico();
                    p.getCliente().setProfessorSintetico(p.getProfessorCarteira());
                    cs.alterar(ctx,  p.getCliente());
                    cs.inserirLogAlteracaoProfessorAluno(ctx, profAntesAlteracao, p.getCliente().getProfessorSintetico(), p.getCliente().getMatricula(), "AÇÃO ProgramaTreinoJSONControle");
                }
                p = ps.gravarProgramaSemTratarExcecao(ctx, p, usuario.getProfessor());
                ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(p);
                ps.obterObjetivosDoPrograma(ctx, p);
                json.preencherObjetivosPrograma(p);
                final String urlBase = TemaAtividade.getURLBase(ctx);
                p.setProgramaFichas(ps.obterProgramasFichasPorPrograma(ctx, p.getCodigo()));
                json.preencherProgramaTreinoFichas(p, urlBase);
                json.setUsername(programaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", "") );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/renovarPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap renovarProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                ps.obterObjetivosDoPrograma(ctx, p);
                ps.renovarProgramaTreino(ctx, p, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("Programa renovado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, vex.getMensagens() );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    public ProgramaTreinoAndamento getProgramaTreinoAndamento() {
        return programaTreinoAndamento;
    }

    public void setProgramaTreinoAndamento(ProgramaTreinoAndamento programaTreinoAndamento) {
        this.programaTreinoAndamento = programaTreinoAndamento;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido/franqueadora", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidosSlimFranqueadora(@RequestParam String nome,
                                                                                              @RequestParam String chaveRede) throws JSONException {
        try {
            return ResponseEntityFactory.ok(ps.obterProgramasPreDefinidosSlim(false, nome, chaveRede, true));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/franqueadora/{id}/{chaveFranqueadora}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreinoFranqueadora(@PathVariable("id") final Integer id,
                                                                                   @PathVariable("chaveFranqueadora") final String chaveFranqueadora) {
        try {
            return ResponseEntityFactory.ok(ps.consultarProgramaTreino(id, chaveFranqueadora));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/aulasAgendadasPorAluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulasAgendadasPorAluno(@PathVariable String ctx,
                                                                               @PathVariable("matricula") final Integer matricula,
                                                                               @RequestHeader("empresaId") Integer empresaId,
                                                                               @RequestParam (defaultValue = "01/01/2001") String dataInicio, @RequestParam String dataFim) {
        try {
            return ResponseEntityFactory.ok(ps.consultarAulasAgendadasPorAluno(matricula, dataInicio, dataFim, ctx, null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/v2/aulasAgendadasPorAluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulasAgendadasPorAlunoV2(@PathVariable String ctx,
                                                                               @PathVariable("matricula") final Integer matricula,
                                                                               @RequestParam (defaultValue = "01/01/2001") String dataInicio, @RequestParam String dataFim,
                                                                               @RequestParam (required = false) Integer contrato) {
        try {
            return ResponseEntityFactory.ok(ps.consultarAulasAgendadasPorAlunoV2(matricula, dataInicio, dataFim, ctx, contrato));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/listar-predefinidos/{ctx}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPredefinidosFull(@PathVariable("ctx") final String ctx) throws JSONException {
        try {
            return ResponseEntityFactory.ok(ps.obterProgramasPreDefinidosPorChave(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pendentes/{ctx}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTreinoPorIA(@PathVariable("ctx") final String ctx) {
        try {
            List<ConsultaTreinoDTO> treinos = ps.obterProgramaTreinoGeradoPorIA(ctx);
            return ResponseEntityFactory.ok(treinos);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas de treino gerador por I.A.", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/criaProgramaTreinoPorIA", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<EnvelopeRespostaDTO> criaProgramaTreinoPorIA(
            @PathVariable String ctx,
            @RequestBody AnamneseTreinoPorIADTO anamneseTreinoPorIADTO) {
        try {
            return ResponseEntityFactory.ok(ps.preparaProgramaTreinoPorIA(anamneseTreinoPorIADTO, ctx));
        } catch (Exception e) {
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, "Erro ao criar programa por IA", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/app/excluiProgramaTreinoPorId", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluiProgramaTreinoPorId(@PathVariable String ctx,
                                                                         @RequestParam Integer codigoPrograma) {
        try {
            ps.excluirProgramaTreino(codigoPrograma, ctx);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }
}
