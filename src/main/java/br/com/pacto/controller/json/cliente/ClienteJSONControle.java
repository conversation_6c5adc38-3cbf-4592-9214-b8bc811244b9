/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.cliente.ClienteObservacaoJSON;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.PerfilDISCDTO;
import br.com.pacto.controller.json.aluno.AlunoCadastroSimplesDTO;
import br.com.pacto.controller.json.aluno.AlunoController;
import br.com.pacto.controller.json.aluno.AlunoDTO;
import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import br.com.pacto.controller.json.aluno.HistoricoContatoAlunoVO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.ClienteDadosTotalPassDTO;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.controller.json.usuario.UsuarioJSONControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteObservacaoService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.json.ColetorJSON;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
@Controller
@RequestMapping("/cliente")
public class ClienteJSONControle extends SuperControle {

    private final ClienteSinteticoService clienteSinteticoService;
    private final ClienteObservacaoService clienteObservacaoService;
    private final UsuarioService usuarioService;
    private final FotoService fotoService;

    @Autowired
    public ClienteJSONControle(ClienteSinteticoService clienteSinteticoService, ClienteObservacaoService clienteObservacaoService,
                               UsuarioService usuarioService, FotoService fotoService) {
        this.clienteSinteticoService = clienteSinteticoService;
        this.clienteObservacaoService = clienteObservacaoService;
        this.usuarioService = usuarioService;
        this.fotoService = fotoService;
    }

    @RequestMapping(value = "{ctx}/atualizarFrequenciaSemanal", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarFrequenciaSemanal(@PathVariable String ctx,
                                        @RequestParam String dadosJSON) {
        ModelMap mm = new ModelMap();
        try {
            JSONArray lista = new JSONArray(dadosJSON);
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                String sql = "update ClienteSintetico set frequenciaSemanal = " + obj.getInt("frequenciaSemanal") + " where codigoCliente in (" + obj.getString("codigosClientes") + ")";
                clienteSinteticoService.executeNativeSQL(ctx, sql);
            }
            mm.addAttribute(RETURN, "Sucesso");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/observacoes", method = RequestMethod.POST)
    public ModelMap observacoes(@PathVariable String contexto, @RequestParam("matricula") String matriculaCliente,
                                HttpServletRequest httpRequest) {
        ModelMap modelMap = new ModelMap();
        try {
            final List<ClienteObservacao> observacoes =
                    clienteObservacaoService.consultarObservacoesPorMatriculaCliente(contexto, matriculaCliente);

            modelMap.addAttribute(RETURN, mapearObservacoes(contexto, observacoes, httpRequest));

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/gravarobservacao", method = RequestMethod.POST)
    public ModelMap gravarObservacao(@PathVariable String contexto,
                                     @RequestParam final String userName,
                                     @RequestParam String matricula,
                                     @RequestParam String observacao,
                                     @RequestParam final Boolean importante,
                                     HttpServletRequest request) {
        ModelMap modelMap = new ModelMap();
        try {
            final Usuario usuario = usuarioService.obterPorAtributo(contexto, "username", userName);
            if (usuario == null) {
                return reportarErro(modelMap, "mobile.usuarioinvalido");
            }
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(contexto, matricula);
            ClienteObservacao clienteObservacao = new ClienteObservacao();
            clienteObservacao.setCliente(cliente);
            clienteObservacao.setDataObservacao(Calendario.hoje());
            clienteObservacao.setUsuario_codigo(usuario.getCodigo());
            clienteObservacao.setImportante(importante);
            clienteObservacao.setObservacao(observacao);
            if (UteisValidacao.emptyString(observacao)) {
                return reportarErro(modelMap, "É necessário preencher a observação.");
            }
            clienteSinteticoService.gravarObservacaoCliente(contexto, clienteObservacao);
            modelMap.addAttribute(RETURN, mapearObservacao(contexto, request, clienteObservacao, usuario));
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/observacoes/delete/{id}", method = RequestMethod.DELETE)
    public ModelMap excluir(@PathVariable("contexto") String contexto, @PathVariable("id") Integer idObservacao) {
        ModelMap modelMap = new ModelMap();
        try {
            clienteObservacaoService.excluir(contexto, idObservacao);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    private List<Map<String, Object>> mapearObservacoes(String contexto, List<ClienteObservacao> observacoes,
                                                        HttpServletRequest request) throws Exception {
        final List<Map<String, Object>> observacoesMapeadas = new ArrayList<Map<String, Object>>();
        for (final ClienteObservacao clienteObservacao : observacoes) {
            Usuario usu = usuarioService.obterPorId(contexto, clienteObservacao.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            observacoesMapeadas.add(mapearObservacao(contexto, request, clienteObservacao, usu));
        }
        return observacoesMapeadas;
    }

    private Map<String, Object> mapearObservacao(String contexto, HttpServletRequest request, ClienteObservacao clienteObservacao, Usuario usu) throws Exception {
        final Map<String, Object> observacaoMapeada = new HashMap<String, Object>();
        // dados da observacao
        observacaoMapeada.put("codigo", clienteObservacao.getCodigo());
        observacaoMapeada.put("observacao", clienteObservacao.getObservacao());
        observacaoMapeada.put("data", clienteObservacao.getDataObservacaoApresentar());
        observacaoMapeada.put("dataLong", clienteObservacao.getDataObservacao().getTime());
        observacaoMapeada.put("importante", clienteObservacao.getImportante());

        // dados do professor
        observacaoMapeada.put("nome", usu.getNomeApresentar());
        observacaoMapeada.put("username",usu.getUserName());
        observacaoMapeada.put("srcImg", fotoService.carregarFoto(contexto, usu, false, request));
        return observacaoMapeada;
    }

    @RequestMapping(value = "{ctx}/consultarLocaisAcesso", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarLocaisAcesso(@PathVariable String ctx,
                                   @RequestParam Integer empresa) {

        ModelMap mm = new ModelMap();
        try {
            List<ColetorJSON> lista = clienteSinteticoService.consultarLocaisAcesso(ctx, empresa);
            mm.addAttribute("locaisAcesso", lista);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarLocalAcessoPorNFC", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarLocalAcessoPorNFC(@PathVariable String ctx,
                                        @RequestParam String nfc) {

        ModelMap mm = new ModelMap();
        try {
            ColetorJSON local = clienteSinteticoService.consultarLocalAcessoPorNFC(ctx, nfc);
            mm.addAttribute(local);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarObservacoesDeAluno", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarObservacoesDeAlunoApp(@PathVariable String ctx,
                                            @RequestParam String matriculaAluno,
                                            @RequestParam(required = false) Integer index,
                                            @RequestParam(value = "max", required = false) Integer maxResult,
                                            HttpServletRequest request){

        ModelMap modelMap = new ModelMap();
        try {
            final List<ClienteObservacao> observacoes =
                    clienteObservacaoService.consultarObservacoesPorMatriculaClienteApp(ctx, matriculaAluno, maxResult == null ? 0: maxResult, index == null ? 0 : index);

            List<ClienteObservacaoJSON> observacaoJSONS = new ArrayList<ClienteObservacaoJSON>();
            for(ClienteObservacao co: observacoes)
            {   Usuario usu = usuarioService.obterPorId(ctx, co.getUsuario_codigo());
                ClienteObservacaoJSON observacaoJSON = new ClienteObservacaoJSON(co, usu);
                String foto = null;
                if(co.getUsuario_codigo() != null)
                {
                    foto = usu.getFotoKeyApp();
                    if(UteisValidacao.emptyString(foto))
                    {
                        foto = fotoService.defineURLFotoPessoa(request, "", usu.getIdPessoa(), true, ctx, true);
                    }
                }
                observacaoJSON.setFoto(foto);
                observacaoJSONS.add(observacaoJSON);
            }
            modelMap.addAttribute("sucesso", observacaoJSONS);

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
        }
        return modelMap;
    }


    @RequestMapping(value = "{ctx}/app/removerObservacao", method = RequestMethod.DELETE)
    public @ResponseBody ModelMap removerObservacaoApp(@PathVariable String ctx, @RequestParam Integer codigoObservacao) {
        ModelMap modelMap = new ModelMap();
        try {
            clienteObservacaoService.excluir(ctx, codigoObservacao);
        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        modelMap.addAttribute(STATUS_SUCESSO, "Observação removida com sucesso");
        return modelMap;
    }

    @RequestMapping(value = "{ctx}/app/consultarQuantidadeAcessosClientesAgrupadosDia", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarQuantidadeAcessosClientesAgrupadosDia(@PathVariable String ctx,
                                                            @RequestParam Integer codigoAluno,
                                                            @RequestParam final String username,
                                                            @RequestParam Long dataInicial,
                                                            @RequestParam Long dataFinal) throws ServiceException {
        ModelMap modelMap = new ModelMap();
        ModelMap resultado = new ModelMap();

        if (SuperControle.independente(ctx)) {
            resultado.addAttribute(RETURN, clienteSinteticoService.consultarTreinosRealizadosPorDiaSemana(ctx, dataInicial, dataFinal, codigoAluno, username));
        } else {
            try {
                IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                JSONObject json = new JSONObject(integracaoWS.consultarQuantidadeAcessosClientesAgrupadosDia(url, ctx, codigoAluno, dataInicial, dataFinal));

                if (json.has("acessosDiarios")) {
                    JSONObject frequencia = (JSONObject) json.get("acessosDiarios");
                    modelMap.addAttribute("SEG", frequencia.has("SEG") ? frequencia.get("SEG") : 0);
                    modelMap.addAttribute("TER", frequencia.has("TER") ? frequencia.get("TER") : 0);
                    modelMap.addAttribute("QUA", frequencia.has("QUA") ? frequencia.get("QUA") : 0);
                    modelMap.addAttribute("QUI", frequencia.has("QUI") ? frequencia.get("QUI") : 0);
                    modelMap.addAttribute("SEX", frequencia.has("SEX") ? frequencia.get("SEX") : 0);
                    modelMap.addAttribute("SAB", frequencia.has("SAB") ? frequencia.get("SAB") : 0);
                    modelMap.addAttribute("DOM", frequencia.has("DOM") ? frequencia.get("DOM") : 0);
                    resultado.addAttribute(STATUS_SUCESSO, modelMap);
                } else {
                    resultado.addAttribute(STATUS_ERRO, "Não foi possivel obter a frequência do aluno.");
                }
            } catch (Exception ex) {
                resultado.addAttribute(STATUS_ERRO, ex.getMessage());
            }
        }
        return resultado;
    }

    @RequestMapping(value = "{ctx}/simplificado", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreino(@PathVariable String ctx, @RequestBody AlunoCadastroSimplesDTO alunoDTO) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, clienteSinteticoService.cadastrarAlunoPersonalFit(new AlunoDTO(alunoDTO), ctx));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizar-dados-cliente-sintetico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sincronizarDadosClienteSintetico(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.sincronizarDadosClienteSintetico(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizar-matricula-clientesintetico/{empresaZW}", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sincronizarMatriculaClienteSintetico(@PathVariable String ctx, @PathVariable Integer empresaZW) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.sincronizarMatriculaClienteSintetico(ctx, empresaZW));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital/{matricula}/{empresa}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAluno(@PathVariable String ctx, @PathVariable String matricula, @PathVariable String empresa, @RequestParam(value = "validar", required = false) Boolean validar) {
        ModelMap mm = new ModelMap();
        try {
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                return mm.addAttribute(RETURN, clienteSinteticoService.buscarAssinaturaDigitalContratosAluno(ctx, matricula, empresa, validar));
            }
            return mm.addAttribute(RETURN, "[]");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/rB3mU7oX8eS4eA6iC4lH9eY2fU6sF5kR", method = RequestMethod.PATCH)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String matricula = o.optString("matricula");
            String empresa = o.optString("empresa");
            boolean validar = o.optBoolean("validar");

            String returnAssinatura = "[]";
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                returnAssinatura = clienteSinteticoService.buscarAssinaturaDigitalContratosAluno(ctx, matricula, empresa, validar);
            }

            mm.addAttribute(RETURN, Uteis.encryptUserData(returnAssinatura));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital-by-contrato/{contrato}/{empresa}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoByContrato(@PathVariable String ctx, @PathVariable String contrato, @PathVariable String empresa) {
        ModelMap mm = new ModelMap();
        try {
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                Logger.getLogger("CONSULTA_CONTRATO_ASSINATURA").log(Level.SEVERE, null, "teste");
                return mm.addAttribute(RETURN, clienteSinteticoService.buscarAssinaturaDigitalContratosAlunoByContrato(ctx, contrato, empresa));
            }
            return mm.addAttribute(RETURN, "[]");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/kC8zT9lL2xH6qG9wB0mP9vR1mM2bA7aE", method = RequestMethod.PATCH)
    public @ResponseBody
    ModelMap assinaturaDigitalContratosAlunoByContratoCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String contrato = o.optString("contrato");
            String empresa = o.optString("empresa");

            String returnAssinatura = "[]";
            if(Aplicacao.isTrue(Aplicacao.consultaContratoAssinaturaDigital)){
                Logger.getLogger("CONSULTA_CONTRATO_ASSINATURA").log(Level.SEVERE, null, "teste");
                returnAssinatura = clienteSinteticoService.buscarAssinaturaDigitalContratosAlunoByContrato(ctx, contrato, empresa);
            }

            mm.addAttribute(RETURN, Uteis.encryptUserData(returnAssinatura));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/aluno-contrato-assinatura-digital-incluir/{contrato}", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap incluirAssinaturaDigitalContratoAluno(@PathVariable String ctx, @PathVariable String contrato,@RequestBody String assinatura) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, clienteSinteticoService.incluirAssinaturaDigitalContratoAluno(ctx, contrato, assinatura));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacoes-professores", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessores (
            @PathVariable String ctx,
            @RequestBody List<AvaliacaoProfessorDTO> avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.avaliaProfessores(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessor (
            @PathVariable String ctx,
            @RequestBody AvaliacaoProfessorDTO avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.avaliaProfessor(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/v2/avaliacao-professor", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliaProfessorAPPTreino (
            @PathVariable String ctx,
            @RequestBody AvaliacaoProfessorDTO avaliacaoProfessorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.novaAvaliacaoProfessor(avaliacaoProfessorDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor-por-cliente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaAvaliacaoPorCliente (
            @PathVariable String ctx,
            @RequestParam Integer codUsuario
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscaAvaliacaoCliente(codUsuario, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/avaliacao-professor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaAvaliacao (
            @PathVariable String ctx,
            @RequestParam(required = false) String codProfessores
    ) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscaAvaliacao(codProfessores, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/autoriza-acesso-totalpass", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> autorizaAcessoTotalPass(
            @PathVariable String ctx,
            @RequestBody ClienteDadosTotalPassDTO clienteDadosTotalPassDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.autorizaAcessoTotalPass(clienteDadosTotalPassDTO, ctx));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/cliente-acompanhamento", method = {RequestMethod.POST, RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaClienteAcompanhamento (@PathVariable String ctx,
                                                                           @RequestParam String dtInicio,
                                                                           @RequestParam String dtFim) {
        try {
            Date dtInicioDate = Calendario.getDate("yyyyMMdd", dtInicio);
            Date dtFimDate = Calendario.getDate("yyyyMMdd", dtFim);
            return ResponseEntityFactory.ok(clienteSinteticoService.consultarClienteAcompanhamentoJSON(ctx, dtInicioDate, dtFimDate));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
    @ResponseBody
    @RequestMapping(value = "{contexto}/historico-contato", method = RequestMethod.GET)
    public ModelMap historicoContato(@PathVariable String contexto, @RequestParam("matricula") String matriculaCliente,
                                @RequestParam(required = false, defaultValue = "true") Boolean contatoCRM,
                                @RequestParam(required = false, defaultValue = "true") Boolean observacoes,
                                HttpServletRequest httpRequest) {
        ModelMap modelMap = new ModelMap();
        try {
            final List<HistoricoContatoAlunoVO> historicoContatoAlunoVOS =
                    clienteObservacaoService.consultarHistoricoDeContatos(contexto, matriculaCliente, contatoCRM, observacoes);

            modelMap.addAttribute(RETURN, historicoContatoAlunoVOS);

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ClienteJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return modelMap;
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarPerfilDISC(@PathVariable String ctx, @RequestBody PerfilDISCDTO perfilDISCDTO) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.salvarPerfilDISC(ctx, perfilDISCDTO));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarPerfilDISC(@PathVariable String ctx, @RequestParam Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.buscarPerfilDISC(ctx, matricula));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/perfilDISC", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarPerfilDISC(@PathVariable String ctx, @RequestBody PerfilDISCDTO perfilDISCDTO, @RequestParam Integer codigo) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.atualizarPerfilDISC(ctx, perfilDISCDTO, codigo));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/retrospectiva", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> retrospectiva(@PathVariable String ctx, @RequestParam Integer ano, @RequestParam Integer matricula,
                                                             @RequestParam(required = false, defaultValue = "false") Boolean atualizaCache) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterRetrospectiva(ctx, ano, matricula, atualizaCache));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/pontos-saldo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pontosSaldoBrindes(@PathVariable String ctx, @RequestParam Integer matricula) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterPontosSaldo(ctx, matricula));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/brindes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> brindes(@PathVariable String ctx, @RequestParam(required = false, defaultValue = "true") Boolean ativo) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterBrindes(ctx, ativo));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/historico-presenca", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dadosAulas(@PathVariable String ctx, @RequestParam Integer matricula,
                                                          @RequestParam Integer empresa, @RequestParam (defaultValue = "false") Boolean atualizaCache) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.historicoPresenca(ctx, matricula, empresa, atualizaCache));
        } catch (Exception e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao carregar linha de tempo do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}

