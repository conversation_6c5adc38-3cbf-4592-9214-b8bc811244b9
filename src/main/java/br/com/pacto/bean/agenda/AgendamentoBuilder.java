package br.com.pacto.bean.agenda;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.agendamento.AgendamentoDTO;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.HorarioDisponibilidadeService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.util.UtilContext;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by paulo 31/10/2018
 */
public class AgendamentoBuilder {

    public static Agendamento agendamentoDtoToAgendamento(String ctx, AgendamentoDTO agendamentoDTO) throws ServiceException, ParseException {
        Agendamento agendamento = new Agendamento();

        ClienteSintetico clienteSintetico =
                ((ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class)).obterPorCodigo(ctx, agendamentoDTO.getAlunoId());
        agendamento.setCliente(clienteSintetico);

        ProfessorSintetico professorSintetico =
                    ((ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class)).consultarPorCodigoColaborador(ctx, agendamentoDTO.getProfessor());

        agendamento.setProfessor(professorSintetico);

        HorarioDisponibilidade horarioDisponibilidade = null;
        if(agendamentoDTO.getTipoEvento() != null && !agendamentoDTO.getTipoEvento() && agendamentoDTO.getHorarioDisponibilidadeCod() != null) {
            horarioDisponibilidade = ((HorarioDisponibilidadeService) UtilContext.getBean(HorarioDisponibilidadeService.class)).obterPorId(ctx, agendamentoDTO.getHorarioDisponibilidadeCod());
            agendamento.setHorarioDisponibilidade(horarioDisponibilidade);
        }

        if(horarioDisponibilidade == null) {
            TipoEvento tipoEvento = ((TipoEventoService) UtilContext.getBean(TipoEventoService.class)).obterPorId(ctx, agendamentoDTO.getTipo());
            agendamento.setTipoEvento(tipoEvento);
        }

        edicaoDtoToAgendamento(agendamentoDTO, agendamento);

        return agendamento;
    }

    public static Agendamento edicaoDtoToAgendamento(AgendamentoDTO agendamentoDTO, Agendamento agendamento) throws ParseException {
        if(agendamentoDTO.getData() == null){
            agendamentoDTO.setData(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "yyyyMMdd"));
        }
        if(agendamentoDTO.getHorarioInicial() == null){
            agendamentoDTO.setHorarioInicial(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "HH:mm"));
        }
        if(agendamentoDTO.getHorarioFinal() == null){
            agendamentoDTO.setHorarioFinal(Uteis.getDataAplicandoFormatacao(agendamento.getFim(), "HH:mm"));
        }
        Map<String, Date> horarios = getHorarios(Calendario.getDate("yyyyMMdd", agendamentoDTO.getData()), agendamentoDTO.getHorarioInicial(), agendamentoDTO.getHorarioFinal());

        agendamento.setInicio(horarios.get("inicio"));
        if(agendamentoDTO.getHorarioFinal().length() >0){
            agendamento.setFim(horarios.get("fim"));
        }
        agendamento.setObservacao(agendamentoDTO.getObservacao());
        agendamento.setStatus(agendamentoDTO.getStatus());
        agendamento.setDiaSemana(Calendario.getInstance(horarios.get("inicio")).get(Calendar.DAY_OF_WEEK));

        return agendamento;
    }

    private static Map<String, Date> getHorarios(Date dia, String horaInicio, String horaFim) {
        Map<String, Date> ret = new HashMap<>();
        int hora = Integer.parseInt(horaInicio.substring(0,2));
        int minuto = Integer.parseInt(horaInicio.substring(3,5));
        Calendar dataInicial = Calendar.getInstance();
        dataInicial.setTime(Calendario.getDataComHoraZerada(dia));
        dataInicial.set(Calendar.HOUR, hora);
        dataInicial.set(Calendar.MINUTE, minuto);
        ret.put("inicio", dataInicial.getTime());

        if(horaFim.length()>0){
            hora = Integer.parseInt(horaFim.substring(0,2));
            minuto = Integer.parseInt(horaFim.substring(3,5));
            Calendar dataFinal = Calendar.getInstance();
            dataFinal.setTime(Calendario.getDataComHoraZerada(dia));
            dataFinal.set(Calendar.HOUR, hora);
            dataFinal.set(Calendar.MINUTE, minuto);
            ret.put("fim", dataFinal.getTime());
        }
        return ret;
    }

    public static Agendamento agendamentoDtoToAgendamento(String ctx, AgendamentoPersonalDTO agendamentoDTO) throws ServiceException, ParseException {
        Agendamento agendamento = new Agendamento();

        ClienteSintetico clienteSintetico = UtilContext.getBean(ClienteSinteticoService.class).consultarPorMatricula(ctx, String.valueOf(agendamentoDTO.getMatricula()));
        agendamento.setCliente(clienteSintetico);

        ProfessorSintetico professorSintetico;
        if (SuperControle.independente(ctx)) {
            professorSintetico =
                    UtilContext.getBean(ProfessorSinteticoService.class).obterPorId(ctx, agendamentoDTO.getProfessorId());
        } else {
            professorSintetico =
                    UtilContext.getBean(ProfessorSinteticoService.class).consultarPorCodigoColaborador(ctx, agendamentoDTO.getProfessorId());
        }
        agendamento.setProfessor(professorSintetico);
        // horarioDisponibilidadeCod e tipoEvento não é passado no body para o AgendamentoPersonalDTO, o que faz procurar por uma disponibilidade/agendamento antiga
        HorarioDisponibilidade horarioDisponibilidade = null;
        if(agendamentoDTO.getTipoEvento() != null && !agendamentoDTO.getTipoEvento()  && agendamentoDTO.getHorarioDisponibilidadeCod() != null) {
            horarioDisponibilidade = ((HorarioDisponibilidadeService) UtilContext.getBean(HorarioDisponibilidadeService.class)).obterPorId(ctx, agendamentoDTO.getHorarioDisponibilidadeCod());
            agendamento.setHorarioDisponibilidade(horarioDisponibilidade);
        }

        if(horarioDisponibilidade == null) {
            TipoEvento tipoEvento = UtilContext.getBean(TipoEventoService.class).obterPorId(ctx, agendamentoDTO.getTipoAgendamentoId());
            agendamento.setTipoEvento(tipoEvento);
        }

        edicaoDtoToAgendamento(agendamentoDTO, agendamento);

        return agendamento;
    }

    public static Agendamento edicaoDtoToAgendamento(AgendamentoPersonalDTO agendamentoDTO, Agendamento agendamento) throws ParseException {
        Map<String, Date> horarios = getHorarios(Calendario.getDate("dd/MM/yyyy", agendamentoDTO.getDia()), agendamentoDTO.getHorarioInicial(), agendamentoDTO.getHorarioFinal());

        agendamento.setInicio(horarios.get("inicio"));
        agendamento.setFim(horarios.get("fim"));
        agendamento.setObservacao(agendamentoDTO.getObservacao());
        agendamento.setStatus(agendamentoDTO.getStatus());
        agendamento.setDiaSemana(Calendario.getInstance(horarios.get("inicio")).get(Calendar.DAY_OF_WEEK));

        return agendamento;
    }


}
