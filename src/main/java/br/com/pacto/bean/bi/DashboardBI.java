/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.controller.json.gestao.*;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class DashboardBI implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer mes;
    private Integer ano;
    private Integer dia;
    private Integer diasConfiguracao;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicioProcessamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fimProcessamento;
    private Double percUtilizamApp = 0.0;
    private Integer utilizamApp = 0;
    private Integer inativosUtilizamApp = 0;
    private Integer naoUtilizamApp = 0;
    @Transient
    private BITreinoCarteiraDTO biCarteira;
    @Transient
    private BITreinoAvaliacaoTreinoDTO biTreinoAvaliacaoTreino;
    @Transient
    private BITreinoAgendaDTO biAgenda;
    @Transient
    private BITreinoTreinamentoDTO biTreinamento;
    @Transient
    private List<ClienteSintetico> clientesAtivosInstaladosApp = new ArrayList<ClienteSintetico>();
    @Transient
    private List<ClienteSintetico> clientesInativosInstaladosApp = new ArrayList<ClienteSintetico>();
    @Transient
    private List<ClienteSintetico> clientesNaoInstaladosApp = new ArrayList<ClienteSintetico>();
    private Integer totalAlunos = 0;
    private Integer totalAlunosAtivos = 0;
    private Integer totalAlunosInativos = 0;
    private Integer totalAlunosSemAcompanhamento = 0;
    private Integer totalAlunosVisitantes = 0;
    private Integer totalAlunosCancelados = 0;
    private Integer totalAlunosAtivosForaTreino = 0;
    private Integer totalAlunosTreino = 0;
    private Integer totalTreinosRenovar = 0;
    private Integer percentualAvaliacoes = 0;
    private Integer percentualRenovacoes = 0;
    private Integer percentualEmDia = 0;
    private Integer totalAlunosAvaliacoes = 0;
    private Integer totalAlunosSemTreino = 0;
    private Integer totalAlunosSemAvaliacoes = 0;
    private Integer totalNaoRenovaramCarteira = 0;
    private Integer totalRenovacoesCarteira = 0;
    private Integer totalAlunosAvencer = 0;
    private Integer totalNovosCarteira = 0;
    private Integer totalNovosCarteiraNovos = 0;
    private Integer totalNovosCarteiraTrocaram = 0;
    private Integer totalTrocaramCarteira = 0;
    private Integer tempoMedioPermanenciaCarteira = 0;
    private Integer tempoMedioPermanenciaTreino = 0;
    private Integer totalTreinosEmdia = 0;
    private Integer totalTreinosVencidos = 0;
    private Integer totalTreinosAvencer = 0;
    private Integer totalAlunosAcessaram = 0;
    private Integer nrAvaliacoesTreino = 0;
    private Double mediaValorAvaliacao = 0.0;
    private Integer nr5estrelas = 0;
    private Integer nr4estrelas = 0;
    private Integer nr3estrelas = 0;
    private Integer nr2estrelas = 0;
    private Integer nr1estrelas = 0;
    private Integer codigoProfessor;
    private Integer maiorPermanencia;
    private String nomeClienteMaiorPermanencia;
    private String matriculaClienteMaiorPermanencia;
    private Integer menorPermanencia;
    private String nomeClienteMenorPermanencia;
    private String matriculaClienteMenorPermanencia;
    private Integer agendamentos = 0;
    private Integer compareceram = 0;
    private Integer faltaram = 0;
    private Integer confirmados = 0;
    private Integer aguardandoConfirmacao = 0;
    private Integer cancelaram = 0;
    private Integer professores = 0;
    private Integer horasDisponibilidade = 0;
    private Integer horasAtendimento = 0;
    private Integer ocupacao = 0;
    private Integer novosTreinos = 0;
    private Integer treinosRevisados = 0;
    private Integer treinosRenovados = 0;
    private Integer avaliacoesFisicas = 0;
    private Integer empresa = 0;
    private Integer tempoMedianaPermanenciaCarteira = 0;
    private Integer tempoMedianaPermanenciaTreino = 0;
    @Transient
    private List<ClienteSintetico> alunos;
    @Transient
    private List<TreinoRealizado> treinos;
    @Transient
    private Integer maior;

    public Integer getMaiorPermanencia() {
        return maiorPermanencia;
    }

    public void setMaiorPermanencia(Integer maiorPermanencia) {
        this.maiorPermanencia = maiorPermanencia;
    }

    public String getNomeClienteMaiorPermanencia() {
        return nomeClienteMaiorPermanencia;
    }

    public void setNomeClienteMaiorPermanencia(String nomeClienteMaiorPermanencia) {
        this.nomeClienteMaiorPermanencia = nomeClienteMaiorPermanencia;
    }

    public String getMatriculaClienteMaiorPermanencia() {
        return matriculaClienteMaiorPermanencia;
    }

    public void setMatriculaClienteMaiorPermanencia(String matriculaClienteMaiorPermanencia) {
        this.matriculaClienteMaiorPermanencia = matriculaClienteMaiorPermanencia;
    }

    public Integer getMenorPermanencia() {
        return menorPermanencia;
    }

    public void setMenorPermanencia(Integer menorPermanencia) {
        this.menorPermanencia = menorPermanencia;
    }

    public String getNomeClienteMenorPermanencia() {
        return nomeClienteMenorPermanencia;
    }

    public void setNomeClienteMenorPermanencia(String nomeClienteMenorPermanencia) {
        this.nomeClienteMenorPermanencia = nomeClienteMenorPermanencia;
    }

    public String getMatriculaClienteMenorPermanencia() {
        return matriculaClienteMenorPermanencia;
    }

    public void setMatriculaClienteMenorPermanencia(String matriculaClienteMenorPermanencia) {
        this.matriculaClienteMenorPermanencia = matriculaClienteMenorPermanencia;
    }

    public String getUltimaAtualizacao() {
        try {
            return Uteis.getDataAplicandoFormatacao(fimProcessamento, "dd/MM/yyyy HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getMesPermanenciaTreino() {
        return tempoMedioPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreino() {
        int dias = tempoMedioPermanenciaTreino - getMesPermanenciaTreino() * 30;
        return dias >= 0 ? dias : tempoMedioPermanenciaTreino;
    }

    public String getDescricaoMedianaPermanenciaTreino() {
        try {
            return "Mediana: " + getMesPermanenciaTreinoMediana() + " mês e "
                    + getDiaPermanenciaTreinoMediana() + " dia. Média: "
                    + getMesPermanenciaTreino() + " mês e " + getDiaPermanenciaTreino() + " dia.";
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getMesPermanenciaTreinoMedia() {
        if (tempoMedioPermanenciaTreino == null) {
            tempoMedioPermanenciaTreino = 0;
        }
        return tempoMedioPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreinoMedia() {
        if (tempoMedioPermanenciaTreino == null) {
            tempoMedioPermanenciaTreino = 0;
        }
        int dias = tempoMedioPermanenciaTreino - getMesPermanenciaTreinoMedia() * 30;
        return dias >= 0 ? dias : tempoMedioPermanenciaTreino;
    }

    public Integer getMesPermanenciaTreinoMediana() {
        if (tempoMedianaPermanenciaTreino == null) {
            tempoMedianaPermanenciaTreino = 0;
        }
        return tempoMedianaPermanenciaTreino / 30;
    }

    public Integer getDiaPermanenciaTreinoMediana() {
        if (tempoMedianaPermanenciaTreino == null) {
            tempoMedianaPermanenciaTreino = 0;
        }
        int dias = tempoMedianaPermanenciaTreino - getMesPermanenciaTreinoMediana() * 30;
        return dias >= 0 ? dias : tempoMedianaPermanenciaTreino;
    }

    public Integer getMesPermanenciaCarteira() {
        if(tempoMedioPermanenciaCarteira != null){
            return tempoMedioPermanenciaCarteira / 30;
        }else{
            return 0;
        }

    }

    public Integer getDiaPermanenciaCarteira() {
        if(tempoMedioPermanenciaCarteira != null){
            int dias = tempoMedioPermanenciaCarteira - getMesPermanenciaCarteira() * 30;
            return dias >= 0 ? dias : tempoMedioPermanenciaCarteira;
        }else{
            return 0;
        }

    }

    public List<String> getEstrelas() {
        List<String> stars = new ArrayList<String>();
        for (int i = 1; i <= 5; i++) {
            if (i > mediaValorAvaliacao && (i + 1) < mediaValorAvaliacao) {
                stars.add("icon-star-half-empty");
            } else if (i <= mediaValorAvaliacao) {
                stars.add("icon-star");
            } else if (i > mediaValorAvaliacao) {
                stars.add("icon-star-empty ");
            }
        }
        return stars;
    }

    public GestaoTreinoJSON toJSON(){

        if(UteisValidacao.emptyNumber(this.getCodigo())){
            return new GestaoTreinoJSON();
        }
        GestaoTreinoJSON obj = new GestaoTreinoJSON();
        obj.setAno(this.ano);
        obj.setCodigo(this.codigo);
        obj.setDia(this.dia);
        obj.setMes(this.mes);
        obj.setProfessor(this.codigoProfessor);
        obj.setEmpresa(this.empresa);
        obj.setTotalTreinosAvencer(this.totalTreinosAvencer);
        obj.setTotalTreinosVencidos(this.totalTreinosVencidos);
        obj.setTotalTreinosEmdia(this.totalTreinosEmdia);
        obj.setRenovados(this.getTotalRenovacoesCarteira());
        obj.setNaoRenovados(this.getTotalNaoRenovaramCarteira());
        obj.setMesAno((this.mes < 10 ? "0"+this.mes : this.mes) + "/"+ this.ano);
        return obj;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Date getFimProcessamento() {
        return fimProcessamento;
    }

    public Date getInicioProcessamento() {
        return inicioProcessamento;
    }

    public void setInicioProcessamento(Date inicioProcessamento) {
        this.inicioProcessamento = inicioProcessamento;
    }

    public void setFimProcessamento(Date fimProcessamento) {
        this.fimProcessamento = fimProcessamento;
    }

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getTotalAlunosAtivos() {
        return totalAlunosAtivos;
    }

    public void setTotalAlunosAtivos(Integer totalAlunosAtivos) {
        this.totalAlunosAtivos = totalAlunosAtivos;
    }

    public Integer getTotalAlunosInativos() {
        return totalAlunosInativos;
    }

    public void setTotalAlunosInativos(Integer totalAlunosInativos) {
        this.totalAlunosInativos = totalAlunosInativos;
    }

    public Integer getTotalAlunosSemAcompanhamento() { return totalAlunosSemAcompanhamento; }

    public void setTotalAlunosSemAcompanhamento(Integer totalAlunosSemAcompanhamento) { this.totalAlunosSemAcompanhamento = totalAlunosSemAcompanhamento; }

    public Integer getTotalAlunosCancelados() {
        return null == totalAlunosCancelados ? 0 : totalAlunosCancelados;
    }

    public void setTotalAlunosCancelados(Integer totalAlunosCancelados) {
        this.totalAlunosCancelados = totalAlunosCancelados;
    }

    public Integer getTotalAlunosAtivosForaTreino() {
        return totalAlunosAtivosForaTreino;
    }

    public void setTotalAlunosAtivosForaTreino(Integer totalAlunosAtivosForaTreino) {
        this.totalAlunosAtivosForaTreino = totalAlunosAtivosForaTreino;
    }

    public Integer getTotalAlunosTreino() {
        return totalAlunosTreino;
    }

    public void setTotalAlunosTreino(Integer totalAlunosTreino) {
        this.totalAlunosTreino = totalAlunosTreino;
    }

    public Integer getTotalTreinosRenovar() {
        return totalTreinosRenovar;
    }

    public void setTotalTreinosRenovar(Integer totalTreinosRenovar) {
        this.totalTreinosRenovar = totalTreinosRenovar;
    }

    public Integer getPercentualAvaliacoes() {
        return percentualAvaliacoes;
    }

    public void setPercentualAvaliacoes(Integer percentualAvaliacoes) {
        this.percentualAvaliacoes = percentualAvaliacoes;
    }

    public Integer getTotalAlunosAvaliacoes() {
        return totalAlunosAvaliacoes;
    }

    public void setTotalAlunosAvaliacoes(Integer totalAlunosAvaliacoes) {
        this.totalAlunosAvaliacoes = totalAlunosAvaliacoes;
    }

    public Integer getTotalAlunosSemAvaliacoes() {
        return totalAlunosSemAvaliacoes;
    }

    public void setTotalAlunosSemAvaliacoes(Integer totalAlunosSemAvaliacoes) {
        this.totalAlunosSemAvaliacoes = totalAlunosSemAvaliacoes;
    }

    public Integer getTotalNaoRenovaramCarteira() {
        return totalNaoRenovaramCarteira;
    }

    public void setTotalNaoRenovaramCarteira(Integer totalNaoRenovaramCarteira) {
        this.totalNaoRenovaramCarteira = totalNaoRenovaramCarteira;
    }

    public Integer getTotalRenovacoesCarteira() {
        return totalRenovacoesCarteira;
    }

    public void setTotalRenovacoesCarteira(Integer totalRenovacoesCarteira) {
        this.totalRenovacoesCarteira = totalRenovacoesCarteira;
    }

    public Integer getTotalAlunosAvencer() {
        return totalAlunosAvencer;
    }

    public void setTotalAlunosAvencer(Integer totalAlunosAvencer) {
        this.totalAlunosAvencer = totalAlunosAvencer;
    }

    public Integer getTotalNovosCarteira() {
        return totalNovosCarteira;
    }

    public void setTotalNovosCarteira(Integer totalNovosCarteira) {
        this.totalNovosCarteira = totalNovosCarteira;
    }

    public Integer getTotalTrocaramCarteira() {
        return totalTrocaramCarteira;
    }

    public void setTotalTrocaramCarteira(Integer totalTrocaramCarteira) {
        this.totalTrocaramCarteira = totalTrocaramCarteira;
    }

    public Integer getTempoMedioPermanenciaCarteira() {
        return tempoMedioPermanenciaCarteira;
    }

    public void setTempoMedioPermanenciaCarteira(Integer tempoMedioPermanenciaCarteira) {
        this.tempoMedioPermanenciaCarteira = tempoMedioPermanenciaCarteira;
    }

    public Integer getTempoMedioPermanenciaTreino() {
        return tempoMedioPermanenciaTreino;
    }

    public void setTempoMedioPermanenciaTreino(Integer tempoMedioPermanenciaTreino) {
        this.tempoMedioPermanenciaTreino = tempoMedioPermanenciaTreino;
    }

    public Integer getTotalTreinosEmdia() {
        return totalTreinosEmdia;
    }

    public void setTotalTreinosEmdia(Integer totalTreinosEmdia) {
        this.totalTreinosEmdia = totalTreinosEmdia;
    }

    public Integer getTotalTreinosAvencer() {
        return totalTreinosAvencer;
    }

    public void setTotalTreinosAvencer(Integer totalTreinosAvencer) {
        this.totalTreinosAvencer = totalTreinosAvencer;
    }

    public Integer getNrAvaliacoesTreino() {
        return nrAvaliacoesTreino;
    }

    public void setNrAvaliacoesTreino(Integer nrAvaliacoesTreino) {
        this.nrAvaliacoesTreino = nrAvaliacoesTreino;
    }

    public Double getMediaValorAvaliacao() {
        return mediaValorAvaliacao;
    }

    public void setMediaValorAvaliacao(Double mediaValorAvaliacao) {
        this.mediaValorAvaliacao = mediaValorAvaliacao;
    }

    public Integer getNr5estrelas() {
        return nr5estrelas;
    }

    public void setNr5estrelas(Integer nr5estrelas) {
        this.nr5estrelas = nr5estrelas;
    }

    public Integer getNr4estrelas() {
        return nr4estrelas;
    }

    public void setNr4estrelas(Integer nr4estrelas) {
        this.nr4estrelas = nr4estrelas;
    }

    public Integer getNr3estrelas() {
        return nr3estrelas;
    }

    public void setNr3estrelas(Integer nr3estrelas) {
        this.nr3estrelas = nr3estrelas;
    }

    public Integer getNr2estrelas() {
        return nr2estrelas;
    }

    public void setNr2estrelas(Integer nr2estrelas) {
        this.nr2estrelas = nr2estrelas;
    }

    public Integer getNr1estrelas() {
        return nr1estrelas;
    }

    public void setNr1estrelas(Integer nr1estrelas) {
        this.nr1estrelas = nr1estrelas;
    }

    public Integer getTotalAlunosSemTreino() {
        return totalAlunosSemTreino;
    }

    public void setTotalAlunosSemTreino(Integer totalAlunosSemTreino) {
        this.totalAlunosSemTreino = totalAlunosSemTreino;
    }

    public Integer getTotalTreinosVencidos() {
        return totalTreinosVencidos;
    }

    public void setTotalTreinosVencidos(Integer totalTreinosVencidos) {
        this.totalTreinosVencidos = totalTreinosVencidos;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public Integer getPercentualRenovacoes() {
        return percentualRenovacoes;
    }

    public void setPercentualRenovacoes(Integer percentualRenovacoes) {
        this.percentualRenovacoes = percentualRenovacoes;
    }

    public Integer getPercentualEmDia() {
        return percentualEmDia;
    }

    public void setPercentualEmDia(Integer percentualEmDia) {
        this.percentualEmDia = percentualEmDia;
    }

    public List<ClienteSintetico> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ClienteSintetico> alunos) {
        this.alunos = alunos;
    }

    public List<TreinoRealizado> getTreinos() {
        return treinos;
    }

    public void setTreinos(List<TreinoRealizado> treinos) {
        this.treinos = treinos;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getDiasConfiguracao() {
        return diasConfiguracao;
    }

    public void setDiasConfiguracao(Integer diasConfiguracao) {
        this.diasConfiguracao = diasConfiguracao;
    }

    public Integer getMaiorValorEstrela() {
        if (maior == null) {
            maior = 0;
            Integer[] totais = new Integer[]{getNr1estrelas(), getNr2estrelas(), getNr3estrelas(), getNr4estrelas(), getNr5estrelas()};
            for (Integer nr : totais) {
                maior = nr > maior ? nr : maior;
            }
        }
        return maior;
    }

    public Double getNrPercentual(Integer valor) {
        try {
            Double retorno = (valor.doubleValue() / getMaiorValorEstrela()) * 100;
            return retorno == 0.0 ? 0.1 : retorno;
        } catch (Exception e) {
            return 0.0;
        }
    }

    public String getPercentual5Estrelas() {
        return getNrPercentual(getNr5estrelas()) + "%";
    }

    public String getPercentual4Estrelas() {
        return getNrPercentual(getNr4estrelas()) + "%";
    }

    public String getPercentual3Estrelas() {
        return getNrPercentual(getNr3estrelas()) + "%";
    }

    public String getPercentual2Estrelas() {
        return getNrPercentual(getNr2estrelas()) + "%";
    }

    public String getPercentual1Estrelas() {
        return getNrPercentual(getNr1estrelas()) + "%";
    }

    public Integer getPercentualAtivosForaTreino() {
        try {
            return new Double(((getTotalAlunosAtivosForaTreino().doubleValue() / (getTotalAlunosAtivosForaTreino() + getTotalAlunosAtivos()))) * 100).intValue();
        } catch (Exception e) {
            return 0;
        }
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getCompareceram() {
        return compareceram;
    }

    public void setCompareceram(Integer compareceram) {
        this.compareceram = compareceram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getProfessores() {
        return professores;
    }

    public void setProfessores(Integer professores) {
        this.professores = professores;
    }

    public Integer getHorasDisponibilidade() {
        return horasDisponibilidade;
    }

    public void setHorasDisponibilidade(Integer horasDisponibilidade) {
        this.horasDisponibilidade = horasDisponibilidade;
    }

    public Integer getHorasAtendimento() {
        return horasAtendimento;
    }

    public void setHorasAtendimento(Integer horasAtendimento) {
        this.horasAtendimento = horasAtendimento;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getNovosTreinos() {
        return novosTreinos;
    }

    public void setNovosTreinos(Integer novosTreinos) {
        this.novosTreinos = novosTreinos;
    }

    public Integer getTreinosRevisados() {
        return treinosRevisados;
    }

    public void setTreinosRevisados(Integer treinosRevisados) {
        this.treinosRevisados = treinosRevisados;
    }

    public Integer getTreinosRenovados() {
        return treinosRenovados;
    }

    public void setTreinosRenovados(Integer treinosRenovados) {
        this.treinosRenovados = treinosRenovados;
    }

    public Integer getAvaliacoesFisicas() {
        return avaliacoesFisicas;
    }

    public void setAvaliacoesFisicas(Integer avaliacoesFisicas) {
        this.avaliacoesFisicas = avaliacoesFisicas;
    }

    public Integer getMaior() {
        return maior;
    }

    public void setMaior(Integer maior) {
        this.maior = maior;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getTempoMedianaPermanenciaCarteira() {
        return tempoMedianaPermanenciaCarteira;
    }

    public void setTempoMedianaPermanenciaCarteira(Integer tempoMedianaPermanenciaCarteira) {
        this.tempoMedianaPermanenciaCarteira = tempoMedianaPermanenciaCarteira;
    }

    public Integer getTempoMedianaPermanenciaTreino() {
        return tempoMedianaPermanenciaTreino;
    }

    public void setTempoMedianaPermanenciaTreino(Integer tempoMedianaPermanenciaTreino) {
        this.tempoMedianaPermanenciaTreino = tempoMedianaPermanenciaTreino;
    }

    public Integer getTotalNovosCarteiraNovos() {
        return totalNovosCarteiraNovos;
    }

    public void setTotalNovosCarteiraNovos(Integer totalNovosCarteiraNovos) {
        this.totalNovosCarteiraNovos = totalNovosCarteiraNovos;
    }

    public Integer getTotalNovosCarteiraTrocaram() {
        return totalNovosCarteiraTrocaram;
    }

    public void setTotalNovosCarteiraTrocaram(Integer totalNovosCarteiraTrocaram) {
        this.totalNovosCarteiraTrocaram = totalNovosCarteiraTrocaram;
    }

    public Double getValorIndicador(IndicadorDashboardEnum indicador) {
        Double retorno = 0.0;
        switch (indicador) {
            case ACESSOS:
                retorno = getTotalAlunosAcessaram().doubleValue();
                break;
            case TOTAL_ALUNOS:
                retorno = getTotalAlunos().doubleValue();
                break;
            case ATIVOS:
                retorno = getTotalAlunosAtivos().doubleValue();
                break;
            case INATIVOS:
                retorno = getTotalAlunosInativos().doubleValue();
                break;
            case ALUNOS_CANCELADOS:
                retorno = getTotalAlunosCancelados().doubleValue();
                break;
            case ATIVOS_COM_TREINO:
                retorno = getTotalAlunosTreino().doubleValue();
                break;
            case EM_DIA:
                retorno = getTotalTreinosEmdia().doubleValue();
                break;
            case VENCIDOS:
                retorno = getTotalTreinosVencidos().doubleValue();
                break;
            case TREINOS_A_VENCER:
                retorno = getTotalTreinosRenovar().doubleValue();
                break;
            case AGENDAMENTOS:
                retorno = getAgendamentos().doubleValue();
                break;
            case COMPARECERAM:
                retorno = getCompareceram().doubleValue();
                break;
            case FALTARAM:
                retorno = getFaltaram().doubleValue();
                break;
            case CONFIRMARAM:
                retorno = getConfirmados().doubleValue();
                break;
            case AG_CONFIRMACAO:
                retorno = getAguardandoConfirmacao().doubleValue();
                break;
            case CANCELARAM:
                retorno = getCancelaram().doubleValue();
                break;
            case RENOVARAM:
                retorno = getTotalRenovacoesCarteira().doubleValue();
                break;
            case NAO_RENOVARAM:
                retorno = getTotalNaoRenovaramCarteira().doubleValue();
                break;
            case ALUNOS_A_VENCER:
                retorno = getTotalAlunosAvencer().doubleValue();
                break;
            case NOVOS_CARTEIRA_NOVOS:
                retorno = getTotalNovosCarteiraNovos().doubleValue();
                break;
            case NOVOS_CARTEIRA_TROCARAM:
                retorno = getTotalNovosCarteiraTrocaram().doubleValue();
                break;
            case NOVOS_CARTEIRA:
                retorno = getTotalNovosCarteira().doubleValue();
                break;
            case TROCARAM_CARTEIRA:
                retorno = getTotalTrocaramCarteira().doubleValue();
                break;
            case BI_TEMPO_CARTEIRA:
                retorno = getTempoMedioPermanenciaCarteira()/30.0;
                break;
            case ATIVOS_SEM_TREINO:
                retorno = getTotalAlunosSemTreino().doubleValue();
                break;
            case PERC_TREINO_EM_DIA:
                retorno = getPercentualEmDia().doubleValue();
                break;
            case PERC_TREINO_VENCIDOS:
                retorno = ((getTotalTreinosEmdia()) + getTotalTreinosVencidos()) == 0
                        ? 0.0
                        : (getTotalTreinosVencidos() * 100.0) / (getTotalTreinosEmdia() + getTotalTreinosVencidos());
                break;
            case BI_TEMPO_PROGRAMA:
                retorno = getTempoMedioPermanenciaTreino()/30.0;
                break;
            case AVALIACOES:
                retorno = getNrAvaliacoesTreino().doubleValue();
                break;
            case ESTRELAS_1:
                retorno = getNr1estrelas().doubleValue();
                break;
            case ESTRELAS_2:
                retorno = getNr2estrelas().doubleValue();
                break;
            case ESTRELAS_3:
                retorno = getNr3estrelas().doubleValue();
                break;
            case ESTRELAS_4:
                retorno = getNr4estrelas().doubleValue();
                break;
            case ESTRELAS_5:
                retorno = getNr5estrelas().doubleValue();
                break;
            case COM_AVALIACAO_FISICA:
                retorno = getTotalAlunosAvaliacoes().doubleValue();
                break;
            case SEM_AVALIACAO:
                retorno = getTotalAlunosSemAvaliacoes().doubleValue();
                break;
            case PROFESSORES:
                retorno = getProfessores().doubleValue();
                break;
            case HRS_DISPONIBILIDADE:
                retorno = getHorasDisponibilidade().doubleValue();
                break;
            case HRS_ATENDIMENTO:
                retorno = getHorasAtendimento().doubleValue();
                break;
            case OCUPACAO:
                retorno = getOcupacao().doubleValue();
                break;
            case NOVOS_TREINOS:
                retorno = getNovosTreinos().doubleValue();
                break;
            case TREINOS_RENOVADOS:
                retorno = getTreinosRenovados().doubleValue();
                break;
            case TREINOS_REVISADOS:
                retorno = getTreinosRevisados().doubleValue();
                break;
            case AVALIACOES_FISICAS:
                retorno = getAvaliacoesFisicas().doubleValue();
                break;
            case PERCENTUAL_CRESCIMENTO_CARTEIRA:
                retorno = (getTotalAlunos() - getTotalNovosCarteira()) == 0 ?
                        0.0 :
                        (getTotalNovosCarteira()*100.0)/(getTotalAlunos() - getTotalNovosCarteira());
                break;
            case PERCENTUAL_RENOVACAO_CARTEIRA:
                retorno =  getPercentualRenovacoes().doubleValue();
                break;
            case ALUNOS_APP_INSTALADO:
                retorno = getUtilizamApp().doubleValue();
                break;
            case ALUNOS_APP_INSTALADO_ATIVOS:
                retorno = getUtilizamApp().doubleValue();
                break;
            case ALUNOS_APP_NAO_INSTALADO:
                retorno = getNaoUtilizamApp().doubleValue();
                break;
        }
        return retorno;
    }

    public Integer getTotalAlunosAcessaram() {
        return totalAlunosAcessaram;
    }

    public void setTotalAlunosAcessaram(Integer totalAlunosAcessaram) {
        this.totalAlunosAcessaram = totalAlunosAcessaram;
    }
    public String getOrdem(){
        return getAno()+"/"+getMes();
    }

    public Double getPercUtilizamApp() {
        return percUtilizamApp;
    }

    public int getPercUtilizamAppInt() {
        return percUtilizamApp == null ? 0 : percUtilizamApp.intValue();
    }

    public void setPercUtilizamApp(Double percUtilizamApp) {
        this.percUtilizamApp = percUtilizamApp;
    }

    public Integer getUtilizamApp() {
        return utilizamApp;
    }

    public void setUtilizamApp(Integer utilizamApp) {
        this.utilizamApp = utilizamApp;
    }

    public Integer getInativosUtilizamApp() {
        return inativosUtilizamApp;
    }

    public void setInativosUtilizamApp(Integer inativosUtilizamApp) {
        this.inativosUtilizamApp = inativosUtilizamApp;
    }

    public Integer getNaoUtilizamApp() {
        return naoUtilizamApp;
    }

    public void setNaoUtilizamApp(Integer naoUtilizamApp) {
        this.naoUtilizamApp = naoUtilizamApp;
    }

    public List<ClienteSintetico> getClientesAtivosInstaladosApp() {
        return clientesAtivosInstaladosApp;
    }

    public void setClientesAtivosInstaladosApp(List<ClienteSintetico> clientesAtivosInstaladosApp) {
        this.clientesAtivosInstaladosApp = clientesAtivosInstaladosApp;
    }

    public List<ClienteSintetico> getClientesInativosInstaladosApp() {
        return clientesInativosInstaladosApp;
    }

    public void setClientesInativosInstaladosApp(List<ClienteSintetico> clientesInativosInstaladosApp) {
        this.clientesInativosInstaladosApp = clientesInativosInstaladosApp;
    }

    public List<ClienteSintetico> getClientesNaoInstaladosApp() {
        return clientesNaoInstaladosApp;
    }

    public void setClientesNaoInstaladosApp(List<ClienteSintetico> clientesNaoInstaladosApp) {
        this.clientesNaoInstaladosApp = clientesNaoInstaladosApp;
    }

    public Integer getTotalAlunosVisitantes() {
        return totalAlunosVisitantes;
    }

    public void setTotalAlunosVisitantes(Integer totalAlunosVisitantes) {
        this.totalAlunosVisitantes = totalAlunosVisitantes;
    }

    public BITreinoCarteiraDTO getBiCarteira() {
        return biCarteira;
    }

    public void setBiCarteira(BITreinoCarteiraDTO biCarteira) {
        this.biCarteira = biCarteira;
    }

    public BITreinoAvaliacaoTreinoDTO getBiTreinoAvaliacaoTreino() { return biTreinoAvaliacaoTreino; }

    public void setBiTreinoAvaliacaoTreino(BITreinoAvaliacaoTreinoDTO biTreinoAvaliacaoTreinoDTO) {
        this.biTreinoAvaliacaoTreino = biTreinoAvaliacaoTreinoDTO;
    }

    public BITreinoAgendaDTO getBiAgenda() {
        return biAgenda;
    }

    public void setBiAgenda(BITreinoAgendaDTO biAgenda) {
        this.biAgenda = biAgenda;
    }

    public BITreinoTreinamentoDTO getBiTreinamento() {
        return biTreinamento;
    }

    public void setBiTreinamento(BITreinoTreinamentoDTO biTreinamento) {
        this.biTreinamento = biTreinamento;
    }

    public Integer getConfirmados() {
        if (confirmados == null) return 0;
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }

    public Integer getAguardandoConfirmacao() {
        if (aguardandoConfirmacao == null) return 0;
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }
}
