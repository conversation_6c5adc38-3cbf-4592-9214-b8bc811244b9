/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import br.com.pacto.bean.ficha.FiltroCategoriaFichaJSON;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.ficha.CategoriaFichaDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.util.ViewUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityExistsException;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 *
 * <AUTHOR>
 */
@Service
public class CategoriaFichaServiceImpl implements CategoriaFichaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private CategoriaFichaDao categoriaFichaDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LogDao logDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public CategoriaFichaDao getCategoriaFichaDao() {
        return this.categoriaFichaDao;
    }

    public void setCategoriaFichaDao(CategoriaFichaDao categoriaFichaDao) {
        this.categoriaFichaDao = categoriaFichaDao;
    }

    public CategoriaFicha alterar(final String ctx, CategoriaFicha object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getCategoriaFichaDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, CategoriaFicha object) throws ServiceException {
        try {
            getCategoriaFichaDao().delete(ctx, object);
        } catch (EntityExistsException eex) {
            throw new ServiceException("entidadePossuiRelacionamento");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, CategoriaFicha object) throws ValidacaoException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            throw new ValidacaoException("validacao.nome");
        }
        if (getCategoriaFichaDao().exists(ctx, object, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public CategoriaFicha inserir(final String ctx, CategoriaFicha object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getCategoriaFichaDao().insert(ctx, object);
        } catch (ValidacaoException exx) {
            throw exx;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaFicha obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCategoriaFichaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaFicha obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getCategoriaFichaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaFicha> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCategoriaFichaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaFicha> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getCategoriaFichaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaFicha> obterTodos(final String ctx) throws ServiceException {
        try {
            return getCategoriaFichaDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CategoriaFichaResponseTO> listarCategoriasFicha(FiltroCategoriaFichaJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<CategoriaFicha> listaCategoriaEntity = categoriaFichaDao.listarCategoriaFichas(ctx,filtro,paginadorDTO);
       List<CategoriaFichaResponseTO> categoriaFichaResponseTOS = new ArrayList<>();
        for(CategoriaFicha cat: listaCategoriaEntity){
            categoriaFichaResponseTOS.add(new CategoriaFichaResponseTO(cat));
        }
        return categoriaFichaResponseTOS;
    }

    @Override
    public CategoriaFichaResponseTO criarCategoriaFicha(CategoriaFichaResponseTO categoriaFicha, HttpServletRequest request) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        CategoriaFicha novaCategoriaFicha = new CategoriaFicha(categoriaFicha.getNome());
        validarDados(ctx, novaCategoriaFicha);
        CategoriaFichaResponseTO categoria = new CategoriaFichaResponseTO(getCategoriaFichaDao().insert(ctx, novaCategoriaFicha));
        incluirLog(ctx, categoria.getId().toString(),
                "", "", categoria.getNome(),
                "INCLUSÃO", "INCLUSÃO CATEGORIA DE FICHA PREDEFINIDA",
                EntidadeLogEnum.CATEGORIA_FICHA_PREDEFINIDA, "categoriaFichaPredefinida", sessaoService, logDao);
        return  categoria;
    }

    @Override
    public CategoriaFichaResponseTO editarCategoriaFicha(CategoriaFichaResponseTO categoriaFichaTo, HttpServletRequest request) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        CategoriaFicha categoriaFicha = getCategoriaFichaDao().findById(ctx, categoriaFichaTo.getId());
        if(categoriaFicha == null){
            throw new ValidacaoException("Categoria não localizada");
        }
        String nomeAnterior = categoriaFicha.getNome();
        categoriaFicha.setNome(categoriaFichaTo.getNome());
        validarDados(ctx, categoriaFicha);
        CategoriaFichaResponseTO categoria = new CategoriaFichaResponseTO(getCategoriaFichaDao().update(ctx,categoriaFicha));
        incluirLog(ctx, categoria.getId().toString(),
                "", nomeAnterior, categoria.getNome(),
                "ALTERAÇÃO", "ALTERAÇÃO CATEGORIA DE FICHA PREDEFINIDA",
                EntidadeLogEnum.CATEGORIA_FICHA_PREDEFINIDA, "categoriaFichaPredefinida", sessaoService, logDao);
        return categoria;
    }

    @Override
    public void excluirCategoriaFicha(Integer id, HttpServletRequest request) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        CategoriaFicha categoriaFicha = getCategoriaFichaDao().findById(ctx, id);
        if(categoriaFicha == null){
            throw new ValidacaoException("Categoria não localizada");
        }
        incluirLog(ctx, categoriaFicha.getCodigo().toString(),
                "", categoriaFicha.getNome(), "",
                "EXCLUSÃO", "EXCLUSÃO CATEGORIA DE FICHA PREDEFINIDA",
                EntidadeLogEnum.CATEGORIA_FICHA_PREDEFINIDA, "categoriaFichaPredefinida", sessaoService, logDao);
        getCategoriaFichaDao().delete(ctx,categoriaFicha);
    }
}
