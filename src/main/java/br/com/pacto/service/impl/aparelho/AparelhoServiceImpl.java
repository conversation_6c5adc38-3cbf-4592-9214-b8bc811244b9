/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aparelho;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.aparelho.AparelhoEmpresa;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import br.com.pacto.controller.json.programa.AparelhoTO;
import br.com.pacto.controller.json.programa.AtividadeAparelhoResponseRecursiveTO;
import br.com.pacto.dao.intf.aparelho.AparelhoAjusteDao;
import br.com.pacto.dao.intf.aparelho.AparelhoDao;
import br.com.pacto.dao.intf.aparelho.AparelhoWodDao;
import br.com.pacto.dao.intf.atividade.AtividadeAparelhoDao;
import br.com.pacto.dao.intf.aparelho.AparelhoEmpresaDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AparelhoExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.AuditUtilities;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.Map;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 * <AUTHOR>
 */
@Service
public class AparelhoServiceImpl implements AparelhoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AparelhoDao aparelhoDao;
    @Autowired
    private AparelhoEmpresaDao aparelhoEmpresaDao;
    @Autowired
    private AparelhoAjusteDao aparelhoAjusteDao;
    @Autowired
    private AtividadeAparelhoDao atividadeAparelhoDao;
    @Autowired
    private AparelhoWodDao aparelhoWodDao;
    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private LogDao logDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AparelhoDao getAparelhoDao() {
        return this.aparelhoDao;
    }

    public AparelhoEmpresaDao getAparelhoEmpresaDao() {
        return aparelhoEmpresaDao;
    }

    public void setAparelhoDao(AparelhoDao aparelhoDao) {
        this.aparelhoDao = aparelhoDao;
    }

    public void validarDados(final String ctx, Aparelho object) throws ServiceException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            if (!JSFUtilities.isJSFContext()) {
                throw new ServiceException(AparelhoExcecoes.VALIDACAO_NOME_APARELHO);
            }
            throw new ValidacaoException("validacao.nome");
        }
        if (getAparelhoDao().exists(ctx, object, "nome")) {
            if (!JSFUtilities.isJSFContext()) {
                throw new ServiceException(AparelhoExcecoes.VALIDACAO_APARELHO_JA_EXISTE);
            }
            throw new ValidacaoException("cadastros.existente");
        }
        object.setTodasEmpresas(object.getEmpresasHabilitadas() == null || object.getEmpresasHabilitadas().isEmpty());

    }

    public Aparelho alterar(final String ctx, Aparelho object) throws ServiceException {
        try {
            validarDados(ctx, object);
            return getAparelhoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Aparelho object) throws ServiceException {
        try {
            getAparelhoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Aparelho inserir(final String ctx, Aparelho object) throws ServiceException {
        try {
            validarDados(ctx, object);
            return getAparelhoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Aparelho obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAparelhoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Aparelho obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAparelhoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Aparelho> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAparelhoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Aparelho> obterPorParam(final String ctx, String query,
                                        Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAparelhoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Aparelho> obterTodos(final String ctx, final boolean crossfit) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM Aparelho obj ");
            sql.append(" WHERE obj.crossfit = ").append(crossfit).append(" ");
            sql.append(" ORDER by obj.nome ");
            return getAparelhoDao().findByParam(ctx, sql.toString(), new HashMap<String, Object>());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AparelhoEmpresa obterIdentificadorEmpresa(final String ctx, final Aparelho aparelho,
                                                     Integer empresa) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("empresa", empresa);
            params.put("aparelho", aparelho.getCodigo());
            return getAparelhoEmpresaDao().findObjectByParam(ctx, "SELECT obj FROM AparelhoEmpresa obj "
                    + "WHERE obj.empresa.codZW = :empresa AND obj.aparelho.codigo = :aparelho", params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    public List<AparelhoEmpresa> obterIdentificadoresEmpresa(final String ctx, final Aparelho aparelho) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("aparelho", aparelho.getCodigo());
            return getAparelhoEmpresaDao().findByParam(ctx, "SELECT obj FROM AparelhoEmpresa obj "
                    + "WHERE obj.aparelho.codigo = :aparelho", params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AparelhoResponseTO> consultarAparelhos(FiltroAparelhoJSON filtroAparelhoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");
        }
        return getAparelhoDao().consultarAparelhos(ctx, filtroAparelhoJSON, paginadorDTO);
    }

    public AparelhoResponseTO inserir(AparelhoTO aparelhoTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Aparelho aparelho = new Aparelho();


        try {
            aparelhoTO.setNome(aparelhoTO.getNome().trim());
            aparelhoTO.setSensorSelfloops(aparelhoTO.getSensorSelfloops());
            if (getAparelhoDao().aparelhoDuplicado(ctx, aparelhoTO)) {
                throw new ServiceException(AparelhoExcecoes.VALIDACAO_APARELHO_JA_EXISTE);

            }
            povoarItensAparelho(ctx, aparelho, aparelhoTO);
            acao(aparelho, TipoRevisaoEnum.INSERT);
            aparelho = getAparelhoDao().insert(ctx, aparelho);

            incluirLog(ctx, aparelho.getCodigo().toString(),
                    "", "", aparelho.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE APARELHO",
                    EntidadeLogEnum.APARELHO, "Aparelho", sessaoService, logDao);

            return new AparelhoResponseTO(aparelho);
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_INCLUIR_APARELHO, e);
        } finally {
            leaveAcao();
        }
    }

    private void povoarItensAparelho(final String ctx, Aparelho aparelho, AparelhoTO aparelhoTO) throws Exception {
        aparelho.setNome(aparelhoTO.getNome());
        aparelho.setSensorSelfloops(aparelhoTO.getSensorSelfloops());
        aparelho.setCrossfit(aparelhoTO.getCrossfit());
        aparelho.setSigla(aparelhoTO.getSigla());
        aparelho.setIcone(aparelhoTO.getIcone());
        aparelho.setUsarEmReservaEquipamentos(aparelhoTO.getUsarEmReservaEquipamentos());
        aparelho.getAjustes().clear();
        if ((aparelhoTO.getAjusteIds() != null) && (aparelhoTO.getAjusteIds().size() > 0)) {
            // Ajustes que continuam com relação.
            for (Integer idAjuste : aparelhoTO.getAjusteIds()) {
                AparelhoAjuste aparelhoAjuste = null;
                try {
                    aparelhoAjuste = getAparelhoAjusteDao().findById(ctx, idAjuste);
                } catch (Exception e) {
                    throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHO_AJUSTE, e);
                }
                if ((aparelhoAjuste == null) || (aparelhoAjuste.getCodigo() == null) || (aparelhoAjuste.getCodigo() <= 0)) {
                    throw new ServiceException(AparelhoExcecoes.APARELHO_AJUSTE_NAO_ENCONTRADO);
                }
                aparelho.getAjustes().add(aparelhoAjuste);
            }
        }
        if ((aparelhoTO.getNovosAjustes() != null) && (aparelhoTO.getNovosAjustes().size() > 0)) {
            // Novos Ajustes
            for (String novoAjuste : aparelhoTO.getNovosAjustes()) {
                AparelhoAjuste aparelhoAjuste = new AparelhoAjuste();
                aparelhoAjuste.setAparelho(aparelho);
                aparelhoAjuste.setNome(novoAjuste);
                aparelho.getAjustes().add(aparelhoAjuste);
            }
        }
        if ((aparelhoTO.getAtividadeIds() != null) && (aparelhoTO.getAtividadeIds().size() > 0)) {
            for (AtividadeAparelho obj : aparelho.getAtividades()) {
                ((AtividadeAparelhoDao) UtilContext.getBean(AtividadeAparelhoDao.class)).delete(ctx, obj.getCodigo());
            }
            aparelho.getAtividades().clear();
            for (Integer idAtividade : aparelhoTO.getAtividadeIds()) {
                Atividade atividade = null;
                try {
                    atividade = getAtividadeDao().findById(ctx, idAtividade);
                } catch (Exception e) {
                    throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_ATIVIDADE, e);
                }
                if ((atividade == null) || (atividade.getCodigo() == null) || (atividade.getCodigo() <= 0)) {
                    throw new ServiceException(AparelhoExcecoes.ATIVIDADE_NAO_ENCONTRADA);
                }
                AtividadeAparelho atividadeAparelho = new AtividadeAparelho();
                atividadeAparelho.setAparelho(aparelho);
                atividadeAparelho.setAtividade(atividade);
                aparelho.getAtividades().add(atividadeAparelho);
            }
        }
        // validarDados(ctx, aparelho);
    }

    @Override
    public AparelhoResponseTO alterar(Integer id, AparelhoTO aparelhoTO) throws ServiceException {

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            Aparelho aparelhoAtual = new Aparelho();
            Aparelho aparelhoAlterado = new Aparelho();

            aparelhoTO.setId(id);
            aparelhoTO.setNome(aparelhoTO.getNome().trim());

            if (aparelhoDao.aparelhoDuplicado(ctx, aparelhoTO)) {
                throw new ServiceException(AparelhoExcecoes.VALIDACAO_APARELHO_JA_EXISTE);
            }
            try {
                aparelhoAlterado = getAparelhoDao().findById(ctx, id);
                aparelhoAtual = aparelhoAlterado.clone();
            } catch (Exception e) {
                throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHO, e);
            }
            if ((aparelhoAtual == null) || (aparelhoAtual.getCodigo() == null) || (aparelhoAtual.getCodigo() <= 0)) {
                throw new ServiceException(AparelhoExcecoes.APARELHO_NAO_ENCONTRADO);
            }
            povoarItensAparelho(ctx, aparelhoAlterado, aparelhoTO);
            try {
                incluirLog(ctx, aparelhoAlterado.getCodigo().toString(), null,
                        aparelhoAtual.getDescricaoParaLog(aparelhoAlterado),
                        aparelhoAlterado.getDescricaoParaLog(aparelhoAtual), "ALTERAÇÃO",
                        "ALTERAÇÃO DE APARELHO", EntidadeLogEnum.APARELHO, "Aparelho", sessaoService, logDao);
                acao(aparelhoAlterado, TipoRevisaoEnum.UPDATE);
                aparelhoAlterado = getAparelhoDao().update(ctx, aparelhoAlterado);
            } catch (Exception e) {
                throw new ServiceException(AparelhoExcecoes.ERRO_ALTERAR_APARELHO, e);
            } finally {
                leaveAcao();
            }
            return new AparelhoResponseTO(aparelhoAlterado);
        } catch (Exception e) {
            throw new ServiceException(AparelhoExcecoes.ERRO_ALTERAR_APARELHO, e);
        }
    }

    public void excluir(Integer codigoAparelho) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        try {
            validarAparelhoVinculadoReservaEquipamento(ctx, codigoAparelho);
            Aparelho aparelho = null;
            try {
                aparelho = carregarAparelhoComRelacionamentos(ctx, codigoAparelho);
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                        String.format("Erro ao buscar aparelho ID: %d", codigoAparelho), e);
                throw new ServiceException(AparelhoExcecoes.ERRO_BUSCAR_APARELHO, e);
            }

            if ((aparelho == null) || (aparelho.getCodigo() == null) || (aparelho.getCodigo() <= 0)) {
                throw new ServiceException(AparelhoExcecoes.APARELHO_NAO_ENCONTRADO);
            }

            incluirLog(ctx, aparelho.getCodigo().toString(), "",
                    aparelho.getDescricaoParaLog(null), "", "EXCLUSÃO",
                    "EXCLUSÃO DE APARELHO", EntidadeLogEnum.APARELHO, "Aparelho", sessaoService, logDao);
            acao(aparelho, TipoRevisaoEnum.DELETE);

            excluirRelacionamentosManualmente(ctx, aparelho);

            getAparelhoDao().delete(ctx, aparelho);

        } catch (ServiceException e) {
            Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                    String.format("Erro de negócio ao excluir aparelho ID: %d - %s", codigoAparelho, e.getMessage()), e);
            throw e;
        } catch (Exception e) {
            Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                    String.format("Erro inesperado ao excluir aparelho ID: %d - %s", codigoAparelho, e.getMessage()), e);
            throw new ServiceException(AparelhoExcecoes.ERRO_EXCLUIR_APARELHO, e);
        } finally {
            leaveAcao();
        }
    }

    private Aparelho carregarAparelhoComRelacionamentos(String ctx, Integer codigoAparelho) throws Exception {
        Aparelho aparelho = getAparelhoDao().findById(ctx, codigoAparelho);

        if (aparelho != null) {
            try {
                if (aparelho.getAtividades() != null) {
                    aparelho.getAtividades().size();
                }
                if (aparelho.getAjustes() != null) {
                    aparelho.getAjustes().size();
                }
                if (aparelho.getEmpresasHabilitadas() != null) {
                    aparelho.getEmpresasHabilitadas().size();
                }
                if (aparelho.getAparelhosWod() != null) {
                    aparelho.getAparelhosWod().size();
                }
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).warning(
                        String.format("Erro ao carregar relacionamentos do aparelho ID: %d - %s", codigoAparelho, e.getMessage()));
            }
        }

        return aparelho;
    }

    private void excluirRelacionamentosManualmente(String ctx, Aparelho aparelho) throws ServiceException {
        try {
            try {
                if (aparelho.getAtividades() != null && !aparelho.getAtividades().isEmpty()) {
                    for (AtividadeAparelho atividadeAparelho : new ArrayList<>(aparelho.getAtividades())) {
                        atividadeAparelhoDao.delete(ctx, atividadeAparelho);
                    }
                    aparelho.getAtividades().clear();
                }
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                        String.format("Erro ao excluir atividades do aparelho ID: %d - %s", aparelho.getCodigo(), e.getMessage()), e);
            }

            try {
                if (aparelho.getAjustes() != null && !aparelho.getAjustes().isEmpty()) {
                    for (AparelhoAjuste ajuste : new ArrayList<>(aparelho.getAjustes())) {
                        aparelhoAjusteDao.delete(ctx, ajuste);
                    }
                    aparelho.getAjustes().clear();
                }
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                        String.format("Erro ao excluir ajustes do aparelho ID: %d - %s", aparelho.getCodigo(), e.getMessage()), e);
            }

            try {
                if (aparelho.getEmpresasHabilitadas() != null && !aparelho.getEmpresasHabilitadas().isEmpty()) {
                    for (AparelhoEmpresa empresa : new ArrayList<>(aparelho.getEmpresasHabilitadas())) {
                        aparelhoEmpresaDao.delete(ctx, empresa);
                    }
                    aparelho.getEmpresasHabilitadas().clear();
                }
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                        String.format("Erro ao excluir empresas do aparelho ID: %d - %s", aparelho.getCodigo(), e.getMessage()), e);
            }

            try {
                if (aparelho.getAparelhosWod() != null && !aparelho.getAparelhosWod().isEmpty()) {
                    for (AparelhoWod aparelhoWod : new ArrayList<>(aparelho.getAparelhosWod())) {
                        aparelhoWodDao.delete(ctx, aparelhoWod);
                    }
                    aparelho.getAparelhosWod().clear();
                }
            } catch (Exception e) {
                Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                        String.format("Erro ao excluir WODs do aparelho ID: %d - %s", aparelho.getCodigo(), e.getMessage()), e);
            }

        } catch (Exception e) {
            Logger.getLogger(AparelhoServiceImpl.class.getName()).log(Level.SEVERE,
                    String.format("Erro ao excluir relacionamentos do aparelho ID: %d - %s", aparelho.getCodigo(), e.getMessage()), e);
            throw new ServiceException("Erro ao excluir relacionamentos do aparelho: " + e.getMessage(), e);
        }
    }

    private void validarAparelhoVinculadoReservaEquipamento(String ctx, Integer codigoAparelho) throws ServiceException {
        StringBuilder sqlZw = new StringBuilder();
        sqlZw.append("select codigo, turma from turmamapaequipamentoaparelho t where codigo_aparelhotreino = " + codigoAparelho);
        String listaCodigos = "";

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlZw.toString(), conZW)) {
                while (rs.next()) {
                    listaCodigos += ", " + rs.getString("turma");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("Erro ao verificar se aparelho possui vinculo com reserva de equipamento:" + e.getMessage());
        }

        if (!UteisValidacao.emptyString(listaCodigos)) {
            listaCodigos = Uteis.removerPrefixo(listaCodigos, ", ");
            String[] arrayCodigos = listaCodigos.split(",\\s*");
            String sufixoMensagem = arrayCodigos.length == 1 ? " da aula de código: " : " das aulas de código: ";
            throw new ServiceException(AparelhoExcecoes.VALIDACAO_APARELHO_VINCULADO_RESERVA_EQUIPAMENTO.getDescricaoExcecao() + sufixoMensagem + listaCodigos);
        }
    }

    public AparelhoResponseTO consultarAparelho(Integer codigoAparelho) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        Aparelho aparelho = obterPorId(ctx, codigoAparelho);
        if ((aparelho == null) || (aparelho.getCodigo() == null) || (aparelho.getCodigo() <= 0)) {
            throw new ServiceException(AparelhoExcecoes.APARELHO_NAO_ENCONTRADO);
        }
        return new AparelhoResponseTO(aparelho);
    }

    @Override
    public List<AtividadeAparelhoResponseRecursiveTO> listaTodosAparelhos(boolean crossfit) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            prepare(ctx);
            StringBuilder hql = new StringBuilder();
            HashMap<String, Object> params = new HashMap<>();
            hql.append("SELECT obj FROM Aparelho obj WHERE obj.crossfit = :crossfit");
            params.put("crossfit", crossfit);
            List<Aparelho> aparelhoList = getAparelhoDao().findByParam(ctx, hql.toString(), params);
            List<AtividadeAparelhoResponseRecursiveTO> listR = new ArrayList<>();
            for (Aparelho aparelho : aparelhoList) {
                listR.add(new AtividadeAparelhoResponseRecursiveTO(aparelho));
            }
            return listR;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public AtividadeDao getAtividadeDao() {
        return atividadeDao;
    }

    public void setAtividadeDao(AtividadeDao atividadeDao) {
        this.atividadeDao = atividadeDao;
    }

    public AparelhoAjusteDao getAparelhoAjusteDao() {
        return aparelhoAjusteDao;
    }

    public void setAparelhoAjusteDao(AparelhoAjusteDao aparelhoAjusteDao) {
        this.aparelhoAjusteDao = aparelhoAjusteDao;
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    protected void acao(final Aparelho aparelho, TipoRevisaoEnum tipo) throws ServiceException {
        AuditUtilities.putAcaoFromCurrentThread(Thread.currentThread().getId(), (aparelho.getCrossfit() ? "ap-cross" : "ap-treino") +
                "_" + aparelho.getNome() +
                "_t" + tipo.getId() +
                "_" + (aparelho.getCodigo() == null ? "0" : aparelho.getCodigo()));
    }

    protected void leaveAcao() {
        try {
            AuditUtilities.leaveAcaoFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    @Override
    public List<AparelhoResponseTO> aparelhosHabilitadosReservaEquipamento() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return getAparelhoDao().consultarAparelhosHabilitadosReservaEquipamento(ctx);
    }

}
