package br.com.pacto.service.impl.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.agendamento.AlunoVinculoAulaEnum;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.agenda.AgendaModoBDServiceImpl;
import br.com.pacto.service.impl.agenda.AgendaServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcecoes;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.agendatotal.AlunosAulaService;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AlunosAulasServiceImpl implements AlunosAulaService {

    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AgendaTotalService agendaTotalService;
    @Autowired
    private AparelhoService aparelhoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;

    public List<AlunoTurmaDTO> alunosAula(Integer empresa,
                                          Date dia,
                                          Integer horarioTurma,
                                          PaginadorDTO paginadorDTO,
                                          HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AgendaModoBDServiceImpl agendaAulasService;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);

                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aula = integracaoWS.consultarUmaAula(ctx,
                        Calendario.getData(dia, "dd/MM/yyyy"),
                        horarioTurma);
                if (aula == null) {
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
                AgendaTotalTO aulaHr = new AgendaTotalTO(aula);
                List<AgendaTotalTO> aulas = new ArrayList<>();
                aulas.add(aulaHr);

            Map<String, List<AgendadoTO>> mapaAlunos = agendaAulasService.montarMapaAgendados(
                    ctx,
                    Calendario.getDataComHoraZerada(dia),
                    Calendario.getDataComHora(dia, "23:59:59"),
                    empresa,
                    aulas,
                    false, horarioTurma);

                TurmaResponseDTO turmaResponseDTO = agendaZWToTurmaResponseDTO(
                        ctx,
                        agendaAulasService,
                        request,
                        aulaHr.getIdentificador(),
                        aulaHr,
                        mapaAlunos
                );
                for (AlunoTurmaDTO aluno : turmaResponseDTO.getAlunos()) {
                    String posicaoAlunoMapa = montarAlunoPosicaoMapaReservaEquipamento(ctx, horarioTurma, aluno.getEquipamentoReservado());
                    aluno.setEquipamentoReservado(posicaoAlunoMapa);
                    if (aluno != null && !UteisValidacao.emptyNumber(aluno.getMatriculaZW())) {
                        montarSaldoCreditoAluno(aluno, integracaoWS, ctx, dia);
                    }
                    selfloopsConfiguracoesService.obterRankingCourseDoDiaIntegracaoSelfloops(ctx, empresa, horarioTurma, Calendario.getData(dia, "yyyyMMdd"), aluno);
                }
                return paginarAlunos(turmaResponseDTO.getAlunos(), paginadorDTO);
            }
        }catch (Exception e){
            System.out.println("Erro alunosAula");
            Uteis.logar(e, AlunosAulasServiceImpl.class);
            throw new ServiceException(e.getMessage());
        }
    }



    private void montarSaldoCreditoAluno(AlunoTurmaDTO aluno, IntegracaoTurmasWSConsumer integracaoWS, String ctx,Date dia) throws Exception {
        List<ControleCreditoTreinoJSON> extrato = integracaoWS.obterExtratoCreditos(ctx, aluno.getMatriculaZW(), dia);
        int totalCreditos = 0;
        int totalUtilizados = 0;

        for (ControleCreditoTreinoJSON item : extrato) {
            if ("COMPRA".equals(item.getOperacao()) || "TRANSF. SALDO".equals(item.getOperacao())) {
                totalCreditos += item.getQuantidade();
            } else if ("UTILIZAÇÃO".equals(item.getOperacao()) || "AJUSTE MANUAL".equals(item.getOperacao())) {
                totalUtilizados += Math.abs(item.getQuantidade());
            }
        }

        int saldoAtual = totalCreditos - totalUtilizados;


        if (totalCreditos > 0 || totalUtilizados > 0) {
            String tooltip;
            if (saldoAtual >= 0) {
                tooltip = "Já foram consumidos " + totalUtilizados + " dos " + totalCreditos + " créditos adquiridos.";
            } else {
                tooltip = "Foram consumidos " + totalUtilizados + " créditos de " + totalCreditos + " que foram adquiridos, "
                        + "isso significa que o saldo atual do aluno é negativo em " + Math.abs(saldoAtual) + ".";
            }

            aluno.setToolTipCredito(tooltip);
            aluno.setCredito(totalUtilizados + "/" + totalCreditos);

        } else {
            aluno.setToolTipCredito(null);
            aluno.setCredito(null);
        }

    }


    private String montarAlunoPosicaoMapaReservaEquipamento(String ctx, Integer horarioTurma, String posicaoMapa) throws Exception {
        if (UteisValidacao.emptyString(posicaoMapa)) {
            return "";
        }
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                Integer codigoTurma = null;
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select turma from horarioturma h where codigo = " + horarioTurma, conZW)) {
                    if (rs.next()) {
                        codigoTurma = rs.getInt("turma");
                        if (UteisValidacao.emptyNumber(codigoTurma)) {
                            return posicaoMapa;
                        }
                    }
                }

                StringBuilder sql = new StringBuilder();
                sql.append("select codigo, codigo_aparelhotreino, turma, mapaequipamento \n");
                sql.append("from turmamapaequipamentoaparelho \n");
                sql.append("where turma = ").append(codigoTurma);
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (rs.next()) {
                        if (rs.getString("mapaequipamento").contains(posicaoMapa)) {
                            Aparelho aparelho = aparelhoService.obterPorId(ctx, rs.getInt("codigo_aparelhotreino"));
                            if (aparelho != null && !UteisValidacao.emptyNumber(aparelho.getCodigo()) && aparelho.getUsarEmReservaEquipamentos()) {
                                posicaoMapa = posicaoMapa + " - " + aparelho.getSigla();
                                return posicaoMapa;
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }

        return posicaoMapa;
    }

    private List<AlunoTurmaDTO> paginarAlunos(List<AlunoTurmaDTO> alunos, PaginadorDTO paginadorDTO){
        paginadorDTO.setQuantidadeTotalElementos(new Long(alunos.size()));
        try {
            if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
                return alunos;
            }
            Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
            long last = (index + paginadorDTO.getSize() > (alunos.size() - 1) ?
                    (alunos.size()) : index + paginadorDTO.getSize());
            return alunos.subList(index.intValue(), (int) last);
        }catch (Exception e){
            System.out.println("Erro paginarAlunos");
            Uteis.logar(e, AlunosAulasServiceImpl.class);
        }
        return alunos;
    }

    private TurmaResponseDTO agendaZWToTurmaResponseDTO(String ctx,
                                                        AgendaModoBDServiceImpl agendaAulasService,
                                                        HttpServletRequest request,
                                                        String keyHorario,
                                                        AgendaTotalTO agendaTotal,
                                                        Map<String, List<AgendadoTO>> mapaAlunos) throws Exception {
        TurmaResponseDTO turma = new TurmaResponseDTO();
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<Integer> alunosPresentesId = integracaoWS.alunosPresentes( ctx, Integer.valueOf(agendaTotal.getId()), agendaTotal.getDateInicio());
        if(mapaAlunos.get(keyHorario) == null){
            return turma;
        }
        List<Integer> dependentes = new ArrayList<>();

        try {
            dependentes = agendaAulasService.dependentes(mapaAlunos.get(keyHorario));
        }catch (Exception e){
            Uteis.logar(e, AgendaServiceImpl.class);
        }

        for (AgendadoTO aluno : mapaAlunos.get(keyHorario)) {
            AlunoTurmaDTO alunoTurma = montarAluno(ctx, request, alunosPresentesId, dependentes, aluno);

            turma.getAlunos().add(alunoTurma);
        }
        turma.setAlunos(new ArrayList<>(Ordenacao.ordenarLista(turma.getAlunos(), "nome")));

        return turma;
    }

    private AlunoTurmaDTO montarAluno(String ctx, HttpServletRequest request,
                                      List<Integer> alunosPresentesId,
                                      List<Integer> dependentes,
                                      AgendadoTO aluno) throws ServiceException {
        AlunoTurmaDTO alunoTurma = new AlunoTurmaDTO();
        alunoTurma.setCodigoCliente(aluno.getCodigoCliente());
        alunoTurma.setDataNascimento(aluno.getDataNascimento());
        alunoTurma.setCodigoPessoa(aluno.getCodigoPessoa());
        alunoTurma.setHorarioMarcacao(aluno.getHorarioMarcacao());
        alunoTurma.setId(aluno.getCodigoCliente());
        alunoTurma.setNome(aluno.getNome());
        alunoTurma.setCodigoPassivo(aluno.getCodigoPassivo());
        alunoTurma.setCodigoIndicado(aluno.getCodigoIndicado());
        alunoTurma.setUserIdSelfloops(aluno.getUserIdSelfloops());

        try {
            alunoTurma.setAutorizadoGestaoRede(aluno.getAutorizadoGestaoRede());
            alunoTurma.setCodAcessoAutorizado(aluno.getCodAcessoAutorizado());
            alunoTurma.setMatriculaAutorizado(aluno.getMatriculaAutorizado());
        } catch (Exception ex){
            ex.printStackTrace();
        }

        Boolean autorizado = (!UteisValidacao.emptyString(aluno.getMatricula()) && aluno.getMatricula().startsWith("AUT"));
        if (autorizado) {
            alunoTurma.setMatriculaZW(aluno.getCodigoCliente());
        } else if (!UteisValidacao.emptyString(aluno.getMatricula())) {
            alunoTurma.setMatriculaZW(Integer.valueOf(aluno.getMatricula()));
        }
        alunoTurma.setJustificativa(aluno.getJustificativa());

        alunoTurma.setSituacaoContrato(SituacaoContratoZWEnum.getInstance(aluno.getSituacaoContrato()));
        alunoTurma.setSituacaoAluno(SituacaoAlunoEnum.getInstance(aluno.getSituacao()));
        ClienteSintetico cliente = (autorizado || (!UteisValidacao.emptyNumber(alunoTurma.getCodigoPassivo()) || !UteisValidacao.emptyNumber(alunoTurma.getCodigoIndicado()))) ? null : clienteService.obterPorCodigoCliente(ctx, aluno.getCodigoCliente());
        if (cliente != null) {
            alunoTurma.setImageUri(Aplicacao.obterUrlFotoDaNuvem(cliente.getPessoa().getFotoKey()));
            alunoTurma.setOrigemSistema(cliente.getOrigemCliente() != null ? cliente.getOrigemCliente().name(): null);
        }
        if (aluno.isDesmarcado()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DESMARCADO);
        } else if (aluno.isDiaria() && !aluno.isGymPass()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA);
        } else if (aluno.isGymPass()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA_GYMPASS);
        } else if(aluno.isTotalPass()){
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA_TOTALPASS);
        } else if (aluno.isVisitante()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.VISITANTE);
        } else if (aluno.getDesafio()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DESAFIO);
        } else if (aluno.isReposicao() && !aluno.getUsaSaldo() && !aluno.getExperimental()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.REPOSICAO);
        } else if (aluno.isReposicao() && aluno.getUsaSaldo() && !aluno.getExperimental()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.MARCACAO);
        } else if (aluno.isExperimental() && !aluno.getDesafio() && !aluno.isGymPass()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.AULA_EXPERIMENTAL);
        } else if(dependentes.contains(aluno.getCodigoCliente())){
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DEPENDENTE);
        } else if(aluno.isFixo()){
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.PARTICIPANTE_FIXO);
        } else  if (aluno.isReposicao()) {
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.REPOSICAO);
        }
        if(autorizado){
            alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.INTEGRACAO);
            alunoTurma.setUnidade(aluno.getCidade());
        }
        alunoTurma.setFixo(aluno.isFixo());
        alunoTurma.setConfirmado(alunosPresentesId.contains(aluno.getCodigoCliente()) || aluno.isConfirmado());
        alunoTurma.setEquipamentoReservado(aluno.getEquipamentoReservado());
        return alunoTurma;
    }
}
