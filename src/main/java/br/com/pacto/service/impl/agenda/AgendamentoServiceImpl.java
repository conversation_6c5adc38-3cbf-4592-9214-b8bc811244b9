/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.agenda;

import br.com.pacto.base.impl.ConfiguracaoSistemaJdbcService;
import br.com.pacto.base.oamd.ManyDataBasesException;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.AgendamentoBuilder;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.cliente.AcaoAlunoEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ObjetivoAluno;
import br.com.pacto.bean.programa.ObjetivoAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoAlunoVO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoVO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAluno;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.AgendamentoDTO;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDTO;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoPersonalDTO;
import br.com.pacto.controller.json.aluno.FrequenciaAtendimentoAlunoVO;
import br.com.pacto.controller.json.avaliacao.AgendamentoPersonalJSON;
import br.com.pacto.controller.json.avaliacao.ConfirmacaoAgendamentoJSON;
import br.com.pacto.controller.json.avaliacao.HorarioPersonalAgendaJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.base.ConfiguracoesDiasDeBloqueioDTO;
import br.com.pacto.controller.json.gestao.IndicadorAgendaResponseDTO;
import br.com.pacto.controller.json.gestao.TreinosExecutadosEAcessosPorDiaVO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.to.DefaultScheduleEvent;
import br.com.pacto.controller.to.ScheduleModel;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.programa.ObjetivoAlunoDao;
import br.com.pacto.dao.intf.programa.ObjetivoIntermediarioAlunoDao;
import br.com.pacto.dao.intf.programa.ObjetivoPredefinidoDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.enumerador.agenda.PeriodicidadeAgendamentoEnum;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.HorarioConcomitanteException;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.locacao.LocacaoServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.HorarioDisponibilidadeService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.AgendaTO;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.MesesEnum;
import br.com.pacto.util.enumeradores.RecursosAgendaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import static br.com.pacto.base.impl.EntityManagerFactoryService.getPropsKey;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;


import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Arrays.asList;

/**
 *
 * <AUTHOR>
 */
@Service
public class AgendamentoServiceImpl implements AgendamentoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private HorarioDisponibilidadeService horarioDisponibilidadeService;
    @Autowired
    private ConfiguracaoSistemaService css;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private DashboardBIService dashboardBIService;

    @Autowired
    private LogDao logDao;
    @Autowired
    private NotificacaoService notificacaoService;
    @Autowired
    private ObjetivoAlunoDao objetivoAlunoDao;
    @Autowired
    private ObjetivoPredefinidoDao objetivoPredefinidoDao;
    @Autowired
    private ObjetivoIntermediarioAlunoDao objetivoIntermediarioAlunoDao;
    @Autowired
    private LocacaoServiceImpl locacaoService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private ClienteAcompanhamentoDao clienteAcompanhamentoDao;
    @Autowired
    private AgendaTotalService agendaTotalService;
    @Autowired
    HorarioDisponibilidadeDao horarioDisponibilidadeDao;

    public ClienteSinteticoService getClienteService() {
        return clienteService;
    }

    public void setClienteService(ClienteSinteticoService clienteService) {
        this.clienteService = clienteService;
    }

    public ProfessorSinteticoService getProfessorSinteticoService() {
        return professorSinteticoService;
    }

    public void setProfessorSinteticoService(ProfessorSinteticoService professorSinteticoService) {
        this.professorSinteticoService = professorSinteticoService;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AgendamentoDao getAgendamentoDao() {
        return this.agendamentoDao;
    }

    public void setAgendamentoDao(AgendamentoDao professorAgendamentoDao) {
        this.agendamentoDao = professorAgendamentoDao;
    }

    @Override
    public Agendamento alterar(final String ctx, Agendamento object, Usuario usuario) throws ServiceException {
        return alterar(ctx, object, usuario, null);
    }

    public Agendamento alterar(final String ctx, Agendamento object, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {
        try {
            ConfiguracaoSistema cfgPermitirAlunos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_REAGENDAMENTO_POR_ALUNO);
            if(usuario != null
                    && usuario.getTipo() != null
                    && usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && !cfgPermitirAlunos.getValorAsBoolean()){
                throw new Exception("mobile.excecao.aluno.reagendar");
            }
            object.setCliente(object.getDisponibilidade() ? null : object.getCliente());
            //Caso seja necessário voltar o metodo, alterado para o KZN-1259
            //validaAgendamento(ctx, object, usuario, empresaId);
            if (!object.getStatus().equals(StatusAgendamentoEnum.CANCELADO)
                    && !object.getStatus().equals(StatusAgendamentoEnum.FALTOU)) {
                if (object.getTipoEvento() != null) {
                    validarRestricaoTipoEvento(ctx, object);
                } else {
                    validarRestricaoDisponibilidadeConfigV2(ctx, object);
                }
            }
            preencherUsuario(object, usuario);
            return getAgendamentoDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Agendamento alterarAlgunsCamposDisponibilidade(final String ctx, Agendamento object, Usuario usuario) throws ServiceException {
        try {
            preencherUsuario(object, usuario);
            getAgendamentoDao().updateAlgunsCampos(ctx,
                    new String[]{"inicio", "fim", "professor", "diaTodo", "usuarioUltAlteracao", "ultimaAlteracao"},
                    new Object[]{object.getInicio(), object.getFim(), object.getProfessor(), object.getDiaTodo(),
                            usuarioService.obterPorId(ctx, object.getUsuarioUltAlteracao_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS),
                            object.getUltimaAlteracao()},
                    new String[]{"codigo"},
                    new Object[]{object.getCodigo()});
            return object;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Agendamento alterarStatus(final String ctx, Agendamento object,
                                     StatusAgendamentoEnum novoStatus, String observacao) throws ServiceException {
        try {
            object.setStatus(novoStatus);
            object.setObservacao(observacao);
            agendamentoDao.updateAlgunsCampos(ctx, new String[]{"status","observacao"},
                    new Object[]{novoStatus,observacao}, new String[]{"codigo"}, new Object[]{object.getCodigo()});
            return object;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void aplicarAlteracoesAoNSU(final String ctx, Agendamento object,
                                       boolean somenteMesmoTipo,boolean somenteMesmoTipoPosteriores, Usuario usuario) throws ServiceException {
        try {
            object.setProfessor((object.getProfessorCod() != null && object.getProfessorCod() > 0)
                    ? getProfessorSinteticoService().obterPorId(ctx, object.getProfessorCod()) : null);
            List<Agendamento> lista = obterDisponibilidadePorNSUApartirDia(ctx, object.getNsu(), Uteis.somarDias(Calendario.hoje(), -1));
            object.atualizarInicioFim();
            for (Agendamento agendamento : lista) {
                if (!somenteMesmoTipo || agendamento.getTipoEvento().getCodigo().equals(object.getTipoEvento().getCodigo())) {
                    if(!somenteMesmoTipoPosteriores || (Calendario.maiorOuIgual(agendamento.getInicio(),object.getInicio()))) {
                        agendamento.setarTransients();
                        agendamento.setHoraFim(object.getHoraFim());
                        agendamento.setHoraInicio(object.getHoraInicio());
                        agendamento.setMinutoFim(object.getMinutoFim());
                        agendamento.setMinutoInicio(object.getMinutoInicio());
                        agendamento.atualizarInicioFim();
                        agendamento.setProfessor(object.getProfessor());
                        agendamento.setDiaTodo(object.getDiaTodo());
                        alterarAlgunsCamposDisponibilidade(ctx, agendamento, usuario);
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public void excluir(final String ctx, Agendamento object) throws Exception {
        getAgendamentoDao().delete(ctx, object);
    }

    @Override
    public void excluir(final String ctx, Agendamento object, boolean aplicarAoNsu, boolean somenteMesmoTipo,
                        boolean somenteMesmoTipoPosteriores) throws ServiceException {
        try {
            object.setCliente(object.getDisponibilidade() ? null : object.getCliente());
            if (aplicarAoNsu) {
                String[] atributos = somenteMesmoTipo ? new String[]{"nsu", "tipoEvento.codigo"} :
                        somenteMesmoTipoPosteriores ? new String[]{"nsu", "tipoEvento.codigo", "inicio > "} :
                                new String[]{"nsu"};
                Object[] valores = somenteMesmoTipo ? new Object[]{object.getNsu(), object.getTipoEvento().getCodigo()} :
                        somenteMesmoTipoPosteriores ? new Object[]{object.getNsu(), object.getTipoEvento().getCodigo(), object.getInicio()} :
                                new Object[]{object.getNsu()};
                getAgendamentoDao().deleteComParam(ctx, atributos, valores);
                if(somenteMesmoTipoPosteriores){
                    try {
                        getAgendamentoDao().delete(ctx, object);
                    } catch (Exception e) {
                        throw new ServiceException(e);
                    }

                }
            } else {
                getAgendamentoDao().delete(ctx, object);
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void preencherUsuario(Agendamento agendamento, Usuario usuario) {
        if (agendamento.getUsuarioLancou_codigo() == null && usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
            agendamento.setUsuarioLancou_codigo(usuario.getCodigo());
        }
        if (!validacao.emptyNumber(agendamento.getCodigo()) && usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
            agendamento.setUsuarioUltAlteracao_codigo(usuario.getCodigo());
        }
        agendamento.setUltimaAlteracao(Calendario.hoje());
    }

    public Agendamento inserir(final String ctx, Agendamento object, Usuario usuario) throws ServiceException {
        return inserir(ctx, object, usuario, null);
    }

    public Agendamento inserir(final String ctx, Agendamento object, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {
        try {
            Date inicio = object.getInicio();
            Date fim = object.getFim();
            validaAgendamento(ctx, object, usuario, empresaId);
            validarRestricaoTipoEvento(ctx, object);
            preencherUsuario(object, usuario);
            object.setInicio(inicio);
            object.setFim(fim);
            return getAgendamentoDao().insert(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Agendamento inserirV2(final String ctx, Agendamento object, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {
        try {
            Date inicio = object.getInicio();
            Date fim = object.getFim();
            validaAgendamentoV2(ctx, object, usuario, empresaId);
            validarRestricaoDisponibilidadeConfigV2(ctx, object);
            preencherUsuario(object, usuario);
            object.setInicio(inicio);
            object.setFim(fim);
            return getAgendamentoDao().insert(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Date inserirRecorrente(final String ctx, Agendamento object, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {
        try {
            Date inicio = object.getInicio();
            Date fim = object.getFim();
            validaAgendamentoV2Recorrente(ctx, object, usuario, empresaId);
            Date dataIndisponibilidade = validaDisponibilidadeAgendamentoRecorrente(ctx, object, usuario, empresaId);
            if(dataIndisponibilidade != null){
                return dataIndisponibilidade;
            }
            validarRestricaoDisponibilidadeConfigV2(ctx, object);

            preencherUsuario(object, usuario);
            object.setInicio(inicio);
            object.setFim(fim);
            getAgendamentoDao().insert(ctx, object);
            return null;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Agendamento obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAgendamentoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Agendamento obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAgendamentoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAgendamentoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> obterPorParam(final String ctx, String query,
                                           Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAgendamentoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> obterTodos(final String ctx) throws ServiceException {
        try {
            return getAgendamentoDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    @Override
    public List<IndicadorAgendaResponseDTO> obterPorProfessor(Integer empresaId, FiltroGestaoJSON filtro, String sort) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String professor = "";
            if(filtro.getProfessorId() != null){
                professor = professorSinteticoService.obterPorId(ctx,filtro.getProfessorId()).getCodigoColaborador().toString();
            }

            String statusIds = "";
            String tipoEventoIds = "";

            if (!UteisValidacao.emptyList(filtro.getTiposEventosIds())) {
                for (Integer id : filtro.getTiposEventosIds()) {
                    tipoEventoIds = tipoEventoIds+(",")+id.toString();
                }
                tipoEventoIds = tipoEventoIds.substring(1);
            }


            if(filtro.getTodosStatus()){
                statusIds = null;
            }else{
                statusIds = filtro.getStatusAgendamentoEnum().getId().toString();
            }

            List<Agendamento> agendamentos =  consultarPorData(ctx, filtro.getInicio(), filtro.getFim(),
                    empresaId, professor, tipoEventoIds, statusIds,
                    null, false, false, null, null, null, null);
            List<IndicadorAgendaResponseDTO> agendaResponseDTO = new ArrayList<>();

            for(Agendamento a : agendamentos){
                agendaResponseDTO.add(new IndicadorAgendaResponseDTO(a));
            }

            return agendaResponseDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    @Override
    public List<Agendamento> consultarPorData(final String ctx, Date inicio,
                                              Date fim, final Integer empresaZW, final String professores, final String tipos,
                                              String status, final Integer codigoCliente, Boolean disponibilidades, Boolean ignorarCancelados,
                                              String orderBy, Integer index, Integer maxResults, String nomeProfessor) throws ServiceException {
        return consultarPorData(ctx, inicio, fim, empresaZW, professores, tipos, status,
                codigoCliente, disponibilidades, ignorarCancelados, orderBy, index, maxResults, false, nomeProfessor, false);
    }

    @Override
    public List<Agendamento> consultarPorData(final String ctx, Date inicio,
                                              Date fim, final Integer empresaZW, final String professores, final String tipos,
                                              String status, final Integer codigoCliente, Boolean disponibilidades, Boolean ignorarCancelados,
                                              String orderBy, Integer index, Integer maxResults, Boolean between, String nome, boolean modeloNovo) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from Agendamento obj where obj.professor.ativo = TRUE ");
            if (inicio != null && between) {
                query.append(" and :inicio between obj.inicio and obj.fim ");
                p.put("inicio", inicio);
            }
            if (inicio != null && !between) {
                query.append(" and obj.inicio >= :inicio ");
                p.put("inicio", inicio);
            }
            if (fim != null && !between) {
                query.append(" and obj.fim <= :fim ");
                p.put("fim", fim);
            }
            if(!UteisValidacao.emptyString(nome)){
                query.append(" and (");
                query.append(" upper(obj.professor.nome) like '%").append(nome.toUpperCase()).append("%'");
                query.append(" or upper(obj.cliente.nome) like '%").append(nome.toUpperCase()).append("%')");
            }
            if (professores != null && !professores.isEmpty()) {
                if(SuperControle.independente(ctx)) {
                    query.append(" and obj.professor.codigo IN (").append(professores).append(") ");
                }else{
                    query.append(" and obj.professor.codigoColaborador IN (").append(professores).append(") ");
                }
            }

            if (tipos != null && !tipos.isEmpty() && !modeloNovo) {
                query.append(" and obj.tipoEvento.codigo IN (").append(tipos).append(") ");
            }

            if (tipos != null && !tipos.isEmpty() && modeloNovo) {
                query.append(" and obj.horarioDisponibilidade.disponibilidade.codigo IN (").append(tipos).append(") ");
            }

            if (status != null && !status.isEmpty()) {
                query.append(" and obj.status IN (").append(status).append(") ");
            } else if (ignorarCancelados) {
                query.append(" and obj.status NOT IN (").append(StatusAgendamentoEnum.CANCELADO.ordinal()).append(") ");
            }
            if (codigoCliente != null && codigoCliente > 0) {
                query.append(" and obj.cliente.codigo = :codigocliente ");
                p.put("codigocliente", codigoCliente);
            }
            if (disponibilidades != null && disponibilidades) {
                query.append(" and obj.disponibilidade is true ");
            }
            if (disponibilidades != null && !disponibilidades) {
                query.append(" and obj.disponibilidade is false and obj.cliente.codigo is not null");
            }
            if (empresaZW != null) {
                if (SuperControle.independente(ctx)) {
                    query.append(" and obj.professor.empresa.codigo = ").append(empresaZW);
                } else {
                    query.append(" and obj.professor.empresa.codZW = ").append(empresaZW);
                }
            }
            query.append(orderBy == null ? " order by obj.inicio " : query.append(" order by ").append(orderBy));



            if (maxResults != null && index != null) {
                return obterPorParam(ctx, query.toString(), p, maxResults, index);
            } else {
                return obterPorParam(ctx, query.toString(), p);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Agendamento> consultarPorDataV2(final String ctx, Date inicio,
                                              Date fim, final Integer empresaZW, final String professores, final String tipos,
                                              String status, final Integer codigoCliente, Boolean disponibilidades, Boolean ignorarCancelados,
                                              String orderBy, Integer index, Integer maxResults, Boolean between, String nome, boolean modeloNovo) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from Agendamento obj where obj.professor.ativo = TRUE ");
            if (inicio != null) {
                query.append(" and :inicio between obj.inicio and obj.fim ");
                p.put("inicio", inicio);
            }

            if(!UteisValidacao.emptyString(nome)){
                query.append(" and (");
                query.append(" upper(obj.professor.nome) like '%").append(nome.toUpperCase()).append("%'");
                query.append(" or upper(obj.cliente.nome) like '%").append(nome.toUpperCase()).append("%')");
            }
            if (professores != null && !professores.isEmpty()) {
                if(SuperControle.independente(ctx)) {
                    query.append(" and obj.professor.codigo IN (").append(professores).append(") ");
                }else{
                    query.append(" and obj.professor.codigoColaborador IN (").append(professores).append(") ");
                }
            }

            if (tipos != null && !tipos.isEmpty() && !modeloNovo) {
                query.append(" and obj.tipoEvento.codigo IN (").append(tipos).append(") ");
            }

            if (tipos != null && !tipos.isEmpty() && modeloNovo) {
                query.append(" and obj.horarioDisponibilidade.disponibilidade.codigo IN (").append(tipos).append(") ");
            }

            if (status != null && !status.isEmpty()) {
                query.append(" and obj.status IN (").append(status).append(") ");
            } else if (ignorarCancelados) {
                query.append(" and obj.status NOT IN (").append(StatusAgendamentoEnum.CANCELADO.ordinal()).append(") ");
            }
            if (codigoCliente != null && codigoCliente > 0) {
                query.append(" and obj.cliente.codigo = :codigocliente ");
                p.put("codigocliente", codigoCliente);
            }
            if (disponibilidades != null && disponibilidades) {
                query.append(" and obj.disponibilidade is true ");
            }
            if (disponibilidades != null && !disponibilidades) {
                query.append(" and obj.disponibilidade is false and obj.cliente.codigo is not null");
            }
            if (empresaZW != null) {
                if (SuperControle.independente(ctx)) {
                    query.append(" and obj.professor.empresa.codigo = ").append(empresaZW);
                } else {
                    query.append(" and obj.professor.empresa.codZW = ").append(empresaZW);
                }
            }
            query.append(orderBy == null ? " order by obj.inicio " : query.append(" order by ").append(orderBy));

            if (maxResults != null && index != null) {
                return obterPorParam(ctx, query.toString(), p, maxResults, index);
            } else {
                return obterPorParam(ctx, query.toString(), p);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Agendamento> consultarPrevistosNosProximosMinutos(final String ctx,
                                                                  final Date dataBase, Integer professor, Integer aluno,
                                                                  Integer start,
                                                                  TipoLembreteEnum tipoLembrete) throws ServiceException {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("dataInicio", dataBase);
        p.put("tipoLembrete", tipoLembrete);
        p.put("status1", StatusAgendamentoEnum.CANCELADO);
        p.put("status2", StatusAgendamentoEnum.FALTOU);
        StringBuilder hql = new StringBuilder("select o from " + Agendamento.class.getSimpleName() + " o ");
        hql.append("where disponibilidade is false ");
        hql.append("and (EXTRACT(EPOCH FROM inicio - :dataInicio").append(")/60) between ").append(start).append(" and ").append(tipoLembrete.getnMinutos()).append(" ");
        hql.append("and codigo not in (select lembrete.agendamento.codigo from LembreteAgendamento lembrete where tipo = :tipoLembrete) ");
        hql.append("and status not in (:status1,:status2) and codigo not in (select lembrete.agendamento.codigo from LembreteAgendamento lembrete where tipo = :tipoLembrete) ");
        if (professor != null) {
            hql.append(" and o.professor.codigo = :professor ");
            p.put("professor", professor);
        }
        if (aluno != null) {
            hql.append(" and o.cliente.codigo = :aluno ");
            p.put("aluno", aluno);
        }
        try {
            return obterPorParam(ctx, hql.toString(), p);
        } catch (ServiceException ex) {
            Logger.getLogger(AgendamentoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Agendamento> consultarAgendamentosNovosOuAlterados(final String ctx,
                                                                   Integer professor, Integer aluno,
                                                                   TipoLembreteEnum tipoLembrete) throws ServiceException {
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("status1", StatusAgendamentoEnum.CANCELADO);
        p.put("status2", StatusAgendamentoEnum.FALTOU);
        StringBuilder hql = new StringBuilder("select o from " + Agendamento.class.getSimpleName() + " o ");
        hql.append("where disponibilidade is false ");
        hql.append("and inicio >= CURRENT_DATE ");
        hql.append("and status not in (:status1,:status2) ");
        if (professor != null) {
            hql.append(" and agendamento.professor.codigo = :professor ");
            p.put("professor", professor);
        }
        if (aluno != null) {
            hql.append("and o.cliente.codigo = :aluno ");
            p.put("aluno", aluno);
        }
        if (tipoLembrete == TipoLembreteEnum.AGENDAMENTO_NOVO) {
            hql.append("and codigo not in (select lembrete.agendamento.codigo from LembreteAgendamento lembrete where lembrete.agendamento.codigo is not null) ");
        }
        if (tipoLembrete == TipoLembreteEnum.AGENDAMENTO_ALTERADO) {
            p.put("tipoLembrete", tipoLembrete);
            hql.append("and usuarioUltAlteracao_codigo is not null and ultimaAlteracao is not null and dataLancamento is not null and (EXTRACT(EPOCH FROM current_timestamp - ultimaAlteracao)/60) <= ").append(tipoLembrete.getnMinutos()).append(" ");
            hql.append("and codigo not in (select lembrete.agendamento.codigo from LembreteAgendamento lembrete where lembrete.agendamento.codigo is not null and tipo = :tipoLembrete and cast(dataCriacao as date) = CURRENT_DATE) ");
        }

        try {
            if (tipoLembrete == TipoLembreteEnum.AGENDAMENTO_NOVO || tipoLembrete == TipoLembreteEnum.AGENDAMENTO_ALTERADO) {
                return obterPorParam(ctx, hql.toString(), p);
            }
        } catch (ServiceException ex) {
            Logger.getLogger(AgendamentoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
        return null;
    }

    public List<Agendamento> consultarDisponibilidadeV2(final String ctx, Date inicio, Date fim, Integer professor,
                                                        List<HorarioDisponibilidade> tipos, boolean between, boolean inside, final Integer empresaZW) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            if (inside) {
                query.append(" where obj.inicio >= :inicio and obj.fim <= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            } else if (between) {
                query.append(" where obj.inicio <= :inicio and obj.fim > :fim and obj.disponibilidade IS true  ");
            } else {
                query.append(" where obj.inicio <= :inicio and obj.fim >= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            }
            if (professor != null && professor > 0) {
                query.append(" and obj.professor.codigo = :professor ");
            }
            if (empresaZW != null) {
                query.append(" and obj.professor.empresa.codZW = ").append(empresaZW);
            }
            if (tipos != null && !tipos.isEmpty()) {
                String tiposSelecionados = Uteis.getListaEscolhidos(tipos, "Escolhido", "CodigoDisponibilidade", true, false);
                if (tiposSelecionados != null && !tiposSelecionados.isEmpty()) {
                    query.append(" and obj.horarioDisponibilidade.disponibilidade.codigo IN (").append(tiposSelecionados).append(") ");
                }

            }

            query.append(" order by obj.inicio ");
            if(!inside && between){
                p.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, 29));
                p.put("fim", inicio);
            }else{
                p.put("inicio", inicio);
            }

            if (professor != null && professor > 0) {
                p.put("professor", professor);
            }
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> consultarDisponibilidade(final String ctx, Date inicio, Date fim, Integer professor,
                                                      List<TipoEvento> tipos, boolean between, boolean inside, final Integer empresaZW) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            if (inside) {
                query.append(" where obj.inicio >= :inicio and obj.fim <= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            } else if (between) {
                query.append(" where obj.inicio <= :inicio and obj.fim > :fim and obj.disponibilidade IS true  ");
            } else {
                query.append(" where obj.inicio <= :inicio and obj.fim >= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            }
            if (professor != null && professor > 0) {
                query.append(" and obj.professor.codigo = :professor ");
            }
            if (empresaZW != null) {
                query.append(" and obj.professor.empresa.codZW = ").append(empresaZW);
            }
            if (tipos != null && !tipos.isEmpty()) {
                String tiposSelecionados = Uteis.getListaEscolhidos(tipos, "Escolhido", "Codigo", true, false);
                if (tiposSelecionados != null && !tiposSelecionados.isEmpty()) {
                    query.append(" and obj.tipoEvento.codigo IN (").append(tiposSelecionados).append(") ");
                }

            }

            query.append(" and obj.tipoEvento.ativo is true  order by obj.inicio ");
            if(!inside && between){
                p.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, 29));
                p.put("fim", inicio);
            }else{
                p.put("inicio", inicio);
            }

            if (professor != null && professor > 0) {
                p.put("professor", professor);
            }
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> verificarEventosConcomitantesV2(final String ctx, Date inicio, Date fim,
                                                             Integer professor, Integer codigoEvento) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from Agendamento obj ");
            query.append(" WHERE ((obj.inicio <= :inicio and obj.fim >= :fim) ");
            query.append(" OR (obj.inicio BETWEEN :inicio and  :fim) ");
            query.append(" OR (obj.fim BETWEEN :inicio and  :fim)) ");
            query.append(" AND obj.status <> ").append(StatusAgendamentoEnum.CANCELADO.getId());
            query.append(" AND obj.status <> ").append(StatusAgendamentoEnum.REAGENDADO.getId());
            query.append(" AND obj.disponibilidade is not true ");
            if (professor != null && professor > 0) {
                p.put("professor", professor);
                query.append(" and obj.professor.codigo = :professor ");
            }
            if (codigoEvento != null && codigoEvento > 0) {
                p.put("evento", codigoEvento);
                query.append(" and obj.codigo <> :evento ");
            }
            query.append(" order by obj.inicio ");

            p.put("inicio", inicio);
            p.put("fim", fim);
            if (professor != null && professor > 0) {
            }
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Agendamento> verificarEventosConcomitantes(final String ctx, Date inicio, Date fim,
                                                           Integer professor, Integer codigoEvento) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from Agendamento obj ");
            query.append(" left join obj.tipoEvento t ");
            query.append(" left join obj.horarioDisponibilidade h ");
            query.append(" WHERE ((obj.inicio <= :inicio and obj.fim >= :fim) ");
            query.append(" OR (obj.inicio BETWEEN :inicio and  :fim) ");
            query.append(" OR (obj.fim BETWEEN :inicio and  :fim)) ");
            query.append(" AND obj.status <> ").append(StatusAgendamentoEnum.CANCELADO.getId());
            query.append(" AND obj.status <> ").append(StatusAgendamentoEnum.REAGENDADO.getId());
            query.append(" AND obj.disponibilidade is not true ");
            query.append(" AND (\n" +
                    "       (t is not null and t.ativo is true) \n" +
                    "       OR \n" +
                    "       (h is not null and h.ativo is true)\n" +
                    "   ) ");
            if (professor != null && professor > 0) {
                p.put("professor", professor);
                query.append(" and obj.professor.codigo = :professor ");
            }
            if (codigoEvento != null && codigoEvento > 0) {
                p.put("evento", codigoEvento);
                query.append(" and obj.codigo <> :evento ");
            }
            query.append(" order by obj.inicio ");

            p.put("inicio", inicio);
            p.put("fim", fim);
            if (professor != null && professor > 0) {
            }
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Agendamento validaAgendamentoV2(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {

        if (agendamento.getHorarioDisponibilidade() == null) {
            return validaAgendamento(ctx, agendamento, usuario, empresaId);
        }
        if (agendamento.getHorarioDisponibilidade().getDisponibilidade() == null) {
            throw new ValidacaoException("validacao.agenda.tipo");
        }
        if (agendamento.getInicio() == null) {
            throw new ValidacaoException("validacao.agenda.inicio");
        }
        if (agendamento.getFim() == null) {
            throw new ValidacaoException("validacao.agenda.fim");
        }
        if (agendamento.getFim().before(agendamento.getInicio())) {
            throw new ValidacaoException("validacao.agendamento.horaFimMenorHoraInicio");
        }
        if (agendamento.getCliente() != null) {
            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() != 0) {
                AtomicReference<String> codigosValidar = new AtomicReference<>("");
                agendamento.getHorarioDisponibilidade().getDisponibilidade().getItensValidacao().forEach(i -> {
                    codigosValidar.set(codigosValidar.get() + "," + (i.getPlano() != null ? i.getPlano() : i.getProduto()));
                });

                if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() == 1) {
                    if (!Objects.equals(agendamento.getCliente().getSituacao(), "AT")) {
                        throw new ValidacaoException("validacao.agenda.aluno.plano.ativo");
                    }

                    if (!codigosValidar.get().isEmpty()) {
                        try {
                            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                        "select exists (select p.codigo from plano p " +
                                                "inner join contrato c on c.plano = p.codigo " +
                                                "inner join situacaoclientesinteticodw scsc on scsc.codigocontrato = c.codigo " +
                                                "where scsc.codigocliente = " + agendamento.getCliente().getCodigoCliente() +
                                                " and p.codigo in (" + codigosValidar.get().replaceFirst(",", "") + ") limit 1) as existe", conZW)) {
                                    while (rs.next()) {
                                        if (!rs.getBoolean("existe")) {
                                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO.name());
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO.name());
                        }
                    }
                }

                if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() == 2) {
                    if (!codigosValidar.get().isEmpty()) {
                        try {
                            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                        "select exists (select mp.codigo from movproduto mp " +
                                                "where mp.pessoa = " + agendamento.getCliente().getCodigoPessoa() +
                                                " and mp.produto in (" + codigosValidar.get().replaceFirst(",", "") + ") limit 1) as existe", conZW)) {
                                    while (rs.next()) {
                                        if (!rs.getBoolean("existe")) {
                                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO.name());
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO.name());
                        }
                    }
                }
            }
        }

        int duracao = 0;
        if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 1) {
            duracao = agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao();
        }

        if (duracao != 0 && (Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) < duracao &&
                !(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) + 1 == duracao ||
                        Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) + 2 == duracao))) {
            int tempoAtual = Math.toIntExact(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()));
            throw new ValidacaoException("O tempo mínimo para este tipo de agendamento é de " + duracao +
                " minutos. Tempo atual: " + tempoAtual + " minutos.");
        }

        if (agendamento.getProfessor() == null
                || agendamento.getProfessor().getCodigo() == null
                || agendamento.getProfessor().getCodigo().equals(0)) {
            throw new ValidacaoException("validacao.agenda.professor");
        }

        if (!agendamento.getDisponibilidade()) {

            ConfiguracaoSistema cfgAlunosAtivos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
            if (!agendamento.getDisponibilidade() && validacao.emptyNumber(agendamento.getCodigo())) {
                if ((agendamento.getCliente() != null && (agendamento.getCliente().getSituacao().equals("VI")
                        || agendamento.getCliente().getSituacaoContrato().equals("DE")
                        || agendamento.getCliente().getSituacaoContrato().equals("CA")))
                        && cfgAlunosAtivos.getValorAsBoolean() && agendamento.getHorarioDisponibilidade().getDisponibilidade().getComportamento() == 1) {
                    throw new ValidacaoException("obrigatorio.alunoSemSituacaoCompativel");
                }
            }

            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 2 && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                if (!verificarIntervaloComTolerancia(minutosEntreDatas.intValue(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMinima(),
                        agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMaxima(), 2)) {
                    throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
                }
            }

            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 1 && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                if (!compararComTolerancia(minutosEntreDatas.intValue(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao(), 2)) {
                    throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                            agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao()));
                }
            }

            List<HorarioDisponibilidade> tipos = new ArrayList<>();
            tipos.add(agendamento.getHorarioDisponibilidade());
            List<Agendamento> disponibilidades = null;
            if (agendamento.getCodigo() == null || agendamento.getCodigo() == 0) {
                disponibilidades = consultarDisponibilidadeV2(ctx,
                        Uteis.somarCampoData(agendamento.getInicio(), Calendar.SECOND, 1),
                        Uteis.somarCampoData(agendamento.getFim(), Calendar.SECOND, -1),
                        agendamento.getProfessor().getCodigo(),
                        tipos, false, false,
                        empresaId != null ? empresaId : usuario.getEmpresaZW());
                if (disponibilidades.isEmpty()) {
                    throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.name());
                }
            }

            List<Agendamento> concomitantes = verificarEventosConcomitantesV2(ctx, agendamento.getInicio(),
                    agendamento.getFim(), agendamento.getProfessor().getCodigo(), agendamento.getCodigo());

            if (!concomitantes.isEmpty()) {
                for (Agendamento conc : concomitantes) {
                    if (conc.getCliente() == null) {
                        continue;
                    }
                    if (conc.getInicio().getTime() == agendamento.getFim().getTime()) {
                        agendamento.setFim(Uteis.somarCampoData(agendamento.getFim(), Calendar.MINUTE, -1));
                    } else if (conc.getFim().getTime() == agendamento.getInicio().getTime()) {
                        agendamento.setInicio(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 1));
                    } else {
                        throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.name());
                    }
                }
            }

            ConfiguracaoSistema configuracaoSistema = css.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if (configuracaoSistema.getValorAsBoolean()) {
                List<AgendaDisponibilidadeDTO> disponibilidadesDTO = new ArrayList<>();
                AgendaDisponibilidadeDTO disponibilidade = new AgendaDisponibilidadeDTO();
                disponibilidade.setDia(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "yyyyMMdd"));
                disponibilidade.setHorarioInicial(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "HH:mm"));
                disponibilidade.setHorarioFinal(Uteis.getDataAplicandoFormatacao(agendamento.getFim(), "HH:mm"));
                disponibilidadesDTO.add(disponibilidade);
                boolean conflitoEncontrado = verificarBloqueadosAgendamentoAluno(ctx, agendamento.getInicio(), agendamento.getFim(), empresaId, disponibilidadesDTO);

                if (conflitoEncontrado) {
                    throw new ValidacaoException("Conflito de agendamento: esse ambiente já está sendo usado");
                }
            }
        }

        return null;
    }


    public Agendamento validaAgendamentoV2Recorrente(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {

        if (agendamento.getHorarioDisponibilidade() == null) {
            return validaAgendamento(ctx, agendamento, usuario, empresaId);
        }
        if (agendamento.getHorarioDisponibilidade().getDisponibilidade() == null) {
            throw new ValidacaoException("validacao.agenda.tipo");
        }
        if (agendamento.getInicio() == null) {
            throw new ValidacaoException("validacao.agenda.inicio");
        }
        if (agendamento.getFim() == null) {
            throw new ValidacaoException("validacao.agenda.fim");
        }
        if (agendamento.getFim().before(agendamento.getInicio())) {
            throw new ValidacaoException("validacao.agendamento.horaFimMenorHoraInicio");
        }
        if (agendamento.getCliente() != null) {
            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() != 0) {
                AtomicReference<String> codigosValidar = new AtomicReference<>("");
                agendamento.getHorarioDisponibilidade().getDisponibilidade().getItensValidacao().forEach(i -> {
                    codigosValidar.set(codigosValidar.get() + "," + (i.getPlano() != null ? i.getPlano() : i.getProduto()));
                });

                if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() == 1) {
                    if (!Objects.equals(agendamento.getCliente().getSituacao(), "AT")) {
                        throw new ValidacaoException("validacao.agenda.aluno.plano.ativo");
                    }

                    if (!codigosValidar.get().isEmpty()) {
                        try {
                            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                        "select exists (select p.codigo from plano p " +
                                                "inner join contrato c on c.plano = p.codigo " +
                                                "inner join situacaoclientesinteticodw scsc on scsc.codigocontrato = c.codigo " +
                                                "where scsc.codigocliente = " + agendamento.getCliente().getCodigoCliente() +
                                                " and p.codigo in (" + codigosValidar.get().replaceFirst(",", "") + ") limit 1) as existe", conZW)) {
                                    while (rs.next()) {
                                        if (!rs.getBoolean("existe")) {
                                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO.name());
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO.name());
                        }
                    }
                }

                if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoValidacao() == 2) {
                    if (!codigosValidar.get().isEmpty()) {
                        try {
                            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                        "select exists (select mp.codigo from movproduto mp " +
                                                "where mp.pessoa = " + agendamento.getCliente().getCodigoPessoa() +
                                                " and mp.produto in (" + codigosValidar.get().replaceFirst(",", "") + ") limit 1) as existe", conZW)) {
                                    while (rs.next()) {
                                        if (!rs.getBoolean("existe")) {
                                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO.name());
                                        }
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO.name());
                        }
                    }
                }
            }
        }

        int duracao = 0;
        if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 1) {
            duracao = agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao();
        }

        if (duracao != 0 && (Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) < duracao &&
                !(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) + 1 == duracao ||
                        Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) + 2 == duracao))) {
            int tempoAtual = Math.toIntExact(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()));
            throw new ValidacaoException("O tempo mínimo para este tipo de agendamento é de " + duracao +
                " minutos. Tempo atual: " + tempoAtual + " minutos.");
        }

        if (agendamento.getProfessor() == null
                || agendamento.getProfessor().getCodigo() == null
                || agendamento.getProfessor().getCodigo().equals(0)) {
            throw new ValidacaoException("validacao.agenda.professor");
        }

        if (!agendamento.getDisponibilidade()) {

            ConfiguracaoSistema cfgAlunosAtivos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
            if (!agendamento.getDisponibilidade() && validacao.emptyNumber(agendamento.getCodigo())) {
                if ((agendamento.getCliente() != null && (agendamento.getCliente().getSituacao().equals("VI")
                        || agendamento.getCliente().getSituacaoContrato().equals("DE")
                        || agendamento.getCliente().getSituacaoContrato().equals("CA")))
                        && cfgAlunosAtivos.getValorAsBoolean() && agendamento.getHorarioDisponibilidade().getDisponibilidade().getComportamento() == 1) {
                    throw new ValidacaoException("obrigatorio.alunoSemSituacaoCompativel");
                }
            }

            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 2 && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                if (!verificarIntervaloComTolerancia(minutosEntreDatas.intValue(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMinima(),
                        agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMaxima(), 2)) {
                    throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
                }
            }

            if (agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario() == 1 && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                if (!compararComTolerancia(minutosEntreDatas.intValue(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao(), 2)) {
                    throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                            agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao()));
                }
            }

            ConfiguracaoSistema configuracaoSistema = css.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if (configuracaoSistema.getValorAsBoolean()) {
                List<AgendaDisponibilidadeDTO> disponibilidadesDTO = new ArrayList<>();
                AgendaDisponibilidadeDTO disponibilidade = new AgendaDisponibilidadeDTO();
                disponibilidade.setDia(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "yyyyMMdd"));
                disponibilidade.setHorarioInicial(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "HH:mm"));
                disponibilidade.setHorarioFinal(Uteis.getDataAplicandoFormatacao(agendamento.getFim(), "HH:mm"));
                disponibilidadesDTO.add(disponibilidade);
                boolean conflitoEncontrado = verificarBloqueadosAgendamentoAluno(ctx, agendamento.getInicio(), agendamento.getFim(), empresaId, disponibilidadesDTO);

                if (conflitoEncontrado) {
                    throw new ValidacaoException("Conflito de agendamento: esse ambiente já está sendo usado");
                }
            }
        }

        return null;
    }


    public Date validaDisponibilidadeAgendamentoRecorrente(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {

        List<HorarioDisponibilidade> tipos = new ArrayList<>();
        tipos.add(agendamento.getHorarioDisponibilidade());
        List<Agendamento> disponibilidades = null;
        if (agendamento.getCodigo() == null || agendamento.getCodigo() == 0) {
            disponibilidades = consultarDisponibilidadeV2(ctx,
                    Uteis.somarCampoData(agendamento.getInicio(), Calendar.SECOND, 1),
                    Uteis.somarCampoData(agendamento.getFim(), Calendar.SECOND, -1),
                    agendamento.getProfessor().getCodigo(),
                    tipos, false, false,
                    empresaId != null ? empresaId : usuario.getEmpresaZW());
            if (disponibilidades.isEmpty()) {
                return agendamento.getInicio();
            }
        }

        List<Agendamento> concomitantes = verificarEventosConcomitantesV2(ctx, agendamento.getInicio(),
                agendamento.getFim(), agendamento.getProfessor().getCodigo(), agendamento.getCodigo());

        if (!concomitantes.isEmpty()) {
            for (Agendamento conc : concomitantes) {
                if (conc.getCliente() == null) {
                    continue;
                }
                if (conc.getInicio().getTime() == agendamento.getFim().getTime()) {
                    agendamento.setFim(Uteis.somarCampoData(agendamento.getFim(), Calendar.MINUTE, -1));
                } else if (conc.getFim().getTime() == agendamento.getInicio().getTime()) {
                    agendamento.setInicio(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 1));
                } else {
                    return agendamento.getInicio();
                }
            }
        }

        return null;
    }









    public boolean verificarBloqueadosAgendamentoAluno(String ctx,
                                                       Date inicio,
                                                       Date fim,
                                                       Integer empresaId,
                                                       List<AgendaDisponibilidadeDTO> locacoes) {

        locacaoService.verificarBloqueadosLocacao(ctx, inicio, fim, empresaId, locacoes);

        return locacoes.stream().anyMatch(AgendaDisponibilidadeDTO::getBloqueado);

    }

    public Agendamento validaAgendamento(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ValidacaoException, ServiceException {
        if (agendamento.getTipoEvento() == null) {
            throw new ValidacaoException("validacao.agenda.tipo");
        }
        if (agendamento.getInicio() == null) {
            throw new ValidacaoException("validacao.agenda.inicio");
        }
        if (agendamento.getFim() == null) {
            throw new ValidacaoException("validacao.agenda.fim");
        }
        if (agendamento.getFim().before(agendamento.getInicio())) {
            throw new ValidacaoException("validacao.agendamento.horaFimMenorHoraInicio");
        }
        int duracao = 0;
        if (!agendamento.getTipoEvento().getDuracao().name().equals("DURACAO_LIVRE")){
            duracao = agendamento.getTipoEvento().getDuracaoMinutosMin();
        }

        if (duracao != 0 && (Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) < duracao &&
                !(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim())+1 == duracao ||
                        Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim())+2 == duracao))) {
            int tempoAtual = Math.toIntExact(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()));
            throw new ValidacaoException("O tempo mínimo para este tipo de agendamento é de " + duracao +
                " minutos. Tempo atual: " + tempoAtual + " minutos.");
        }

        if (agendamento.getProfessor() == null
                || agendamento.getProfessor().getCodigo() == null
                || agendamento.getProfessor().getCodigo().equals(0)) {
            throw new ValidacaoException("validacao.agenda.professor");
        }

        if (!agendamento.getDisponibilidade()) {
            ConfiguracaoSistema cfgAlunosAtivos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
            if (!agendamento.getDisponibilidade() && validacao.emptyNumber(agendamento.getCodigo())) {
                if ((agendamento.getCliente() != null && (agendamento.getCliente().getSituacao().equals("VI")
                        || agendamento.getCliente().getSituacaoContrato().equals("DE")
                        || agendamento.getCliente().getSituacaoContrato().equals("CA")))
                        && cfgAlunosAtivos.getValorAsBoolean() && agendamento.getTipoEvento().getComportamento().getDescricao().equals("Prescrição de treino")) {
                    throw new ValidacaoException("obrigatorio.alunoSemSituacaoCompativel");
                }
            }
            if (agendamento.getTipoEvento().getDuracaoIntervalo() && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!verificarIntervaloComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(),
                        agendamento.getTipoEvento().getDuracaoMinutosMax(), 2)) {
                    throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
                }
            }
            if (agendamento.getTipoEvento().getDuracaoPreDefinida() && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!compararComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(), 2)) {
                    throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                            agendamento.getTipoEvento().getDuracaoMinutosMin()));
                }
            }


            List<TipoEvento> tipos = new ArrayList<TipoEvento>();
            tipos.add(agendamento.getTipoEvento());
            List<Agendamento> disponibilidades = null;
            if (agendamento.getCodigo() == null || agendamento.getCodigo() == 0) {
                disponibilidades = consultarDisponibilidade(ctx,
                        Uteis.somarCampoData(agendamento.getInicio(), Calendar.SECOND, 1),
                        Uteis.somarCampoData(agendamento.getFim(), Calendar.SECOND, -1),
                        agendamento.getProfessor().getCodigo(),
                        tipos, false, false,
                        empresaId != null ? empresaId : usuario.getEmpresaZW());
                if (disponibilidades.isEmpty()) {
                    throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.name());
                }
            }

            List<Agendamento> concomitantes = verificarEventosConcomitantes(ctx, agendamento.getInicio(),
                    agendamento.getFim(), agendamento.getProfessor().getCodigo(), agendamento.getCodigo());

            if (!concomitantes.isEmpty()) {
                for (Agendamento conc : concomitantes) {
                    if(conc.getCliente() == null){
                        continue;
                    }
                    if (conc.getInicio().getTime() == agendamento.getFim().getTime()) {
                        agendamento.setFim(Uteis.somarCampoData(agendamento.getFim(), Calendar.MINUTE, -1));
                    } else if (conc.getFim().getTime() == agendamento.getInicio().getTime()) {
                        agendamento.setInicio(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 1));
                    } else {
                        throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.name());
                    }
                }
            }

            return disponibilidades != null ? disponibilidades.get(0) : null;

        }
        return null;

    }

    public Agendamento validaAgendamentoEmpresas(final String ctx, Agendamento agendamento, Integer empresaId, Date date) throws ValidacaoException, ServiceException {
        if (agendamento.getTipoEvento() == null  && agendamento.getHorarioDisponibilidade() == null) {
            throw new ValidacaoException("validacao.agenda.tipo");
        }
        if (agendamento.getInicio() == null) {
            throw new ValidacaoException("validacao.agenda.inicio");
        }
        if (agendamento.getFim() == null) {
            throw new ValidacaoException("validacao.agenda.fim");
        }
        if (agendamento.getFim().before(agendamento.getInicio())) {
            throw new ValidacaoException("validacao.agendamento.horaFimMenorHoraInicio");
        }
        int duracao = 0;
        if (agendamento.getTipoEvento() != null && !agendamento.getTipoEvento().getDuracao().name().equals("DURACAO_LIVRE")){
            duracao = agendamento.getTipoEvento().getDuracaoMinutosMin();
        }
        if (agendamento.getHorarioDisponibilidade() != null
                && !agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario().equals(TipoDuracaoEvento.DURACAO_LIVRE.ordinal())) {
            duracao = agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracao();
        }

        if (duracao != 0 && (Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) < duracao &&
                !(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim())+1 == duracao))) {
            int tempoAtual = Math.toIntExact(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()));
            throw new ValidacaoException("O tempo mínimo para este tipo de agendamento é de " + duracao +
                " minutos. Tempo atual: " + tempoAtual + " minutos.");
        }

        if (agendamento.getProfessor() == null
                || agendamento.getProfessor().getCodigo() == null
                || agendamento.getProfessor().getCodigo().equals(0)) {
            throw new ValidacaoException("validacao.agenda.professor");
        }

        if (!agendamento.getDisponibilidade()) {
            if (agendamento.getCliente() == null
                    || agendamento.getCliente().getCodigo() == null
                    || agendamento.getCliente().getCodigo().equals(0)) {
                throw new ValidacaoException("validacao.agenda.cliente");
            }

            ConfiguracaoSistema cfgAlunosAtivos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
            if (!agendamento.getDisponibilidade() && validacao.emptyNumber(agendamento.getCodigo())) {
                if ((agendamento.getCliente().getSituacao().equals("VI")
                        || agendamento.getCliente().getSituacaoContrato().equals("DE")
                        || agendamento.getCliente().getSituacaoContrato().equals("CA"))
                        && cfgAlunosAtivos.getValorAsBoolean()) {
                    throw new ValidacaoException("obrigatorio.alunoSemSituacaoCompativel");
                }
            }
            if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getDuracaoIntervalo() && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!verificarIntervaloComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(),
                        agendamento.getTipoEvento().getDuracaoMinutosMax(), 2)) {
                    throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
                }
            }

            if (agendamento.getHorarioDisponibilidade() != null
                    && agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario().equals(TipoDuracaoEvento.INTERVALO_DE_TEMPO.ordinal())
                    && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!verificarIntervaloComTolerancia(
                        minutosEntreDatas.intValue(),
                        agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMinima(),
                        agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMaxima(),
                        2)
                ) {
                    throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
                }
            }


            if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getDuracaoPreDefinida() && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!compararComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(), 2)) {
                    throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                            agendamento.getTipoEvento().getDuracaoMinutosMin()));
                }
            }

            if (agendamento.getHorarioDisponibilidade() != null
                    && agendamento.getHorarioDisponibilidade().getDisponibilidade().getTipoHorario().equals(TipoDuracaoEvento.DURACAO_PREDEFINIDA.ordinal())
                    && !agendamento.getDisponibilidade()) {
                Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
                //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
                if (!compararComTolerancia(
                        minutosEntreDatas.intValue(),
                        agendamento.getHorarioDisponibilidade().getDisponibilidade().getDuracaoMinima(),
                        2)
                ) {
                    throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                            agendamento.getTipoEvento().getDuracaoMinutosMin()));
                }
            }


            List<TipoEvento> tipos = new ArrayList<TipoEvento>();
            if (agendamento.getTipoEvento() != null) {
                tipos.add(agendamento.getTipoEvento());
            }
            List<Agendamento> disponibilidades = null;
            if (agendamento.isMudouDatas()) {
                disponibilidades = disponibilidadeProfessor(ctx,
                        agendamento.getProfessor().getCodigo(), date);
                if (disponibilidades.isEmpty()) {
                    throw new ValidacaoException("validacao.agenda.disponibilidade");
                }
            }

            List<Agendamento> concomitantes = verificarEventosConcomitantes(ctx, agendamento.getInicio(),
                    agendamento.getFim(), agendamento.getProfessor().getCodigo(), agendamento.getCodigo());

            if (!concomitantes.isEmpty()) {
                for (Agendamento conc : concomitantes) {
                    if (conc.getInicio().getTime() == agendamento.getFim().getTime()) {
                        agendamento.setFim(Uteis.somarCampoData(agendamento.getFim(), Calendar.MINUTE, -1));
                    } else if (conc.getFim().getTime() == agendamento.getInicio().getTime()) {
                        agendamento.setInicio(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 1));
                    } else {
                        throw new ValidacaoException("validacao.agenda.concomitante");
                    }
                }
            }

            return disponibilidades != null ? disponibilidades.get(0) : null;

        }
        return null;

    }

    public boolean compararComTolerancia(Integer valor1, Integer valor2, Integer tolerancia) {
        return valor2 > valor1 ? (valor2 - valor1) <= tolerancia : (valor1 - valor2) <= tolerancia;
    }

    public boolean verificarIntervaloComTolerancia(Integer valor1, Integer valorMin, Integer valorMax, Integer tolerancia) {
        return (valor1 >= valorMin && valor1 <= valorMax) || ((valor1 - tolerancia) > valorMin && (valor1 - tolerancia) <= valorMax)
                || ((valor1 + tolerancia) > valorMin && (valor1 + tolerancia) <= valorMax);
    }

    public void gravarAgendamentoAvaliacao(String ctx, Agendamento agendamento, Usuario usuario, AvaliacaoFisica avaliacaoFisica) throws Exception{
        agendamento.setProfessor((agendamento.getProfessorCod() != null && agendamento.getProfessorCod() > 0)
                ? getProfessorSinteticoService().obterPorId(ctx, agendamento.getProfessorCod()) : null);
        agendamento.atualizarInicioFim();
        TipoEvento tipo = tipoEventoService.obterPorId(ctx, agendamento.getTipoEventoCod());
        agendamento.setTipoEvento(tipo);
        agendamento.setDiaSemana(Uteis.getDiaDaSemanaNumero(agendamento.getInicio()));

        agendamento = inserir(ctx, agendamento, usuario);
        avaliacaoFisica.setAgendamentoReavaliacao(agendamento);
        AvaliacaoFisicaService as = (AvaliacaoFisicaService) UtilContext.getBean(AvaliacaoFisicaService.class);
        avaliacaoFisica = as.alterar(ctx, avaliacaoFisica);
    }

    public ConfirmacaoAgendamentoJSON lancarAgendamentoPeloAluno(final String ctx,
                                                                 Date data,
                                                                 final String horario,
                                                                 final Integer professor,
                                                                 final String matricula,
                                                                 final Integer empresa,
                                                                 final Integer tipoEvento) throws Exception{

        ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);
        Integer venda = null;
        data = Calendario.getDataComHoraZerada(data);
        Agendamento agendamento = new Agendamento();
        agendamento.setProfessorCod(professor);
        agendamento.setTipoEventoCod(tipoEvento);
        agendamento.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
        agendamento.setDataAlterar(data);
        String[] splithr = horario.split(" - ");
        agendamento.setHoraInicio(splithr[0].split(":")[0]);
        agendamento.setMinutoInicio(splithr[0].split(":")[1]);
        agendamento.setHoraFim(splithr[1].split(":")[0]);
        agendamento.setMinutoFim(splithr[1].split(":")[1]);
        agendamento.setInicio(Calendario.getDataComHora(data, splithr[0]));
        agendamento.setFim(Calendario.getDataComHora(data, splithr[1]));
        agendamento.setCliente(clienteSintetico);
        agendamento.setDataLancamento(Calendario.hoje());
        agendamento.setProfessor((agendamento.getProfessorCod() != null && agendamento.getProfessorCod() > 0)
                ? getProfessorSinteticoService().obterPorId(ctx, agendamento.getProfessorCod()) : null);
        agendamento.atualizarInicioFim();
        TipoEvento tipo = tipoEventoService.obterPorId(ctx, agendamento.getTipoEventoCod());
        agendamento.setTipoEvento(tipo);
        agendamento.setDiaSemana(Uteis.getDiaDaSemanaNumero(agendamento.getInicio()));
        Usuario u = new Usuario();
        u.setEmpresaZW(empresa);

        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfgTipo = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
        if (cfgTipo.getValorAsBoolean()) {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            StringBuilder horaMinutoInicio = new StringBuilder("");
            horaMinutoInicio.append(agendamento.getHoraInicio()).append(":").append(agendamento.getMinutoInicio());
            StringBuilder horaMinutoFim = new StringBuilder("");
            horaMinutoFim.append(agendamento.getHoraFim()).append(":").append(agendamento.getMinutoFim());
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String dados = integracaoWS.consultarAulaPeloClienteHorario(ctx, clienteSintetico.getMatricula().toString(), agendamento.getDataLancamento(), horaMinutoInicio.toString(), horaMinutoFim.toString());
            if (!dados.contains("ERRO")) {
                JSONArray jsonArray = new JSONArray(dados);
                if (jsonArray.length() > 0) {
                    StringBuilder retorno = new StringBuilder("");
                    for (int i = 0; i < jsonArray.length(); i++) {
                        JSONObject jObj = jsonArray.optJSONObject(i);
                        if (retorno.toString().isEmpty()) {
                            retorno.append(jObj.getString("identificadorturma"));
                        }else{
                            retorno.append(",").append(jObj.getString("identificadorturma"));
                        }
                    }
                    throw new Exception("Você já possui compromissos nesse horário: "+retorno);
                }
            }else{
                throw new Exception(dados);
            }
        }
        agendamento = inserir(ctx, agendamento, u);

        //verificar se deve ser lançado agendamento pro aluno
        ConfiguracaoSistema cfgLancarProduto = css.consultarPorTipo(ctx, ConfiguracoesEnum.LANCAR_PRODUTO_AVALIACAO);
        ConfiguracaoSistema cfgLancarDtVencimentoParcela = css.consultarPorTipo(ctx, ConfiguracoesEnum.LANCAR_PRODUTO_AVALIACAO_DATA_VENCIMENTO);
        ConfiguracaoSistema cfgProduto = css.consultarPorTipo(ctx, ConfiguracoesEnum.PRODUTO_AVALIACAO);
        if(cfgLancarProduto.getValorAsBoolean() && !UteisValidacao.emptyNumber(cfgProduto.getValorAsInteger())){

            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            Date dataVencimento = data;
            if (!cfgLancarDtVencimentoParcela.getValorAsBoolean()) {
                dataVencimento = Calendario.hoje();
            }
            String lancarProdutoAluno = integracaoWS.lancarProdutoAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx,
                    clienteSintetico.getCodigoCliente(),
                    cfgProduto.getValorAsInteger(),
                    0,
                    dataVencimento);
            if(lancarProdutoAluno.startsWith("ok")){
                String[] split = lancarProdutoAluno.split("\\|");
                venda = (Integer.valueOf(split[1]));
            }else{
                throw new Exception(lancarProdutoAluno);
            }
        }

        ConfirmacaoAgendamentoJSON c = new ConfirmacaoAgendamentoJSON();
        c.setDiaHora(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "dd/MM/yyyy HH:mm"));
        c.setLancamento(Uteis.getDataAplicandoFormatacao(agendamento.getDataLancamento(), "dd/MM/yyyy HH:mm"));
        c.setProfessor(agendamento.getProfessor().getNome());
        c.setVenda(venda == null ? "" : venda.toString());
        return c;

    }


    public void adicionarAlterarAgendamento(String ctx, Agendamento agendamento,
                                            boolean coordenador, Map<Date, List<Agendamento>> agendamentos,
                                            List<GenericoTO> diasSemana, FiltrosAgendaTO filtros,
                                            final ClienteSintetico clienteEscolhido, Usuario usuario)
            throws ValidacaoException, ServiceException {

        agendamento.setProfessor((agendamento.getProfessorCod() != null && agendamento.getProfessorCod() > 0)
                ? getProfessorSinteticoService().obterPorId(ctx, agendamento.getProfessorCod()) : null);

        if (agendamento.getDisponibilidade()) {
            if (filtros.getTiposGenerico().isEmpty()) {
                GenericoTO tipo = new GenericoTO(agendamento.getTipoEvento().getCodigo(), agendamento.getTipoEvento().getNome());
                tipo.setEscolhido(true);
                filtros.getTiposGenerico().add(tipo);
            }
            Boolean editarAgendamento = true;
            if(agendamento.getCodigo()==null || agendamento.getCodigo()==0)
                editarAgendamento = false;
            //criar um agendamento para cada tipo
            boolean escolheuAlgum = false;
            for (GenericoTO tipo : filtros.getTiposGenerico()) {
                if (tipo.getEscolhido()) {
                    agendamento.atualizarInicioFim();
                    agendamento = agendamento.getClone(null, null, editarAgendamento);
                    escolheuAlgum = true;
                    TipoEvento tipoEvento = tipoEventoService.obterPorId(ctx, tipo.getCodigo());
                    prepararInclusao(ctx, agendamento, agendamentos, tipoEvento, diasSemana, usuario);

                }
            }
            if(!escolheuAlgum){
                throw new ValidacaoException("informe.tipo.evento");
            }
            if (agendamento.getRepetir()) {
                if (agendamento.getRepetirAte() == null) {
                    throw new ValidacaoException("repetirAteObg");
                }
                repetir(ctx, agendamento, agendamento.getRepetirAte(),
                        agendamentos, true, diasSemana, true, false, usuario);
            }
        } else {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            ConfiguracaoSistema cfTipo = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
            if (cfTipo.getValorAsBoolean()) {
                try {
                    ClienteSintetico clienteSintetico = null;
                    if (clienteEscolhido != null) {
                        clienteSintetico = clienteService.obterPorId(ctx, clienteEscolhido.getCodigo());
                    }else if(filtros.getCliente() != null && !UteisValidacao.emptyNumber(filtros.getCliente().getCodigo())){
                        clienteSintetico = clienteService.obterPorId(ctx, filtros.getCliente().getCodigo());
                    }else if(agendamento.getCliente() != null && !UteisValidacao.emptyNumber(agendamento.getCliente().getCodigo())){
                        clienteSintetico = clienteService.obterPorId(ctx, agendamento.getCliente().getCodigo());
                    }else if(!UteisValidacao.emptyNumber(agendamento.getAlunoCod())){
                        clienteSintetico = clienteService.obterPorId(ctx, agendamento.getAlunoCod());
                    }
                    if (clienteSintetico != null) {
                        StringBuilder horaMinutoInicio = new StringBuilder("");
                        horaMinutoInicio.append(agendamento.getHoraInicio()).append(":").append(agendamento.getMinutoInicio());
                        StringBuilder horaMinutoFim = new StringBuilder("");
                        horaMinutoFim.append(agendamento.getHoraFim()).append(":").append(agendamento.getMinutoFim());

                        if (!SuperControle.independente(ctx)) {
                            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                            String dados = integracaoWS.consultarAulaPeloClienteHorario(ctx, clienteSintetico.getMatricula().toString(), agendamento.getDataAlterar(), horaMinutoInicio.toString(), horaMinutoFim.toString());
                            if (!dados.contains("ERRO")) {
                                JSONArray jsonArray = new JSONArray(dados);
                                if (jsonArray.length() > 0) {
                                    StringBuilder retorno = new StringBuilder("");
                                    for (int i = 0; i < jsonArray.length(); i++) {
                                        JSONObject jObj = jsonArray.optJSONObject(i);
                                        if (retorno.toString().isEmpty()) {
                                            retorno.append(jObj.getString("identificadorturma"));
                                        }else{
                                            retorno.append(",").append(jObj.getString("identificadorturma"));
                                        }
                                    }
                                    throw new ServiceException("O aluno já possui compromissos nesse horário: "+retorno);
                                }
                            }else{
                                throw new ServiceException(dados);
                            }
                        }
                    }
                } catch (Exception ex) {
                    throw new ServiceException(ex.getMessage());
                }
            }
            if (UteisValidacao.emptyNumber(agendamento.getTipoEventoCod())) {
                throw new ValidacaoException("validacao.agenda.tipo");
            }
            TipoEvento tipo = tipoEventoService.obterPorId(ctx, agendamento.getTipoEventoCod());
            if (agendamento.getCodigo() == null || agendamento.getCodigo().intValue() == 0) {
                if (clienteEscolhido != null) {
                    agendamento.setAlunoCod(clienteEscolhido.getCodigo());
                } else if (!tipo.getApenasAlunosCarteira() && filtros.getCliente() != null) {
                    agendamento.setAlunoCod(filtros.getCliente().getCodigo());
                }
            }
            prepararInclusao(ctx, agendamento, agendamentos, tipo, diasSemana, usuario);
        }

    }

    private void prepararInclusao(String ctx, Agendamento agendamento, Map<Date, List<Agendamento>> agendamentos,
                                  TipoEvento tipo, List<GenericoTO> diasSemana, Usuario usuario) throws ValidacaoException, ServiceException {

        agendamento.setCliente((agendamento.getAlunoCod() != null && agendamento.getAlunoCod() > 0)
                ? getClienteService().obterPorId(ctx, agendamento.getAlunoCod()) : null);
        agendamento.atualizarInicioFim();
        agendamento.setTipoEvento(tipo);
        agendamento.setDiaSemana(Uteis.getDiaDaSemanaNumero(agendamento.getInicio()));
        if (agendamento.getCodigo() == null || agendamento.getCodigo().equals(0)) {
            agendamento = inserir(ctx, agendamento, usuario);
            if (agendamento.getNsu() == null) {
                agendamento.setNsu(agendamento.getCodigo());
                gravarNSU(ctx, agendamento.getCodigo());
            }
            adicionarAoMapaAgendamentos(agendamentos, agendamento);
        } else {
            agendamento = alterar(ctx, agendamento, usuario);
        }

    }

    public void repetir(String ctx, Agendamento evento,
                        Date dataLimite, Map<Date, List<Agendamento>> agendamentos,
                        boolean usarDiasSemana, List<GenericoTO> diasSemana,
                        boolean aplicarNSU, boolean apenasOutrosTipos, Usuario usuario) throws ValidacaoException, ServiceException {

        //obter dias da semana marcados
        List<Integer> diasSelecionados = new ArrayList<Integer>();
        if (usarDiasSemana) {
            for (GenericoTO gen : diasSemana) {
                if (gen.getEscolhido()) {
                    diasSelecionados.add(gen.getCodigo());
                }
            }
        }
        List<Date> diasEntreDatas;
        evento.setarTransients();
        try {
            diasEntreDatas = Uteis.getDiasEntreDatas(evento.getInicio(), dataLimite);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
        List<TipoEvento> tipos = aplicarNSU ? tipoEventoService.consultarTiposNoMesmoNSU(ctx, evento.getNsu())
                : new ArrayList<TipoEvento>();
        if (!aplicarNSU) {
            tipos.add(evento.getTipoEvento());
        }
        Integer nsu = null;
        for (Date dia : diasEntreDatas) {
            for (TipoEvento tipo : tipos) {
                if ((!apenasOutrosTipos || tipo.getCodigo().intValue() != evento.getTipoEvento().getCodigo().intValue())
                        && Calendario.maiorOuIgual(Calendario.getDataComHoraZerada(dia), Calendario.getDataComHoraZerada(evento.getInicio()))
                        && (diasSelecionados.isEmpty() || diasSelecionados.contains(Uteis.getDiaDaSemanaNumero(dia)))) {
                    Agendamento clone = evento.getClone(
                            Uteis.getDateTime(dia, Integer.valueOf(evento.getHoraInicio()), Integer.valueOf(evento.getMinutoInicio()), 0),
                            Uteis.getDateTime(dia, Integer.valueOf(evento.getHoraFim()), Integer.valueOf(evento.getMinutoFim()), 0), false);
                    clone.setTipoEvento(tipo);
                    Agendamento inserir = inserir(ctx, clone, usuario);
                    if (nsu == null) {
                        nsu = inserir.getCodigo();
                        evento.setNsu(nsu);
                        incluirLog(ctx,
                                inserir.getCodigo().toString(),
                                "",
                                "",
                                evento.getDescricaoParaLog(null) ,
                                "INCLUSÃO",
                                "INCLUSÃO DE DISPONIBILIDADE",
                                EntidadeLogEnum.DISPONIBILIDADE,
                                "Disponibilidade",
                                usuario.getUserName(),
                                logDao);
                    }
                    if(agendamentos != null){
                        adicionarAoMapaAgendamentos(agendamentos, inserir);
                    }
                }
            }
        }
    }

    public void repetirV2(String ctx, Agendamento evento,
                          Date dataLimite, Map<Date, List<Agendamento>> agendamentos,
                          boolean usarDiasSemana, String diaSemana,
                          boolean aplicarNSU, boolean apenasOutrosTipos, Usuario usuario) throws ValidacaoException, ServiceException {

        List<Date> diasEntreDatas;

        try {
            diasEntreDatas = Uteis.getDiasEntreDatas(evento.getInicio(), dataLimite);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
        List<HorarioDisponibilidade> horarioDisp = aplicarNSU ? horarioDisponibilidadeService.consultarHorarioDisponibilidadesNoMesmoNSU(ctx, evento.getNsu())
                : new ArrayList<>();
        if (!aplicarNSU) {
            horarioDisp.add(evento.getHorarioDisponibilidade());
        }
        Integer nsu = null;
        for (Date dia : diasEntreDatas) {
            for (HorarioDisponibilidade hD : horarioDisp) {
                if ((!apenasOutrosTipos || hD.getCodigo().intValue() != evento.getHorarioDisponibilidade().getCodigo().intValue())
                        && Calendario.maiorOuIgual(Calendario.getDataComHoraZerada(dia), Calendario.getDataComHoraZerada(evento.getInicio()))
                        && (Objects.equals(DiasSemana.getDiaSemana(diaSemana).getNumeral(), Uteis.getDiaDaSemanaNumero(dia)))) {
                    Agendamento clone = evento.getClone(
                            Uteis.getDateTime(dia, Integer.valueOf(evento.getHoraInicio()), Integer.valueOf(evento.getMinutoInicio()), 0),
                            Uteis.getDateTime(dia, Integer.valueOf(evento.getHoraFim()), Integer.valueOf(evento.getMinutoFim()), 0), false);
                    clone.setTipoEvento(null);
                    clone.setHorarioDisponibilidade(hD);
                    Agendamento inserir = inserirV2(ctx, clone, usuario, null);
                    if (nsu == null) {
                        nsu = inserir.getCodigo();
                        evento.setNsu(nsu);
                        incluirLog(ctx,
                                inserir.getCodigo().toString(),
                                "",
                                "",
                                evento.getDescricaoParaLog(null) ,
                                "INCLUSÃO",
                                "INCLUSÃO DE DISPONIBILIDADE",
                                EntidadeLogEnum.DISPONIBILIDADE,
                                "Disponibilidade",
                                usuario.getUserName(),
                                logDao);
                    }
                    if(agendamentos != null){
                        adicionarAoMapaAgendamentos(agendamentos, inserir);
                    }
                }
            }
        }
    }

    private void adicionarAoMapaAgendamentos(Map<Date, List<Agendamento>> agendamentos, Agendamento agendamento) {
        List<Agendamento> lista = agendamentos.get(Calendario.getDataComHoraZerada(agendamento.getInicio()));
        if (lista == null) {
            lista = new ArrayList<Agendamento>();
            lista.add(agendamento);
            agendamentos.put(Calendario.getDataComHoraZerada(agendamento.getInicio()), lista);
        } else {
            lista.add(agendamento);
        }
    }

    @Override
    public Agendamento sugerirAgendamento(String ctx, ClienteSintetico cliente) throws ServiceException {
        Agendamento agendamento = new Agendamento();
        agendamento.setInicio(Calendario.hoje());
        agendamento.setFim(Uteis.somarCampoData(agendamento.getInicio(), Calendar.HOUR_OF_DAY, 1));
        agendamento.setCliente(cliente);
        agendamento.setProfessor(cliente.getProfessorSintetico());
        //obter as próximas disponibilidades do professor do aluno
        List<Agendamento> proximasDisponibilidadeProfessor = proximasDisponibilidadeProfessor(ctx, cliente.getProfessorSintetico().getCodigo(), Calendario.hoje());
        //se não tem, avisar para que o usuario mesmo escolha o agendamento, sem sugerir
        if (proximasDisponibilidadeProfessor == null || proximasDisponibilidadeProfessor.isEmpty()) {
            throw new ServiceException("Este professor não tem disponibilidades!");
        }
        for (Agendamento disponibilidade : proximasDisponibilidadeProfessor) {
            //a disponibilidade é hoje?
            Boolean hoje = Calendario.igual(Calendario.getDataComHoraZerada(Calendario.hoje()),
                    Calendario.getDataComHoraZerada(disponibilidade.getInicio()));
            if (hoje) {
                //se for hoje, verificar se tem algum evento concomitante, pois se não tiver, pode sugerir do jeito que está
                List<Agendamento> concomitantes = verificarEventosConcomitantes(ctx, agendamento.getInicio(), agendamento.getFim(), disponibilidade.getProfessor().getCodigo(), null);
                if (concomitantes == null || concomitantes.isEmpty()) {
                    return agendamento;
                }
            }
            //se está aqui é porque não é hoje ou existem eventos concomitantes
            //obter eventos da disponibilidade
            List<Agendamento> eventos = verificarEventosConcomitantes(ctx, hoje ? Calendario.hoje() : disponibilidade.getInicio(),
                    disponibilidade.getFim(),
                    disponibilidade.getProfessor().getCodigo(), null);
            Ordenacao.ordenarLista(eventos, "fim");
            Date novoInicio = hoje ? Calendario.hoje() : disponibilidade.getInicio();
            agendamento.setInicio(novoInicio);
            agendamento.setFim(Uteis.somarCampoData(novoInicio, Calendar.HOUR_OF_DAY, 1));
            if (eventos == null || eventos.isEmpty()) {
                return agendamento;
            }
            for (Agendamento evento : eventos) {
                long minutosDuracao = Uteis.minutosEntreDatas(novoInicio, evento.getInicio());
                if (minutosDuracao >= 30) {
                    agendamento.setInicio(novoInicio);
                    agendamento.setFim(Uteis.somarCampoData(evento.getInicio(), Calendar.MINUTE, -1));
                    return agendamento;
                } else {
                    novoInicio = Uteis.somarCampoData(evento.getFim(), Calendar.MINUTE, 1);
                    agendamento.setInicio(novoInicio);
                    agendamento.setFim(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, 30));
                }
            }
        }
        agendamento.setarTransients();
        agendamento.prepararCods(cliente);
        return agendamento;
    }

    public List<Agendamento> proximasDisponibilidadeProfessor(String ctx, Integer professor, Date data) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Agendamento obj "
                    + "WHERE obj.professor.codigo = :professor AND fim > :data AND disponibilidade is true ORDER BY obj.inicio";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("professor", professor);
            params.put("data", data);
            return getAgendamentoDao().findByParam(ctx, hql, params, 30, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Agendamento> disponibilidadeProfessor(String ctx, Integer professor, Date data) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Agendamento obj "
                    + "WHERE obj.professor.codigo = :professor AND cast(inicio as date) = :data AND disponibilidade is true ORDER BY obj.inicio";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("professor", professor);
            params.put("data", data);
            return getAgendamentoDao().findByParam(ctx, hql, params, 30, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void gravarNSU(String ctx, Integer codigo) throws ServiceException {
        try {
            getAgendamentoDao().updateAlgunsCampos(ctx, new String[]{"nsu"}, new Object[]{codigo}, new String[]{"codigo"}, new Object[]{codigo});
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Agendamento> obterPorNSU(String ctx, Integer nsu) throws ServiceException {
        try {
            return getAgendamentoDao().findListByAttributes(ctx, new String[]{"nsu"}, new Object[]{nsu}, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public List<Agendamento> obterDisponibilidadePorNSUApartirDia(String ctx, Integer nsu, Date dia) throws ServiceException {
        try {
            return getAgendamentoDao().obterDisponibilidadePorNSUApartirDia(ctx, nsu, dia, null);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_CONSULTAR_DISPONIBILIDADES, e);
        }
    }

    public void alterarProfessorAgendamentosFuturos(String ctx, Integer professorNovo,Integer professorAntigo, Integer tipo) throws ServiceException {
        try {
            StringBuilder cmd = new StringBuilder();
            cmd.append(" UPDATE Agendamento SET professor_codigo = ");
            cmd.append(professorNovo);
            cmd.append(" WHERE NOT disponibilidade ");
            cmd.append(" AND inicio >= '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")).append("' ");
            cmd.append(" AND professor_codigo = ");
            cmd.append(professorAntigo);
            if(tipo != null){
                cmd.append(" AND tipoevento_codigo = ");
                cmd.append(tipo);
            }
            getAgendamentoDao().executeNativeSQL(ctx, cmd.toString());
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Map<Date, List<Agendamento>> montarAgenda(String ctx, ScheduleModel eventModel, Usuario usuario, FiltrosAgendaTO filtros,
                                                     Boolean consultaEmBanco, Map<Date, List<Agendamento>> agendamentos, Boolean disponibilidades, ConfiguracaoSistema cfgAbrirSistema) throws ServiceException {
        try {
            eventModel.clear();
            String statusSelecionados = Uteis.getListaEscolhidos(filtros.getStatusFiltros(), "Escolhido", "Codigo", true, false);
            String codigosProfessores = (perfilOk(usuario))
                    ? (filtros.getCodigoProfessorSelecionado() != null && filtros.getCodigoProfessorSelecionado() > 0)
                    ? filtros.getCodigoProfessorSelecionado().toString()
                    : Uteis.splitFromArray(filtros.getProfessoresSelecionados().toArray(), true)
                    : usuario.getProfessor().getCodigoColaborador().toString();

            List<Agendamento> consultarPorData = consultarPorData(ctx, Calendario.getDataComHoraZerada(filtros.getInicio()),
                    Calendario.getDataComHora(filtros.getFim(), "23:59:59"),
                    usuario.getEmpresaZW(),
                    codigosProfessores, getTiposEvSelecionados(filtros,
                            cfgAbrirSistema != null && cfgAbrirSistema.getValorAsBoolean()),
                    statusSelecionados, null, (disponibilidades ? disponibilidades : null), false, null, null, null, null);
            agendamentos = montarMapaAgendamentos(consultarPorData);
            setarMargemLargura(agendamentos);
            List<AgendaTO> agendas = montarAgendaAgrupandoDisponibilidades(consultarPorData, filtros, usuario);
            agendas = Ordenacao.ordenarLista(agendas, "inicio");
            int i = 0;
            for(AgendaTO ag : agendas){
                ag.setStyleClass(ag.getStyleClass() + " zindex"+i++);
            }
            for (AgendaTO agenda : agendas) {
                eventModel.addEvent(agenda);
            }

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return agendamentos;
    }

    private void setarMargemDisponibilidades(List<AgendaTO> agendas){
        Map<Date, Map<Double, List<String>>> disponibilidadesDiaHorario = new HashMap<Date, Map<Double, List<String>>>();
        agendas = Ordenacao.ordenarLista(agendas, "tamanho");
        Collections.reverse(agendas);

        for(AgendaTO ag : agendas){
            int cont = 0;
            if(ag.getAgendamento().getDisponibilidade()){
                ag.getAgendamento().setLeft(0.0);
                double iInicio = Uteis.obterIndiceHora(ag.getInicio(), false);
                double iFim = Uteis.obterIndiceHora(ag.getFim(), false);
                int cont2 = 0;
                for(AgendaTO ag2 : agendas){
                    if(cont2 != cont && ag2.getAgendamento().getDisponibilidade()
                            && Calendario.igual(ag2.getInicio(), ag.getInicio())){
                        double i2Inicio = Uteis.obterIndiceHora(ag2.getInicio(), false);
                        double i2Fim = Uteis.obterIndiceHora(ag2.getFim(), false);
                        if (((iInicio >= i2Inicio && iInicio <= i2Fim)
                                || (i2Inicio >= iInicio && i2Inicio <= iFim)
                                || (i2Fim >= iInicio && i2Fim <= iFim)
                                || (iFim >= i2Inicio && iFim <= i2Fim))
                                && cont2 > cont ){
                            ag.getAgendamento().setLeft(ag.getAgendamento().getLeft()+1.0);
                        }
                    }
                    cont2++;
                }
            }
            cont++;
        }

        for(AgendaTO ag : agendas){
            if(ag.getAgendamento().getDisponibilidade()){
                Double margem = 6.0 + (0.3 * ag.getAgendamento().getLeft());
                ag.setStyleClass(ag.getStyleClass() + " _leftdisp"+margem+"% ");
            }
        }
    }

    private void addMapaHorario(Double indiceHora, AgendaTO ag, Map<Double, List<String>> disponibilidadesHorario){
        List<String> disponibilidades = disponibilidadesHorario.get(indiceHora);
        if(disponibilidades == null){
            disponibilidades = new ArrayList<String>();
            disponibilidadesHorario.put(indiceHora, disponibilidades);
        }
        disponibilidades.add(ag.getAgendamento().getProfessor().getCodigo()+"_"+
                ag.getAgendamento().getTipoEvento().getCodigo()+"_"+ag.getInicio());
    }

    private void setarMargemLargura(Map<Date, List<Agendamento>> agendamentos) throws ServiceException {
        Map<Date, Map<String, List<String>>> agendamentosDiaHorario = new HashMap<Date, Map<String, List<String>>>();
        for (Date dia : agendamentos.keySet()) {
            Map<String, List<String>> indiceDisponibilidades = agendamentosDiaHorario.get(dia);
            if (indiceDisponibilidades == null) {
                indiceDisponibilidades = new HashMap<String, List<String>>();
                agendamentosDiaHorario.put(dia, indiceDisponibilidades);
            }
            //povoar horarios
            for (double i = 0.0; i < 24.0; i += 0.25) {
                for (Agendamento ag : agendamentos.get(dia)) {
                    try {
                        double iInicio = Uteis.obterIndiceHora(ag.getInicio());
                        double iFim = Uteis.obterIndiceHora(ag.getFim());
                        if (ag.getDisponibilidade()) {
                            List<String> professorTipo = indiceDisponibilidades.get("disp_" + i);
                            if (professorTipo == null) {
                                professorTipo = new ArrayList<String>();
                                indiceDisponibilidades.put("disp_" + i, professorTipo);
                            }
                            if (i >= iInicio && i <= (iFim - 0.1)
                                    && !professorTipo.contains(ag.getProfessor().getCodigo() + "_" + ag.getTipoEvento().getCodigo())) {
                                professorTipo.add(ag.getProfessorCod() + "_" + ag.getTipoEventoCod());
                            }
                        } else {
                            List<String> agendamentosHora = indiceDisponibilidades.get("ag_" + i);
                            if (agendamentosHora == null) {
                                agendamentosHora = new ArrayList<String>();
                                indiceDisponibilidades.put("ag_" + i, agendamentosHora);
                            }
                            if (i == iInicio) {
                                ag.setLeft(Double.valueOf(agendamentosHora.size()));
                                agendamentosHora.add(ag.getCodigo().toString());
                            }
                        }
                    } catch (Exception e) {
                        Logger.getLogger(AgendamentoServiceImpl.class.getName()).log(Level.SEVERE,
                                dia + " - " + i + " - "+ag.getText(true, true), e);
                        throw new ServiceException("Houve um erro na montagem da agenda.");
                    }
                }

            }
            for (Agendamento ag : agendamentos.get(dia)) {
                if(!ag.getDisponibilidade()){
                    List<String> disponibilidades = indiceDisponibilidades.get("disp_" + Uteis.obterIndiceHora(ag.getInicio()));
                    ag.setWidth((disponibilidades == null || disponibilidades.isEmpty() ? 100.0 : (100.0 / disponibilidades.size()))
                            - (disponibilidades == null || disponibilidades.isEmpty() ? 6.5 : (6.5 / disponibilidades.size())));
                    ag.setLeft(ag.getWidth() * ag.getLeft() + 6.5);
                }

            }
        }
    }

    public String getTiposEvSelecionados(FiltrosAgendaTO filtrosAgenda) throws Exception {
        return getTiposEvSelecionados(filtrosAgenda, false);
    }

    public String getTiposEvSelecionados(FiltrosAgendaTO filtrosAgenda, boolean naoConsultarVazios) throws Exception {
        String tiposEv = Uteis.getListaEscolhidos(filtrosAgenda.getTiposEvento(), "Escolhido", "Codigo", true, false);
        if((tiposEv == null || tiposEv.isEmpty()) && naoConsultarVazios){
            return "0";
        }else if (tiposEv == null || tiposEv.isEmpty()) {
            tiposEv = "";
            for (TipoEvento tipo : filtrosAgenda.getTiposEvento()) {
                tiposEv += "," + tipo.getCodigo();
            }
            return tiposEv.replaceFirst(",", "");
        } else {
            return tiposEv;
        }
    }

    public Agendamento montarNovoAgendamento(String ctx, Date dataSelecionada, String view,
                                             FiltrosAgendaTO filtrosAgenda, Usuario usuario,
                                             ClienteSintetico clientesugerido, Date dataFim) throws ValidacaoException, ServiceException {
        try {
            Agendamento novo = new Agendamento();
            novo.setDisponibilidades(new ArrayList<Agendamento>());
            if (dataSelecionada == null) {
                novo.setInicio(Calendario.hoje());
            } else {
                DefaultScheduleEvent event = new DefaultScheduleEvent("", dataSelecionada, dataSelecionada);
                novo.setInicio(view.equals("month") && filtrosAgenda.getDisponibilidades()
                        ? Uteis.getDateTime(event.getStartDate(), Calendario.getInstance().get(Calendar.HOUR_OF_DAY), 0, 0)
                        : event.getStartDate());
            }
            novo.setAlunoCod(clientesugerido == null ? 0 : clientesugerido.getCodigo());
            novo.setFim(dataFim == null ? Uteis.somarCampoData(novo.getInicio(), Calendar.HOUR_OF_DAY, 1) : dataFim);
            novo.setDisponibilidade(filtrosAgenda.getDisponibilidades());

            if (filtrosAgenda.getDisponibilidades()) {
                executarValidacoesUsuarioAgenda(RecursosAgendaEnum.NOVA_DISPONIBILIDADE, usuario, null, true, true);
                filtrosAgenda.setTiposGenerico(new ArrayList<GenericoTO>());
                filtrosAgenda.setTiposSelectItem(new ArrayList<SelectItem>());
                for (TipoEvento tipoEv : filtrosAgenda.getTiposEvento()) {
                    if (tipoEv.getEscolhido()) {
                        filtrosAgenda.getTiposGenerico().add(new GenericoTO(tipoEv.getCodigo(), tipoEv.getNome(), "" + tipoEv.getCor().getDescricao() + " " + tipoEv.getCor().getClasseCor()));
                        filtrosAgenda.getTiposSelectItem().add(new SelectItem(tipoEv.getCodigo(), tipoEv.getNome(), "" + tipoEv.getCor().getDescricao() + " " + tipoEv.getCor().getClasseCor()));
                    }
                }
                if (filtrosAgenda.getTiposSelectItem().isEmpty()) {
                    for (TipoEvento tipoEv : filtrosAgenda.getTiposEvento()) {
                        filtrosAgenda.getTiposGenerico().add(new GenericoTO(tipoEv.getCodigo(), tipoEv.getNome(), "" + tipoEv.getCor().getDescricao() + " " + tipoEv.getCor().getClasseCor()));
                        filtrosAgenda.getTiposSelectItem().add(new SelectItem(tipoEv.getCodigo(), tipoEv.getNome(), "" + tipoEv.getCor().getDescricao() + " " + tipoEv.getCor().getClasseCor()));
                    }
                }
            } else {
                montarProfessoresTiposDisponiveis(ctx, filtrosAgenda, usuario, novo, dataFim != null);
            }
            if (perfilOk(usuario)) {
                novo.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
            } else {
                novo.setProfessor(usuario.getProfessor());
                novo.setProfessorCod(usuario.getProfessor().getCodigo());
                novo.setStatus(StatusAgendamentoEnum.CONFIRMADO);
            }
            novo.setarTransients();
            inicializarProfessorTipo(ctx, filtrosAgenda, novo);
            return novo;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (ServiceException ex) {
            Uteis.logar(ex, AgendamentoService.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    private void inicializarProfessorTipo(String ctx, FiltrosAgendaTO filtros, Agendamento novo) throws ServiceException {
        if (filtros.getTiposSelectItem().size() == 1) {
            novo.setTipoEventoCod((Integer) filtros.getTiposSelectItem().get(0).getValue());
        }
        Ordenacao.ordenarLista(filtros.getTiposSelectItem(), "label");
        filtros.getProfessoresModal().add(0, new SelectItem(0, ""));
    }

    private void montarProfessoresTiposDisponiveis(String ctx,
                                                   FiltrosAgendaTO filtros, Usuario usuario, Agendamento novo, boolean inside) throws ServiceException {

        filtros.setTipoProfessores(new HashMap<Integer, List<ProfessorSintetico>>());
        List<Agendamento> agendamentosDia = consultarDisponibilidade(ctx, novo.getInicio(),
                novo.getFim(), null, filtros.getTiposEvento(), true, inside,
                usuario.getEmpresaZW());
        if (agendamentosDia == null || agendamentosDia.isEmpty()) {
            throw new ValidacaoException("naoExisteDisponibilidade");
        }
        Date fim = null;
        Set<ProfessorSintetico> professores = new HashSet<ProfessorSintetico>();
        Set<TipoEvento> tipos = new HashSet<TipoEvento>();
        for (Agendamento agenda : agendamentosDia) {
            if (agenda.getDisponibilidade()) {
                if (agenda.getProfessor() == null) {
                    continue;
                }
                if (fim == null || agenda.getFim().after(fim)) {
                    fim = new Date(agenda.getFim().getTime());
                }
                professores.add(agenda.getProfessor());
                tipos.add(agenda.getTipoEvento());
                filtros.addProfessorTipo(agenda.getTipoEvento(), agenda.getProfessor());
                novo.getDisponibilidades().add(agenda);
            }
        }

        if (fim != null && fim.before(novo.getFim())) {
            novo.setFim(fim);
        }

        filtros.setTiposGenerico(new ArrayList<GenericoTO>());
        filtros.setTiposSelectItem(new ArrayList<SelectItem>());
        for (TipoEvento tip : tipos) {
            if (executarValidacoesUsuarioAgenda(RecursosAgendaEnum.NOVO_AGENDAMENTO, usuario, tip, false, false)) {
                filtros.getTiposGenerico().add(new GenericoTO(tip.getCodigo(), tip.getNome(), tip.getCor().getDescricao() + " " + tip.getCor().getClasseCor()));
                filtros.getTiposSelectItem().add(new SelectItem(tip.getCodigo(), tip.getNome(), tip.getCor().getDescricao() + " " + tip.getCor().getClasseCor()));
            }
        }
        if (filtros.getTiposSelectItem().isEmpty()) {
            throw new ValidacaoException("naoTemPermissaoIncluirTipos");
        }

        filtros.setProfessoresModal(new ArrayList<SelectItem>());
//        for (ProfessorSintetico pfs : professores) {
//            filtros.getProfessoresModal().add(new SelectItem(pfs.getCodigo(), pfs.getNomeMinusculo()));
//        }
    }

    public void escolherTipoEvento(FiltrosAgendaTO filtros, Agendamento agendamento, boolean atualizarhoras) {
        for (TipoEvento tipo : filtros.getTiposEvento()) {
            if (tipo.getCodigo().intValue() == agendamento.getTipoEventoCod()) {
                agendamento.setTipoEvento(tipo);
                if ((tipo.getDuracaoIntervalo() | tipo.getDuracaoPreDefinida()) && atualizarhoras) {
                    agendamento.setFim(Uteis.somarCampoData(agendamento.getInicio(), Calendar.MINUTE, tipo.getDuracaoMinutosMin()));
                    agendamento.setarHoras();
                }
                return;
            }
        }
    }

    @Override
    public void montarListaAlunos(String ctx, FiltrosAgendaTO filtros, Agendamento agendamento, Integer empresaZW) throws ServiceException {
        filtros.setAlunosCarteira(new ArrayList<SelectItem>());
        escolherTipoEvento(filtros, agendamento, false);
        if (agendamento.getTipoEvento() == null || !agendamento.getTipoEvento().getApenasAlunosCarteira()) {
            return;
        }
        List<ClienteSintetico> lista = clienteService.consultarPorProfessor(ctx, agendamento.getProfessorCod(),empresaZW,true);
        for (ClienteSintetico cliente : lista) {
            filtros.getAlunosCarteira().add(new SelectItem(cliente.getCodigo(), cliente.getNomeMinusculo()));
        }
        agendamento.setCliente(agendamento.getCliente() == null ? new ClienteSintetico() : agendamento.getCliente());
    }

    private boolean perfilOk(Usuario usuario) {
        return usuario.getTipo().equals(TipoUsuarioEnum.ROOT)
                || usuario.getTipo().equals(TipoUsuarioEnum.CONSULTOR)
                || usuario.getTipo().equals(TipoUsuarioEnum.COORDENADOR)
                || usuario.isFuncionalidadeHabilitado(RecursoEnum.VER_AGENDA_OUTROS_PROFESSORES.name());
    }

    public String redimensionarEvento(String ctx, AgendaTO event, Map<Date, List<Agendamento>> agendamentos, List<GenericoTO> diasSemana,
                                      Usuario usuario, FiltrosAgendaTO filtros) throws ServiceException {
        String msg = "";
        executarValidacoesUsuarioAgenda(RecursosAgendaEnum.MOVER_REDIMENSIONAR_EVENTO, usuario,
                event.getAgendamento().getTipoEvento(), event.getAgendamento().getDisponibilidade(), true);
        try {
            if (Calendario.maior(Calendario.getDataComHoraZerada(event.getAgendamento().getFim()),
                    Calendario.getDataComHoraZerada(event.getAgendamento().getInicio()))) {

                repetir(ctx, event.getAgendamento(), event.getAgendamento().getFim(),
                        agendamentos, false, diasSemana, false, false, usuario);
                msg = "eventoalteradonovoseventos";

            } else {
                final String title = event.getAgendamento().getText(perfilOk(usuario) && filtros.mostrarNomeProfessor(), filtros.mostrarTipo());
                event.setAgendamento(alterar(ctx, event.getAgendamento(), usuario));
                event.setTitle(title);
                msg = "eventoalterado";
            }
        } catch (ValidacaoException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return msg;
    }

    public List<AgendaTO> montarAgendaAgrupandoDisponibilidades(List<Agendamento> agendamentos, FiltrosAgendaTO filtros, Usuario usuario) {
        List<AgendaTO> listaAgenda = new ArrayList<AgendaTO>();
        for (Agendamento agendamento : agendamentos) {
            realizarAgrupamento(listaAgenda, agendamento, filtros, usuario);
        }
        return listaAgenda;
    }

    private void realizarAgrupamento(List<AgendaTO> listaAgenda, Agendamento agendamento, FiltrosAgendaTO filtros, Usuario usuario) {
        AgendaTO agenda = agendamento.toAgenda(
                perfilOk(usuario) && filtros.mostrarNomeProfessor(),
                filtros.mostrarTipo(),
                Uteis.getMesData(filtros.getMesAtual()));

        agenda.setAgruparPorTipo(filtros.getAgruparPorEventos());
        agenda.setAgrupar(filtros.getAgrupar());
        agenda.setVisaoMensal(filtros.isVisaoMensal());

        int index = listaAgenda.indexOf(agenda);
        if (index >= 0) {
            AgendaTO agendaExistente = listaAgenda.get(index);
            if (agendaExistente.getInicio().after(agenda.getInicio())) {
                agendaExistente.setInicio(agenda.getInicio());
                agendaExistente.setStartDate(agenda.getStartDate());
            }
            if (agendaExistente.getFim().before(agenda.getFim())) {
                agendaExistente.setFim(agenda.getFim());
                agendaExistente.setEndDate(agenda.getEndDate());
            }
        } else {
            listaAgenda.add(agenda);
        }
    }



    public Map<Date, List<Agendamento>> montarMapaAgendamentos(List<Agendamento> listaAgendamento) {
        Map<Date, List<Agendamento>> mapaAgendamentos = new HashMap<Date, List<Agendamento>>();
        for (Agendamento agendamento : listaAgendamento) {
            adicionarAoMapaAgendamentos(mapaAgendamentos, agendamento);
        }
        return mapaAgendamentos;
    }

    private boolean lancarExceptionOuRetornar(Boolean lancarExc, Boolean condicaoFalse, String msg) throws ValidacaoException {
        if (!condicaoFalse && lancarExc) {
            throw new ValidacaoException(msg);
        }
        return condicaoFalse;

    }

    public boolean executarValidacoesUsuarioAgenda(RecursosAgendaEnum recursoAgenda, Usuario usuario, TipoEvento tipoEvento,
                                                   boolean disponibilidade, boolean lancarException) throws ValidacaoException {
        switch (recursoAgenda) {
            case MOVER_REDIMENSIONAR_EVENTO:
                if (disponibilidade) {
                    return lancarExceptionOuRetornar(lancarException,
                            usuario.isItemHabilitado(RecursoEnum.AGENDA_DISPONIBILIDADE.name(), TipoPermissaoEnum.EDITAR.name()),
                            "naoTemPermissaoEditarDisponibilidade");
                } else {
                    return lancarExceptionOuRetornar(lancarException,
                            usuario.isItemHabilitado(tipoEvento.getComportamento().name(), TipoPermissaoEnum.EDITAR.name()),
                            "naoTemPermissaoEditarEvento");
                }
            case EDITAR_AGENDAMENTO:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(tipoEvento.getComportamento().name(), TipoPermissaoEnum.EDITAR.name()),
                        "naoTemPermissaoEditarEvento");
            case EDITAR_DISPONIBILIDADE:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(RecursoEnum.AGENDA_DISPONIBILIDADE.name(), TipoPermissaoEnum.EDITAR.name()),
                        "naoTemPermissaoEditarDisponibilidade");

            case NOVA_DISPONIBILIDADE:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(RecursoEnum.AGENDA_DISPONIBILIDADE.name(), TipoPermissaoEnum.INCLUIR.name()),
                        "naoTemPermissaoIncluirDisponibilidade");

            case NOVO_AGENDAMENTO:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(tipoEvento.getComportamento().name(), TipoPermissaoEnum.INCLUIR.name()),
                        "naoTemPermissaoIncluirTipos");
            case REMOVER_DISPONIBILIDADE:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(RecursoEnum.AGENDA_DISPONIBILIDADE.name(), TipoPermissaoEnum.EXCLUIR.name()),
                        "naoTemPermissaoIncluirDisponibilidade");

            case REMOVER_AGENDAMENTO:
                return lancarExceptionOuRetornar(lancarException,
                        usuario.isItemHabilitado(tipoEvento.getComportamento().name(), TipoPermissaoEnum.EXCLUIR.name()),
                        "naoTemPermissaoIncluirTipos");
        }
        return true;
    }

    public boolean possuiAgendamentoAssociado(String ctx, Integer nsu) throws ServiceException {
        try {
            Long count = (Long) getAgendamentoDao().count(ctx, "nsu", new String[]{"nsu"}, new Object[]{nsu});
            return count > 1;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public boolean possuiAgendamentoAssociadoOutroTipo(String ctx, Integer nsu, Integer tipo) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Agendamento obj "
                    + "WHERE nsu = :nsu AND tipoEvento.codigo <> :tipo";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nsu", nsu);
            params.put("tipo", tipo);
            List<Agendamento> agendamentos = getAgendamentoDao().findByParam(ctx, hql, params, 30, 0);
            return agendamentos != null && !agendamentos.isEmpty();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public String executarEscolhaUsuario(String ctx, Agendamento agendamento, boolean aplicarAoNSU,
                                         boolean apenasMesmoTipo,boolean apenasMesmoTipoPosteriores, RecursosAgendaEnum recurso, Map<Date, List<Agendamento>> agendamentos,
                                         List<GenericoTO> diasSemana, Usuario usuario) throws ServiceException {
        try {
            if(recurso == null){
                return "";
            }
            switch (recurso) {
                case REMOVER_DISPONIBILIDADE:
                    excluir(ctx, agendamento, aplicarAoNSU, apenasMesmoTipo, apenasMesmoTipoPosteriores);
                    return "disponibilidadeRemovida";
                case MOVER_REDIMENSIONAR_EVENTO:
                    if (aplicarAoNSU) {
                        repetir(ctx, agendamento, agendamento.getFim(),
                                agendamentos, false, diasSemana, true, true, usuario);
                    }
                    return "eventoalteradonovoseventos";
                case EDITAR_DISPONIBILIDADE:
                    if (aplicarAoNSU) {
                        aplicarAlteracoesAoNSU(ctx, agendamento, apenasMesmoTipo,apenasMesmoTipoPosteriores, usuario);
                    } else {
                        agendamento.atualizarInicioFim();
                        agendamento.setProfessor((agendamento.getProfessorCod() != null && agendamento.getProfessorCod() > 0)
                                ? getProfessorSinteticoService().obterPorId(ctx, agendamento.getProfessorCod()) : null);
                        alterar(ctx, agendamento, usuario);
                    }
                    return "comum.dadosGravados";
            }
            return "";
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void validarRestricaoDisponibilidadeConfigV2(String ctx, Agendamento agendamento) throws ServiceException {
        try {
            Map<String, Object> p = new HashMap();
            if (agendamento.getDisponibilidade() ||
                    UteisValidacao.emptyNumber(agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloDiasEntreAgendamentos())) {
                return;
            } else {
                p.put("codigo", agendamento.getCodigo() == null ? 0 : agendamento.getCodigo());
                p.put("cliente", agendamento.getCliente().getCodigo());
                p.put("status1", StatusAgendamentoEnum.CANCELADO);
                p.put("status2", StatusAgendamentoEnum.FALTOU);
                p.put("disponibilidade", agendamento.getHorarioDisponibilidade().getDisponibilidade().getCodigo());
                p.put("dataInicio", Uteis.somarDias(agendamento.getInicio(), -agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloDiasEntreAgendamentos()));
                p.put("dataFim", Uteis.somarDias(agendamento.getInicio(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloDiasEntreAgendamentos()));

                Long numeroAgendamentos = (Long) getAgendamentoDao().countWithParam(ctx, "codigo",
                        new StringBuilder("WHERE codigo <> :codigo and cliente.codigo = :cliente and horarioDisponibilidade.disponibilidade.codigo = :disponibilidade ").
                                append("and status <> :status1 and status <> :status2 and inicio between :dataInicio and :dataFim"),
                        p);
                if (numeroAgendamentos > 0) {
                    throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS.name());
                }
            }
            if (agendamento.getDisponibilidade() ||
                    UteisValidacao.emptyNumber(agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloMinimoDiasCasoFalta())) {
                return;
            } else {
                p = new HashMap();
                p.put("codigo", agendamento.getCodigo() == null ? 0 : agendamento.getCodigo());
                p.put("cliente", agendamento.getCliente().getCodigo());
                p.put("status", StatusAgendamentoEnum.FALTOU);
                p.put("disponibilidade", agendamento.getHorarioDisponibilidade().getDisponibilidade().getCodigo());
                p.put("dataInicio", Uteis.somarDias(agendamento.getInicio(), -agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloMinimoDiasCasoFalta()));
                p.put("dataFim", Uteis.somarDias(agendamento.getInicio(), agendamento.getHorarioDisponibilidade().getDisponibilidade().getIntervaloMinimoDiasCasoFalta()));

                Long numeroAgendamentosFaltosos = (Long) getAgendamentoDao().countWithParam(ctx, "codigo",
                        new StringBuilder("WHERE codigo <> :codigo and cliente.codigo = :cliente and horarioDisponibilidade.disponibilidade.codigo = :disponibilidade ").
                                append("and status = :status and inicio between :dataInicio and :dataFim"),
                        p);

                if (numeroAgendamentosFaltosos > 0) {
                    throw new ValidacaoException(AgendaExcecoes.VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU.name());
                }
            }
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }

    }

    public void validarRestricaoTipoEvento(String ctx, Agendamento agendamento) throws ServiceException {
        try {
            if (agendamento.getDisponibilidade()
                    || agendamento.getTipoEvento().getNrAgendamentos() == null
                    || agendamento.getTipoEvento().getNrAgendamentos().intValue() == 0
                    || agendamento.getTipoEvento().getDias() == null
                    || agendamento.getTipoEvento().getDias().intValue() == 0) {
                return;
            }

            if (agendamento.getTipoEvento().getApenasAlunosCarteira()) {
                boolean alunoNaCarteira = getAgendamentoDao().verificarAlunoNaCarteira(ctx, agendamento.getProfessor().getCodigo(), agendamento.getCliente().getCodigo());
                if (!alunoNaCarteira) {
                    throw new ValidacaoException("validacaoalunoagendamento.carteira");
                }
            }
            Map<String, Object> p = new HashMap();
            p.put("codigo", agendamento.getCodigo() == null ? 0 : agendamento.getCodigo());
            p.put("cliente", agendamento.getCliente().getCodigo());
            p.put("status1", StatusAgendamentoEnum.CANCELADO);
            p.put("status2", StatusAgendamentoEnum.FALTOU);
            p.put("tipoEvento", agendamento.getTipoEvento().getCodigo());
            p.put("dataInicio", Uteis.somarDias(agendamento.getInicio(), -agendamento.getTipoEvento().getDias()));
            p.put("dataFim", Uteis.somarDias(agendamento.getInicio(), agendamento.getTipoEvento().getDias()));

            Long numeroAgendamentos = (Long) getAgendamentoDao().countWithParam(ctx, "codigo",
                    new StringBuilder("WHERE codigo <> :codigo and cliente.codigo = :cliente and tipoEvento.codigo = :tipoEvento ").
                            append("and status <> :status1 and status <> :status2 and inicio between :dataInicio and :dataFim"),
                    p);
            if (numeroAgendamentos >= agendamento.getTipoEvento().getNrAgendamentos()) {
                String mensagemDetalhada = "O aluno já atingiu o limite de agendamentos permitidos para este período (" +
                    agendamento.getTipoEvento().getNrAgendamentos() + " agendamentos em " +
                    (agendamento.getTipoEvento().getDias() * 2) + " dias). Agendamentos realizados: " +
                    numeroAgendamentos + ".";
                throw new ValidacaoException(mensagemDetalhada);
            }
            if (agendamento.getTipoEvento().getIntervaloMinimoFalta() == null
                    || agendamento.getTipoEvento().getIntervaloMinimoFalta().intValue() == 0) {
                return;
            }
            p = new HashMap();
            p.put("codigo", agendamento.getCodigo() == null ? 0 : agendamento.getCodigo());
            p.put("cliente", agendamento.getCliente().getCodigo());
            p.put("status", StatusAgendamentoEnum.FALTOU);
            p.put("tipoEvento", agendamento.getTipoEvento().getCodigo());
            p.put("dataInicio", Uteis.somarDias(agendamento.getInicio(), -agendamento.getTipoEvento().getIntervaloMinimoFalta()));
            p.put("dataFim", Uteis.somarDias(agendamento.getInicio(), agendamento.getTipoEvento().getIntervaloMinimoFalta()));

            Long numeroAgendamentosFaltosos = (Long) getAgendamentoDao().countWithParam(ctx, "codigo",
                    new StringBuilder("WHERE codigo <> :codigo and cliente.codigo = :cliente and tipoEvento.codigo = :tipoEvento ").
                            append("and status = :status and inicio between :dataInicio and :dataFim"),
                    p);

            if ((numeroAgendamentos + numeroAgendamentosFaltosos) >= agendamento.getTipoEvento().getNrAgendamentos()) {
                String mensagemDetalhada = "O aluno já atingiu o limite de agendamentos permitidos para este período (" +
                    agendamento.getTipoEvento().getNrAgendamentos() + " agendamentos em " +
                    (agendamento.getTipoEvento().getIntervaloMinimoFalta() * 2) + " dias). Total de agendamentos + faltas: " +
                    (numeroAgendamentos + numeroAgendamentosFaltosos) + ".";
                throw new ValidacaoException(mensagemDetalhada);
            }

            // Validar intervalo mínimo em dias em caso de falta; Caso o aluno tenha 1 falta dentro desse intervalo ele não pode realizar o agendamento;
            if (numeroAgendamentosFaltosos > 0) {
                String mensagemDetalhada = "O aluno possui " + numeroAgendamentosFaltosos +
                    " falta(s) dentro do período de restrição de " + (agendamento.getTipoEvento().getIntervaloMinimoFalta() * 2) +
                    " dias. Não é possível realizar novo agendamento neste intervalo.";
                throw new ValidacaoException(mensagemDetalhada);
            }
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }

    }

    public boolean clienteTemAgendamentoHoje(final String ctx, final ClienteSintetico cliente) throws ServiceException {
        try {
            return ((Long) getAgendamentoDao().count(ctx, "codigo",
                    new String[]{"cliente.codigo", "inicio <", "fim >"},
                    new Object[]{cliente.getCodigo(), Calendario.getDataComHoraZerada(Uteis.somarDias(Calendario.hoje(), 1)),
                            Calendario.getDataComHora(Uteis.somarDias(Calendario.hoje(), -1), "23:59:59")})) > 0;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Agendamento> agendamentosFuturosCliente(final String ctx, final ClienteSintetico cliente, final TipoAgendamentoEnum tipo) throws ServiceException {
        try {
            return getAgendamentoDao().findListByAttributes(ctx,
                    new String[]{"cliente.codigo", "inicio >", "tipoEvento.comportamento", "status <>"},
                    new Object[]{cliente.getCodigo(), Calendario.hoje(), tipo, StatusAgendamentoEnum.CANCELADO},
                    "inicio", 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Agendamento> obterMaisAgendamentos(final String ctx, final Integer idProfessor, final Integer idCliente,
                                                   final Integer maxResults, final Integer index) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder sql = new StringBuilder("SELECT obj FROM Agendamento obj ");
            if (idProfessor != null) {
                sql.append(" AND obj.professor.codigo = :professor ");
                params.put("professor", idProfessor);
            }
            if (idCliente != null) {
                sql.append(" AND obj.cliente.codigo = :cliente ");
                params.put("cliente", idCliente);
            }
            sql.append(" ORDER BY inicio DESC");

            List<Agendamento> sqltotal = getAgendamentoDao().findByParam(ctx, sql.toString().replaceFirst(" AND ", " WHERE "), params, maxResults, index);
            for (Agendamento ag : sqltotal) {
                ag.getProfessor().getCodigo();
                ag.getTipoEvento().getCodigo();
            }
            return sqltotal;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }


    @Override
    public List<Agendamento> listaAgendamentos(String ctx, Usuario usuario, FiltrosAgendaTO filtros) throws ServiceException {
        try {
            String statusSelecionados = Uteis.getListaEscolhidos(filtros.getStatusFiltros(), "Escolhido", "Codigo", true, false);
            String codigosProfessores = (perfilOk(usuario))
                    ? (filtros.getCodigoProfessorSelecionado() != null && filtros.getCodigoProfessorSelecionado() > 0)
                    ? filtros.getCodigoProfessorSelecionado().toString()
                    : Uteis.splitFromArray(filtros.getProfessoresSelecionados().toArray(), true)
                    : usuario.getProfessor().getCodigoColaborador().toString();
            return consultarPorData(ctx, Calendario.getDataComHoraZerada(filtros.getInicio()),
                    Calendario.getDataComHora(filtros.getFim(), "23:59:59"),
                    usuario.getEmpresaZW(),
                    codigosProfessores, getTiposEvSelecionados(filtros), statusSelecionados,
                    null, false, false, null, null, null, null);

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Agendamento obterAgendamentoNSUMaiorDataFim(String ctx, Integer nsu) throws Exception {
        try {
            String hql = "SELECT obj FROM Agendamento obj "
                    + "WHERE obj.nsu = :nsu "
                    + "ORDER BY obj.fim desc ";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nsu", nsu);
            List<Agendamento> listAgenda = getAgendamentoDao().findByParam(ctx, hql, params, 1, 0);
            if (!UteisValidacao.emptyList(listAgenda)) {
                return listAgenda.get(0);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }

    }

    public boolean existeAgendamentoFuturo(String ctx, final TipoEvento tipoEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" inicio >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
        sql.append(" AND tipoevento_codigo = ").append(tipoEvento.getCodigo()).append("\n");
        sql.append(" AND disponibilidade IS FALSE\n");
        sql.append(" AND status <> ").append(StatusAgendamentoEnum.CANCELADO.getId());
        return agendamentoDao.existsWithParam(ctx, sql);
    }

    public List<String> sugestoesHorario(final String key, final Integer professor, final TipoEvento tipoEvento,
                                         final Date hora, final Integer empresa) throws Exception{
        List<String> sugestoes = new ArrayList<String>();
        Integer duracao = 0;
        switch (tipoEvento.getDuracao()){
            case DURACAO_LIVRE:
                duracao = 60;
                break;
            case INTERVALO_DE_TEMPO:
                duracao = tipoEvento.getDuracaoMinutosMin();
                break;
            case DURACAO_PREDEFINIDA:
                duracao = tipoEvento.getDuracaoMinutosMin();
                break;
        }
        Date inicioDia = Calendario.getDataComHoraZerada(hora);
        Date fimDoDia = Calendario.fimDoDia(hora);
        List<TipoEvento> tipos = asList(new TipoEvento[]{tipoEvento});
        List<Agendamento> disponibilidade = consultarDisponibilidade(key, inicioDia,
                fimDoDia, professor, tipos, false, true, empresa);
        if(!disponibilidade.isEmpty()){
            Date primeiroHorario = disponibilidade.get(0).getInicio();
            Date fim = Uteis.somarCampoData(primeiroHorario, Calendar.MINUTE, duracao);
            if(validarHorarioDisponivel(key, primeiroHorario, fim, professor,disponibilidade)){
                sugestoes.add(Uteis.getDataAplicandoFormatacao(primeiroHorario, "HH:mm")
                        +" - "+Uteis.getDataAplicandoFormatacao(fim, "HH:mm"));
            }
            for(int p = 1; p < 50;p++){
                //#19713 aumentando a quantidade de sugestoes de 10 p /24
                if(sugestoes.size() >= 24){
                    break;
                }
                Date inicio = new Date(fim.getTime());
                fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, duracao);
                if(validarHorarioDisponivel(key, inicio, fim, professor,disponibilidade)){
                    sugestoes.add(Uteis.getDataAplicandoFormatacao(inicio, "HH:mm")
                            +" - "+Uteis.getDataAplicandoFormatacao(fim, "HH:mm"));
                }
            }
        }
        sugestoes.add("add");
        return sugestoes;
    }

    private boolean validarHorarioDisponivel(String key, Date inicio, Date fim, Integer professor, List<Agendamento> disponibilidades) throws Exception{
        boolean dentroDisponibilidade = false;
        for(Agendamento d : disponibilidades){
            if(new Date(d.getInicio().getTime()).after(new Date(Calendario.hoje().getTime())) &&
                    (Calendario.igualComHora(d.getInicio(), inicio) || d.getInicio().before(inicio))
                    && (Calendario.igualComHora(d.getFim(), fim) || d.getFim().after(fim))){
                dentroDisponibilidade = true;
                break;
            }
        }
        if(!dentroDisponibilidade){
            return false;
        }
        List<Agendamento> concomitantes = verificarEventosConcomitantes(key, inicio, fim, professor, 0);
        if (!concomitantes.isEmpty()) {
            for (Agendamento conc : concomitantes) {
                if(conc.getInicio().getTime() == fim.getTime()
                        || conc.getFim().getTime() == inicio.getTime()){
                    continue;
                }
                return false;
            }
        }
        return true;
    }

    @Override
    public List<Agendamento> consultarAgendamentoDataHora(String ctx, Date data,String horaInicial,String horaFinal, Integer codigoCliente,Integer codigoClienteSintetico) throws Exception {

        Integer hora = Integer.parseInt(horaInicial.substring(0,2));
        Integer minuto = Integer.parseInt(horaInicial.substring(3,5));
        Calendar dataInicial = Calendar.getInstance();
        dataInicial.setTime(data);
        dataInicial.set(Calendar.HOUR, hora);
        dataInicial.set(Calendar.MINUTE, minuto);
        Calendar dataFinal = Calendar.getInstance();
        dataFinal.setTime(data);
        hora = Integer.parseInt(horaFinal.substring(0,2));
        minuto = Integer.parseInt(horaFinal.substring(3,5));
        dataFinal.set(Calendar.HOUR, hora);
        dataFinal.set(Calendar.MINUTE, minuto);
        dataInicial.setTime(Uteis.somarCampoData(dataInicial.getTime(), Calendar.MINUTE, 1));
        dataFinal.setTime(Uteis.somarCampoData(dataFinal.getTime(), Calendar.MINUTE, -1));
        HashMap<String, Object> p = new HashMap<String, Object>();
        StringBuilder query = new StringBuilder();
        query.append("SELECT obj from Agendamento obj ").append("\n");
        query.append("INNER JOIN obj.cliente cli  ").append("\n");
        query.append("INNER JOIN obj.professor prof  ").append("\n");
        query.append("WHERE ((:horaInicial between obj.inicio and obj.fim ) OR (:horaFinal between obj.inicio and obj.fim) OR (:horaInicial > obj.inicio and :horaFinal < obj.fim) OR (:horaInicial < obj.inicio and :horaFinal > obj.fim))").append("\n");
        query.append("AND ((cli.codigo = :codigoClienteSintetico) OR (cli.codigoCliente = :cliente)) ").append("\n");
        query.append("AND obj.status <> :status ").append("\n");
        query.append("AND prof.ativo IS TRUE ").append("\n");

        p.put("horaInicial", dataInicial.getTime());
        p.put("horaFinal", dataFinal.getTime());
        p.put("codigoClienteSintetico", codigoClienteSintetico);
        p.put("cliente", codigoCliente);
        p.put("status", StatusAgendamentoEnum.CANCELADO);
        return obterPorParam(ctx, query.toString(), p);
    }

    @Override
    public ServicoAgendamentoDTO criarAgendamentoAluno(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            if(agendamentoDTO.getStatus() == null){
                agendamentoDTO.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
            }
            Usuario usuario = ((UsuarioService) UtilContext.getBean(UsuarioService.class)).obterPorId(usuarioSimplesDTO.getChave(), usuarioSimplesDTO.getId());
            validarCamposAgendamentoAluno(agendamentoDTO);

            Agendamento agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(usuarioSimplesDTO.getChave(), agendamentoDTO);

            validarAgendamentoDuplicado(usuarioSimplesDTO.getChave(), agendamento, agendamentoDTO);
            try {
                if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) {
                    agendamento = inserir(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                } else if (agendamento.getTipoEvento() == null && agendamento.getHorarioDisponibilidade() != null && agendamento.getHorarioDisponibilidade().getCodigo() > 0) {
                    agendamento = inserirV2(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                }
            } catch (ValidacaoException e) {
                if(e.getMessage().contains("limite de agendamentos permitidos")){
                    throw new ServiceException(e.getMessage(), e);
                } else if(e.getMessage().contains("falta(s) dentro do período")){
                    throw new ServiceException(e.getMessage(), e);
                } else if(e.getMessage().toLowerCase().contains("tempo mínimo")){
                    throw new ServiceException(e.getMessage(), e);
                } else if("validacaoalunoagendamento.carteira".equals(e.getMessage())){
                    throw new ServiceException("O aluno não está na carteira do professor selecionado.", e);
                } else {
                    throw new ServiceException("Este horário não está mais disponível. Verifique a agenda do professor ou escolha outro horário.", e);
                }
            }
            agendamento.getCliente().setUrlFoto((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getCliente().getPessoa().getFotoKey(), agendamento.getCliente().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));
            agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), agendamento.getProfessor().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));

            incluirLog(usuarioSimplesDTO.getChave(), agendamento.getCodigo().toString(), "", "", agendamento.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",sessaoService, logDao);

            if(agendamento != null && agendamento.getStatus() != null && agendamento.getStatus().equals(StatusAgendamentoEnum.CONFIRMADO)) {
                String dateTimeStr = dataEnvioNotificacao(agendamentoDTO, false);
                PushMobileRunnable.executarNoticacaoAppDoAluno(usuarioSimplesDTO.getChave(), "Agendamento iniciou", "Seu agendamento com o " + agendamento.getNomeProfessor() + " iniciou. Vamos lá.", usuario.getUserName(), clienteService.dataConvertidaFusoHorarioCliente(usuarioSimplesDTO.getChave(), Uteis.getDate(dateTimeStr, "dd/MM/yyyy HH:mm"), empresaId));
            }

            String ctx = sessaoService.getUsuarioAtual().getChave();

            return new ServicoAgendamentoDTO(agendamento, SuperControle.independente(usuarioSimplesDTO.getChave()));
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        } finally {
            clienteService.leaveAcao();
        }
    }

    @Override
    public List<Date> criarAgendamentoAlunoRecorrente(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Date> listaDatasIndisponiveis = new ArrayList<>();
            List<Date> listaMes = new ArrayList<>();
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            if(agendamentoDTO.getStatus() == null){
                agendamentoDTO.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
            }
            Usuario usuario = ((UsuarioService) UtilContext.getBean(UsuarioService.class)).obterPorId(usuarioSimplesDTO.getChave(), usuarioSimplesDTO.getId());
            validarCamposAgendamentoAluno(agendamentoDTO);

            Agendamento agendamentoV1 = AgendamentoBuilder.agendamentoDtoToAgendamento(usuarioSimplesDTO.getChave(), agendamentoDTO);

            Date dataFinalRecorrencia = new Date();
            if(agendamentoDTO.getDataFim()==null){
                dataFinalRecorrencia = agendamentoV1.getCliente().getDataVigenciaAte();
            }else{
                if(agendamentoDTO.getDataFim().after(agendamentoV1.getCliente().getDataVigenciaAte())){
                    throw new ServiceException(AgendaExcecoes.ERRO_DATA_RECORRENCIA_AGENDAMENTO);
                }else{
                    dataFinalRecorrencia = agendamentoDTO.getDataFim();
                }
            }

            Date dataInicial = Calendario.getDate("yyyyMMdd", agendamentoDTO.getData());
            Date dataFinal = dataFinalRecorrencia;
            Map<Date, Integer> listaDeDatasEDias  = new HashMap<Date, Integer>();
            if(PeriodicidadeAgendamentoEnum.getFromId(agendamentoDTO.getOpcPeriodicidade()).equals(PeriodicidadeAgendamentoEnum.SEMANAL) || PeriodicidadeAgendamentoEnum.getFromId(agendamentoDTO.getOpcPeriodicidade()).equals(PeriodicidadeAgendamentoEnum.TODOS_OS_DIAS)){
                listaDeDatasEDias = getPeriodicidadeAgendamentoSemanal(dataInicial,dataFinal,agendamentoDTO, 1);
            }else if(PeriodicidadeAgendamentoEnum.getFromId(agendamentoDTO.getOpcPeriodicidade()).equals(PeriodicidadeAgendamentoEnum.MENSAL)){

                List<Integer> listaDiasDaSemana = getDia(agendamentoDTO);
                int semanaMes = agendamentoDTO.getOpcSemanaMes();


                for(Integer diaDaLista : listaDiasDaSemana) {
                    Map<String, List<?>> lista = processarData(diaDaLista, semanaMes, dataInicial, dataFinal, 1, Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"));

                    List<Date> datasEncontradas = null;
                    for (Map.Entry<String, List<?>> entry : lista.entrySet()) {
                        if (entry.getKey().equals("datasEncontradas")) {
                            datasEncontradas = (List<Date>) entry.getValue();
                        }
                        if (entry.getKey().equals("listaMes")) {
                            listaMes = (List<Date>) entry.getValue();
                        }
                    }

                    for (Date dtEncontrada : datasEncontradas) {
                            listaDeDatasEDias.put(dtEncontrada, diaDaLista);
                    }
                }
            }else if(PeriodicidadeAgendamentoEnum.getFromId(agendamentoDTO.getOpcPeriodicidade()).equals(PeriodicidadeAgendamentoEnum.NAO_SE_REPETE)){

                Map<String, Date> horarios = getHorarios(Calendario.getDate("yyyyMMdd", agendamentoDTO.getData()), agendamentoDTO.getHorarioInicial(), agendamentoDTO.getHorarioFinal());
                listaDeDatasEDias.put(dataInicial,Calendario.getInstance(horarios.get("inicio")).get(Calendar.DAY_OF_WEEK));
            }else if(PeriodicidadeAgendamentoEnum.getFromId(agendamentoDTO.getOpcPeriodicidade()).equals(PeriodicidadeAgendamentoEnum.PERSONALIZADO)){
                if(agendamentoDTO.getOpcSemanaOuMes() == 1){//Semana
//
                    List<Integer> listaDiasDaSemana = getDia(agendamentoDTO);
                    int semanaMes = agendamentoDTO.getOpcSemanaMes();

                    for(Integer diaDaLista : listaDiasDaSemana) {
                        Map<String, List<?>> lista = processarSemana(diaDaLista, semanaMes, dataInicial, dataFinal, agendamentoDTO.getNrVezes(), Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"));

                        List<Date> datasEncontradas = null;
                        for (Map.Entry<String, List<?>> entry : lista.entrySet()) {
                            if (entry.getKey().equals("datasEncontradas")) {
                                datasEncontradas = (List<Date>) entry.getValue();
                            }
                            if (entry.getKey().equals("listaMes")) {
                                listaMes = (List<Date>) entry.getValue();
                            }
                        }
                        if(datasEncontradas.isEmpty()){
                            throw new Exception("Nenhuma data encontrada até a data fim ("+Uteis.getData(dataFinal)+")");
                        }
                        for (Date dtEncontrada : datasEncontradas) {
                            listaDeDatasEDias.put(dtEncontrada, diaDaLista);
                        }
                    }
                }else {
                    List<Integer> listaDiasDaSemana = getDia(agendamentoDTO);
                    int semanaMes = agendamentoDTO.getOpcSemanaMes();

                    for(Integer diaDaLista : listaDiasDaSemana) {
                        Map<String, List<?>> lista = processarData(diaDaLista, semanaMes, dataInicial, dataFinal, agendamentoDTO.getNrVezes(), Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"));

                        List<Date> datasEncontradas = null;
                        for (Map.Entry<String, List<?>> entry : lista.entrySet()) {
                            if (entry.getKey().equals("datasEncontradas")) {
                                datasEncontradas = (List<Date>) entry.getValue();
                            }
                            if (entry.getKey().equals("listaMes")) {
                                listaMes = (List<Date>) entry.getValue();
                            }
                        }

                        for (Date dtEncontrada : datasEncontradas) {
                            listaDeDatasEDias.put(dtEncontrada, diaDaLista);
                        }
                    }
                }
            }
            List<HorarioDisponibilidade> horarioDisponibilidadeList = Collections.emptyList();
            if (agendamentoV1.getHorarioDisponibilidade() != null
                    && agendamentoV1.getHorarioDisponibilidade().getDisponibilidade() != null) {

                Integer codigoDisp = agendamentoV1
                        .getHorarioDisponibilidade()
                        .getDisponibilidade()
                        .getCodigo();

                horarioDisponibilidadeList = horarioDisponibilidadeDao.findListByAttributes(
                        ctx,
                        new String[]{"disponibilidade.codigo"},
                        new Object[]{codigoDisp},
                        "codigo", 0
                );
            }
            Map<Date, Integer> listaDeDatasEDiasOrdenado = new TreeMap<>(listaDeDatasEDias);

            for (Map.Entry<Date, Integer> set :  listaDeDatasEDiasOrdenado.entrySet()) {
                Agendamento agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(usuarioSimplesDTO.getChave(), agendamentoDTO);

                String[] dataReplace = Uteis.getDataComHHMM(set.getKey()).split(" - ");
                String[] dataReplaceDia = dataReplace[0].split("/");
                String dtFormatada = dataReplaceDia[2]+dataReplaceDia[1]+dataReplaceDia[0];
                agendamentoDTO.setData(dtFormatada);
                Map<String, Date> horarios = getHorarios(set.getKey(), agendamentoDTO.getHorarioInicial(), agendamentoDTO.getHorarioFinal());
                agendamento.setInicio(horarios.get("inicio"));
                agendamento.setFim(horarios.get("fim"));
                agendamento.setDiaSemana(set.getValue());

                Date dataDuplicadoRecorrente = validarAgendamentoDuplicadoRecorrente(usuarioSimplesDTO.getChave(), agendamento, agendamentoDTO);
                if(dataDuplicadoRecorrente != null){
                    listaDatasIndisponiveis.add(dataDuplicadoRecorrente);
                }
                if(dataDuplicadoRecorrente == null){
                    Date dataNaoInsertAgendamentoNull = agendamento.getInicio();
                    if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) {
                        agendamento = inserir(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                    }
                    if(agendamento==null){
                        listaDatasIndisponiveis.add(dataNaoInsertAgendamentoNull);
                    }
                    if (agendamento != null && agendamento.getTipoEvento() == null && agendamento.getHorarioDisponibilidade() != null && agendamento.getHorarioDisponibilidade().getCodigo() > 0) {
                        Date dataNaoInsert = inserirRecorrente(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                        if(dataNaoInsert != null){
                            if(contemDiaDisponibilidade(horarioDisponibilidadeList, set.getValue())){
                                listaDatasIndisponiveis.add(dataNaoInsert);
                            }
                        }
                    }
                    if(listaDatasIndisponiveis.isEmpty()){
                        if(agendamento.getCodigo() != null){
                            agendamento.getCliente().setUrlFoto((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getCliente().getPessoa().getFotoKey(), agendamento.getCliente().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));
                            agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), agendamento.getProfessor().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));

                            incluirLog(usuarioSimplesDTO.getChave(), agendamento.getCodigo().toString(), "", "", agendamento.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",sessaoService, logDao);
                        }
                    }
                }
            }
            if(!listaMes.isEmpty()){
                listaDatasIndisponiveis = listaMes;
            }
            listaDatasIndisponiveis.sort(Comparator.naturalOrder());
            return listaDatasIndisponiveis;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        } finally {
            clienteService.leaveAcao();
        }
    }

    public static List<Date> getDatasEspecificas(Date dataInicial, Date dataFinal, int diaSemana, int multiplicador) {
        List<Date> datas = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataInicial);

        // Percorre o período de datas
        while (!calendar.getTime().after(dataFinal)) {
            if (calendar.get(Calendar.DAY_OF_WEEK) == diaSemana) {
                datas.add(calendar.getTime());

                if(multiplicador > 1){
                    calendar.add(Calendar.WEEK_OF_MONTH, multiplicador);
                }else{
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                }

            }else{
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

        }
        return datas;
    }

    public Map<Date, Integer> getPeriodicidadeAgendamentoSemanal(Date dataInicial,Date dataFinal , AgendamentoDTO agendamentoDTO, int multiplicador){
        Map<Date, Integer> listaDeDatasEDias  = new HashMap<Date, Integer>();

        if(agendamentoDTO.getDom()){
            getDatasEspecificas(dataInicial, dataFinal,1, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,1);
            });
        }
        if(agendamentoDTO.getSeg()){
            getDatasEspecificas(dataInicial, dataFinal,2, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,2);
            });
        }
        if(agendamentoDTO.getTer()){
            getDatasEspecificas(dataInicial, dataFinal,3, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,3);
            });
        }
        if(agendamentoDTO.getQua()){
            getDatasEspecificas(dataInicial, dataFinal,4, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,4);
            });
        }
        if(agendamentoDTO.getQui()){
            getDatasEspecificas(dataInicial, dataFinal,5, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,5);
            });
        }
        if(agendamentoDTO.getSex()){
            getDatasEspecificas(dataInicial, dataFinal,6, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,6);
            });
        }
        if(agendamentoDTO.getSab()){
            getDatasEspecificas(dataInicial, dataFinal,7, multiplicador).forEach(dt ->{
                listaDeDatasEDias.put(dt,7);
            });
        }

        return listaDeDatasEDias;
    }


    private String dataEnvioNotificacao(AgendamentoDTO agendamentoDTO, Boolean app) {
        String data = agendamentoDTO.getData().trim();
        String horario = agendamentoDTO.getHorarioFinal().trim();

        String dataFormatada = data.substring(0,4) + "-" + data.substring(4,6) + "-" + data.substring(6,8);
        String dateTimeStr = dataFormatada + " " + horario;

        System.out.println("DEBUG - dateTimeStr: '" + dateTimeStr + "'");

        try {
            Date dateTime = Calendario.getDate("yyyy-MM-dd HH:mm", dateTimeStr);
            return Calendario.somaMinutos(dateTime, -10, "dd/MM/yyyy HH:mm");
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void validarAgendamentoDuplicado(String ctx, Agendamento agendamento, AgendamentoDTO agendamentoDTO) throws Exception {
        List<Agendamento> agendamentos = consultarAgendamentoDataHora(ctx, Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"),
                agendamentoDTO.getHorarioInicial(),
                agendamentoDTO.getHorarioFinal(), agendamento.getCliente().getCodigoCliente(), agendamentoDTO.getAlunoId());

        if (agendamentos != null && agendamentos.size() > 0) {
            throw new ServiceException(AgendaExcecoes.ERRO_AGENDAMENTO_DUPLICADO);
        }
    }

    public Date validarAgendamentoDuplicadoRecorrente(String ctx, Agendamento agendamento, AgendamentoDTO agendamentoDTO) throws Exception {
        List<Agendamento> agendamentos = consultarAgendamentoDataHora(ctx, Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"),
                agendamentoDTO.getHorarioInicial(),
                agendamentoDTO.getHorarioFinal(), agendamento.getCliente().getCodigoCliente(), agendamentoDTO.getAlunoId());

        if (agendamentos != null && agendamentos.size() > 0) {
            return Uteis.getDataHora00(agendamentos.get(0).getInicio());
        }
        return null;
    }

    public void validarCamposAgendamentoAluno(AgendamentoDTO agendamentoDTO) throws ServiceException {
        Boolean valido = true;
        if (agendamentoDTO.getAlunoId() == null) {
            valido = false;
        }
        if (agendamentoDTO.getData() == null) {
            valido = false;
        }
        if (StringUtils.isBlank(agendamentoDTO.getHorarioInicial())) {
            valido = false;
        }
        if (StringUtils.isBlank(agendamentoDTO.getHorarioFinal())) {
            valido = false;
        }
        if (agendamentoDTO.getProfessor() == null) {
            valido = false;
        }
        if (agendamentoDTO.getStatus() == null) {
            valido = false;
        }
        if (agendamentoDTO.getTipo() == null) {
            valido = false;
        }

        if (!valido) {
            throw new ServiceException(AgendaExcecoes.ERRO_CAMPOS_OBRIGATORIO);
        }
    }


    public void validaAgendamento(Integer empresaId, Integer id, AgendamentoDTO agendamentoDTO) throws ServiceException {
        try {
            if(UteisValidacao.emptyString(agendamentoDTO.getData())){
                return;
            }
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            String ctx = usuarioSimplesDTO.getChave();
            Agendamento agendamento;
            if(UteisValidacao.emptyNumber(id)){
                agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(usuarioSimplesDTO.getChave(), agendamentoDTO);
            }else{
                Agendamento agendamentoSalvo = obterPorId(usuarioSimplesDTO.getChave(), id);
                if(agendamentoSalvo.getTipoEvento() != null) {
                    agendamentoSalvo.setTipoEvento(tipoEventoService.obterPorId(ctx, agendamentoSalvo.getTipoEvento().getCodigo()));
                }
                if(agendamentoSalvo.getHorarioDisponibilidade() != null) {
                    agendamentoSalvo.setHorarioDisponibilidade(horarioDisponibilidadeService.obterPorId(ctx, agendamentoSalvo.getHorarioDisponibilidade().getCodigo()));
                }
                agendamentoSalvo.setProfessor(professorSinteticoService.obterPorId(ctx, agendamentoSalvo.getProfessor().getCodigo()));
                agendamentoSalvo.setCliente(clienteService.obterPorId(ctx, agendamentoSalvo.getCliente().getCodigo()));
                agendamento = AgendamentoBuilder.edicaoDtoToAgendamento(agendamentoDTO, agendamentoSalvo);
            }
            Usuario usuario = usuarioService.obterPorId(usuarioSimplesDTO.getChave(), usuarioSimplesDTO.getId());
            if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) {
                validaAgendamento(ctx, agendamento, usuario, empresaId);
            } else {
                validaAgendamentoV2(ctx, agendamento, usuario, empresaId);
            }
        }catch (ServiceException e){
            throw e;
        }catch (Exception e){
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO);
        }
    }

    @Override
    public ServicoAgendamentoDTO alterarAgendamentoAluno(HttpServletRequest request, Integer empresaId, Integer id, AgendamentoDTO agendamentoDTO) throws ServiceException, HorarioConcomitanteException {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            Usuario usuario = usuarioService.obterPorId(usuarioSimplesDTO.getChave(), usuarioSimplesDTO.getId());
            Agendamento agendamentoSalvo = obterPorId(usuarioSimplesDTO.getChave(), id);
            Agendamento antesAlteracao = UtilReflection.copy(agendamentoSalvo);
            try {
                clienteService.acao(AcaoAlunoEnum.ALTEROU_SERVICO, agendamentoSalvo.getCliente().getMatriculaString());
            }catch (Exception e){}
            Agendamento agendamento = AgendamentoBuilder.edicaoDtoToAgendamento(agendamentoDTO, agendamentoSalvo);

            try {
                if (agendamentoDTO.getHorarioAlterado()) {
                    validarHorarioAgendamento(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                }
                if (agendamento.getTipoEvento() != null) {
                    validaAgendamento(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                } else {
                    validaAgendamentoV2(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
                }
            }catch (ValidacaoException e){
                if(e.getMessage().contains("limite de agendamentos permitidos")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if(e.getMessage().contains("falta(s) dentro do período")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if(e.getMessage().toLowerCase().contains("tempo mínimo")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if("validacaoalunoagendamento.carteira".equals(e.getMessage())){
                    throw new HorarioConcomitanteException("O aluno não está na carteira do professor selecionado.");
                } else {
                    throw new HorarioConcomitanteException("Este horário não está mais disponível. Verifique a agenda do professor ou escolha outro horário.");
                }
            }catch (Exception e){
                if(e.getMessage() != null && e.getMessage().toLowerCase().contains("tempo mínimo")){
                    throw new HorarioConcomitanteException(e.getMessage());
                }else {
                    throw new HorarioConcomitanteException("Este horário não está mais disponível. Verifique a agenda do professor ou escolha outro horário.");
                }
            }
            agendamentoSalvo = alterar(usuarioSimplesDTO.getChave(), agendamento, usuario, empresaId);
            agendamentoSalvo.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamentoSalvo.getProfessor().getPessoa().getFotoKey(), agendamentoSalvo.getProfessor().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));
            agendamentoSalvo.getCliente().setUrlFoto(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamentoSalvo.getCliente().getPessoa().getFotoKey(), agendamentoSalvo.getCliente().getCodigoPessoa(), false, usuarioSimplesDTO.getChave(), false));

            incluirLog(usuarioSimplesDTO.getChave(),
                    agendamento.getCodigo().toString(), "",
                    antesAlteracao.getDescricaoParaLog(agendamento),
                    agendamento.getDescricaoParaLog(antesAlteracao),
                    "ALTERAÇÃO",
                    "ALTERAÇÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",
                    sessaoService, logDao);

            return new ServicoAgendamentoDTO(agendamentoSalvo, SuperControle.independente(usuarioSimplesDTO.getChave()));
        } catch (HorarioConcomitanteException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public void validarHorarioAgendamento(final String ctx, Agendamento agendamento, Usuario usuario, Integer empresaId) throws ServiceException {
        List<Agendamento> disponibilidades = null;
        if (agendamento.getTipoEvento() != null) {
            List<TipoEvento> tipos = new ArrayList<TipoEvento>();
            tipos.add(agendamento.getTipoEvento());
            disponibilidades = consultarDisponibilidade(ctx,
                    Uteis.somarCampoData(agendamento.getInicio(), Calendar.SECOND, 1),
                    Uteis.somarCampoData(agendamento.getFim(), Calendar.SECOND, -1),
                    agendamento.getProfessor().getCodigo(),
                    tipos, false, false,
                    empresaId != null ? empresaId : usuario.getEmpresaZW());
        } else {
            List<HorarioDisponibilidade> horariosDisp = new ArrayList<HorarioDisponibilidade>();
            horariosDisp.add(agendamento.getHorarioDisponibilidade());
            disponibilidades = consultarDisponibilidadeV2(ctx,
                    Uteis.somarCampoData(agendamento.getInicio(), Calendar.SECOND, 1),
                    Uteis.somarCampoData(agendamento.getFim(), Calendar.SECOND, -1),
                    agendamento.getProfessor().getCodigo(),
                    horariosDisp, false, false,
                    empresaId != null ? empresaId : usuario.getEmpresaZW());
        }
        if (disponibilidades.isEmpty()) {
            throw new ValidacaoException(AgendaExcecoes.SEM_DISPONIBILIDADE_HORARIO.name());
        }
    }

    @Override
    public void removerAgendamentoAluno(Integer agendamentoId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Agendamento agendamento = obterPorId(ctx, agendamentoId);
            clienteService.acao(AcaoAlunoEnum.EXCLUIU_SERVICO, agendamento.getCliente().getMatriculaString());
            agendamento.setOperacaoEmMassa(TipoRevisaoEnum.DELETE.getId());
            String obs = agendamento.getObservacao();
            agendamento.setObservacao("");
            agendamento = agendamentoDao.update(ctx, agendamento);
            incluirLog(ctx, agendamento.getCodigo().toString(), "", agendamento.getDescricaoParaLog(null), "", "EXCLUSÃO", "EXCLUSÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",sessaoService, logDao);
            agendamentoDao.updateAlgunsCampos(ctx, new String[]{"observacao"},
                    new Object[]{obs}, new String[]{"codigo"}, new Object[]{agendamento.getCodigo()});
            if (agendamento != null) {
                excluir(ctx, agendamento, false, false, false);
            } else {
                throw new ServiceException(AgendaExcecoes.ERRO_CONSULTAR_AGENDAMENTO);
            }
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_AGENDAMENTO, e);
        } finally {
            clienteService.leaveAcao();
        }
    }

    public void refresh(final String ctx, Agendamento agendamento) {
        try {
            agendamentoDao.refresh(ctx, agendamento);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public HorarioPersonalAgendaJSON sugestoesHorarioApp(final String key, final TipoEvento tipoEvento,
                                                         final Date hora, final Integer empresa) throws Exception{
        HorarioPersonalAgendaJSON sugestoes = new HorarioPersonalAgendaJSON();
        Integer duracao = 0;
        Integer quantidadeEnviar = 0;
        switch (tipoEvento.getDuracao()){
            case DURACAO_LIVRE:
                duracao = 60;
                break;
            case INTERVALO_DE_TEMPO:
                duracao = tipoEvento.getDuracaoMinutosMin();
                break;
            case DURACAO_PREDEFINIDA:
                duracao = tipoEvento.getDuracaoMinutosMin();
                break;
        }
        Date inicioDia = Calendario.getDataComHoraZerada(hora);
        Date fimDoDia = Calendario.fimDoDia(hora);
        fimDoDia = Uteis.somarCampoData(fimDoDia, Calendar.DAY_OF_MONTH, 1);
        List<TipoEvento> tipos = Arrays.asList(new TipoEvento[]{tipoEvento});
        List<Agendamento> disponibilidade = consultarDisponibilidadeApp(key, inicioDia,
                fimDoDia, tipos, false, true, empresa);
        if(!disponibilidade.isEmpty()){
            Date primeiroHorario = disponibilidade.get(0).getInicio();
            Date fim = Uteis.somarCampoData(primeiroHorario, Calendar.MINUTE, duracao);
            AgendamentoPersonalJSON agendamentos = validarHorarioDisponivelApp(key, primeiroHorario, fim,disponibilidade);
            if(agendamentos.isValido()){
                sugestoes.setProfessorId(agendamentos.getIdProfessor());
                sugestoes.setAgendamentoId(agendamentos.getIdAgendamento());
                sugestoes.setInicio(primeiroHorario.getTime());
                sugestoes.setFim(fim.getTime());
                quantidadeEnviar++;
            }
            for(int p = 1; p < 50;p++){
                //#19713 aumentando a quantidade de sugestoes de 10 p /24
                if(quantidadeEnviar >= 24){
                    break;
                }
                Date inicio = new Date(fim.getTime());
                fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, duracao);
                AgendamentoPersonalJSON agendamento = validarHorarioDisponivelApp(key, inicio, fim,disponibilidade);
                if(agendamento.isValido()){
                    sugestoes.setProfessorId(agendamento.getIdProfessor());
                    sugestoes.setInicio(inicio.getTime());
                    sugestoes.setFim(fim.getTime());
                    sugestoes.setAgendamentoId(agendamento.getIdAgendamento());
                    quantidadeEnviar++;
                }
            }
        }
        return sugestoes;
    }

    public List<Agendamento> consultarDisponibilidadeApp(final String ctx, Date inicio, Date fim,
                                                         List<TipoEvento> tipos, boolean between, boolean inside, final Integer empresaZW) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            if (inside) {
                query.append(" where obj.inicio >= :inicio and obj.fim <= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            } else if (between) {
                query.append(" where obj.inicio <= :inicio and obj.fim > :fim and obj.disponibilidade IS true  ");
            } else {
                query.append(" where obj.inicio <= :inicio and obj.fim >= :fim and obj.disponibilidade IS true  ");
                p.put("fim", fim);
            }
            if (empresaZW != null) {
                query.append(" and obj.professor.empresa.codZW = ").append(empresaZW);
            }
            if (tipos != null && !tipos.isEmpty()) {
                String tiposSelecionados = Uteis.getListaEscolhidos(tipos, "Escolhido", "Codigo", true, false);
                if (!tiposSelecionados.isEmpty()) {
                    query.append(" and obj.tipoEvento.codigo IN (").append(tiposSelecionados).append(") ");
                }

            }
            query.append(" and obj.professor.ativo is true  order by obj.inicio ");
            if(!inside && between){
                p.put("inicio", Uteis.somarCampoData(inicio, Calendar.MINUTE, 29));
                p.put("fim", inicio);
            }else{
                p.put("inicio", inicio);
            }
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private AgendamentoPersonalJSON validarHorarioDisponivelApp(String key, Date inicio, Date fim, List<Agendamento> disponibilidades) throws Exception{
        AgendamentoPersonalJSON agendamento = new AgendamentoPersonalJSON();
        for(Agendamento d : disponibilidades){
            if(new Date(d.getInicio().getTime()).after(new Date(Calendario.hoje().getTime())) &&
                    (Calendario.igualComHora(d.getInicio(), inicio) || d.getInicio().before(inicio))
                    && (Calendario.igualComHora(d.getFim(), fim) || d.getFim().after(fim))){
                agendamento.setIdProfessor(d.getProfessor().getCodigo());
                agendamento.setValido(true);
                agendamento.setIdAgendamento(d.getCodigo());
                break;
            }
        }
        if(!agendamento.isValido()){
            return agendamento;
        }
        List<Agendamento> concomitantes = verificarEventosConcomitantesApp(key, inicio, fim, 0);
        if (!concomitantes.isEmpty()) {
            for (Agendamento conc : concomitantes) {
                if(conc.getInicio().getTime() == fim.getTime()
                        || conc.getFim().getTime() == inicio.getTime()){
                    continue;
                }
                return agendamento;
            }
        }
        return agendamento;
    }

    public List<Agendamento> verificarEventosConcomitantesApp(final String ctx, Date inicio, Date fim, Integer codigoEvento) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from Agendamento obj ");
            query.append(" WHERE ((obj.inicio <= :inicio and obj.fim >= :fim) ");
            query.append(" OR (obj.inicio BETWEEN :inicio and  :fim) ");
            query.append(" OR (obj.fim BETWEEN :inicio and  :fim)) ");
            query.append(" AND obj.status <> ").append(StatusAgendamentoEnum.CANCELADO.getId());
            query.append(" AND obj.disponibilidade is not true ");
            if (codigoEvento != null && codigoEvento > 0) {
                p.put("evento", codigoEvento);
                query.append(" and obj.codigo <> :evento ");
            }
            query.append(" order by obj.inicio ");

            p.put("inicio", inicio);
            p.put("fim", fim);
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ServicoAgendamentoPersonalDTO criarAgendamentoAlunoPersonal(HttpServletRequest request, Integer empresaId, AgendamentoPersonalDTO agendamentoDTO, String ctx) throws ServiceException {
        try {

            validarCamposAgendamentoAluno(agendamentoDTO);
            Usuario usuario = usuarioService.consultarPorMatricula(ctx, agendamentoDTO.getMatricula());

            if(agendamentoDTO.getStatus() == null){
                agendamentoDTO.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
            }

            Agendamento agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(ctx, agendamentoDTO);

            validarAgendamentoDuplicado(ctx, agendamento, agendamentoDTO);
            boolean agendamentoRegistrado = false;
            if (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) {
                agendamento = inserir(ctx, agendamento, usuario, empresaId);
                agendamentoRegistrado = true;
            }
            if (agendamento.getTipoEvento() == null && agendamento.getHorarioDisponibilidade() != null && agendamento.getHorarioDisponibilidade().getCodigo() > 0) {
                agendamento = inserirV2(ctx, agendamento, usuario, empresaId);
                agendamentoRegistrado = true;
            }
            if (!agendamentoRegistrado) {
                throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO);
            }

            agendamento.getCliente().setUrlFoto((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getCliente().getPessoa().getFotoKey(), agendamento.getCliente().getCodigoPessoa(), false, ctx, false));
            agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), agendamento.getProfessor().getCodigoPessoa(), false, ctx, false));
            incluirLog(ctx,
                    agendamento.getCodigo().toString(), "", "",
                    agendamento.getDescricaoParaLog(null), "INCLUSÃO",
                    "INCLUSÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO,
                    "Agendamento",usuario == null ? "-" : usuario.getUserName(), logDao);

            return new ServicoAgendamentoPersonalDTO(agendamento, SuperControle.independente(ctx));
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        }
    }

    public void validarCamposAgendamentoAluno(AgendamentoPersonalDTO agendamentoDTO) throws ServiceException {
        boolean valido = true;
        if (agendamentoDTO.getMatricula() == null) {
            valido = false;
        }
        if (agendamentoDTO.getDia() == null) {
            valido = false;
        }
        if (StringUtils.isBlank(agendamentoDTO.getHorarioInicial())) {
            valido = false;
        }
        if (StringUtils.isBlank(agendamentoDTO.getHorarioFinal())) {
            valido = false;
        }
        if (agendamentoDTO.getProfessorId() == null) {
            valido = false;
        }
        if (agendamentoDTO.getStatus() == null) {
            valido = false;
        }
        if (agendamentoDTO.getTipoAgendamentoId() == null && (agendamentoDTO.getTipoEvento() == null || agendamentoDTO.getTipoEvento())) {
            valido = false;
        }

        if (!valido) {
            throw new ServiceException(AgendaExcecoes.ERRO_CAMPOS_OBRIGATORIO);
        }
    }

    public void validarAgendamentoDuplicado(String ctx, Agendamento agendamento, AgendamentoPersonalDTO agendamentoDTO) throws Exception {
        ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, String.valueOf(agendamentoDTO.getMatricula()));
        List<Agendamento> agendamentos = consultarAgendamentoDataHora(ctx, Calendario.getDate("dd/MM/yyyy", agendamentoDTO.getDia()),
                agendamentoDTO.getHorarioInicial(),
                agendamentoDTO.getHorarioFinal(), cliente.getCodigo(), agendamento.getCliente().getCodigoCliente());

        if (agendamentos != null && agendamentos.size() > 0) {
            throw new ServiceException(AgendaExcecoes.ERRO_AGENDAMENTO_DUPLICADO);
        }
    }

    @Override
    public void removerAgendamentoAlunoPersonal(String ctx, Integer agendamentoId) throws ServiceException {
        try {
            Agendamento agendamento = obterPorId(ctx, agendamentoId);
            if (agendamento != null) {
                excluir(ctx, agendamento, false, false, false);
            } else {
                throw new ServiceException(AgendaExcecoes.ERRO_CONSULTAR_AGENDAMENTO);
            }
        } catch (ServiceException e) {
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_AGENDAMENTO, e);
        }
    }

    @Override
    public ServicoAgendamentoPersonalDTO alterarAgendamentoAlunoPersonal(HttpServletRequest request, Integer empresaId, Integer id, AgendamentoPersonalDTO agendamentoDTO, String ctx) throws ServiceException {
        try {
            Usuario usuario = usuarioService.consultarPorMatricula(ctx, agendamentoDTO.getMatricula());
            Agendamento agendamentoSalvo = obterPorId(ctx, id);

            Agendamento agendamento = AgendamentoBuilder.edicaoDtoToAgendamento(agendamentoDTO, agendamentoSalvo);

            try {
                validaAgendamento(ctx, agendamento, usuario, empresaId);
            }catch (ValidacaoException e){
                if(e.getMessage().contains("limite de agendamentos permitidos")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if(e.getMessage().contains("falta(s) dentro do período")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if(e.getMessage().toLowerCase().contains("tempo mínimo")){
                    throw new HorarioConcomitanteException(e.getMessage());
                } else if("validacaoalunoagendamento.carteira".equals(e.getMessage())){
                    throw new HorarioConcomitanteException("O aluno não está na carteira do professor selecionado.");
                } else {
                    throw new HorarioConcomitanteException("Este horário não está mais disponível. Verifique a agenda do professor ou escolha outro horário.");
                }
            }catch (Exception e){
                if(e.getMessage() != null && e.getMessage().toLowerCase().contains("tempo mínimo")){
                    throw new HorarioConcomitanteException(e.getMessage());
                }else {
                    throw new HorarioConcomitanteException("Este horário não está mais disponível. Verifique a agenda do professor ou escolha outro horário.");
                }
            }
            agendamentoSalvo = alterar(ctx, agendamento, usuario, empresaId);
            agendamentoSalvo.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class)).defineURLFotoPessoa(request, agendamentoSalvo.getProfessor().getPessoa().getFotoKey(), agendamentoSalvo.getProfessor().getCodigoPessoa(), false, ctx, false));
            agendamentoSalvo.getCliente().setUrlFoto(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamentoSalvo.getCliente().getPessoa().getFotoKey(), agendamentoSalvo.getCliente().getCodigoPessoa(), false, ctx, false));

            return new ServicoAgendamentoPersonalDTO(agendamentoSalvo, SuperControle.independente(ctx));
        } catch (HorarioConcomitanteException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO);
        }
    }

    @Override
    public Agendamento alterarTodasEmpresas(final String ctx, Agendamento object, Usuario usuario, Date date) throws ServiceException {
        usuario.setEmpresaZW(null);
        try {
            ConfiguracaoSistema cfgPermitirAlunos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_REAGENDAMENTO_POR_ALUNO);
            if(usuario.getTipo() != null && usuario.getTipo().equals(TipoUsuarioEnum.ALUNO) && !cfgPermitirAlunos.getValorAsBoolean()){
                throw new Exception("mobile.excecao.aluno.reagendar");
            }
            object.setCliente(object.getDisponibilidade() ? null : object.getCliente());
            validaAgendamentoEmpresas(ctx, object, null,date);
            if (!object.getStatus().equals(StatusAgendamentoEnum.CANCELADO)
                    && !object.getStatus().equals(StatusAgendamentoEnum.FALTOU)) {
                if (object.getTipoEvento() != null) {
                    validarRestricaoTipoEvento(ctx, object);
                } else {
                    validarRestricaoDisponibilidadeConfigV2(ctx, object);
                }
            }
            preencherUsuario(object, usuario);
            object = getAgendamentoDao().update(ctx, object);
            return object;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void gravarLogAlteracao(String ctx, String username, Agendamento antes, Agendamento depois){
        try {
            incluirLog(ctx,
                    depois.getCodigo().toString(), "",
                    antes.getDescricaoParaLog(depois),
                    depois.getDescricaoParaLog(antes),
                    "ALTERAÇÃO",
                    "ALTERAÇÃO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",
                    username, logDao);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void gravarLogCancelamento(String ctx, String username, Agendamento antes, Agendamento depois){
        try {
            incluirLog(ctx,
                    depois.getCodigo().toString(), "",
                    antes.getDescricaoParaLog(depois),
                    depois.getDescricaoParaLog(antes),
                    "CANCELAMENTO",
                    "CANCELAMENTO DE AGENDAMENTO", EntidadeLogEnum.AGENDADESERVICO, "Agendamento",
                    username, logDao);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void validarAgendamentoAluno(HttpServletRequest request, Integer empresaId, AgendamentoDTO agendamentoDTO) throws ServiceException {
        UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
        Agendamento agendamento = new Agendamento();
        List<Agendamento> agendamentos = new ArrayList<>();
        try {
            agendamento = AgendamentoBuilder.agendamentoDtoToAgendamento(usuarioSimplesDTO.getChave(), agendamentoDTO);
            agendamentos = consultarAgendamentoDataHora(usuarioSimplesDTO.getChave(),
                    Uteis.getDate(agendamentoDTO.getData(), "yyyyMMdd"),
                    agendamentoDTO.getHorarioInicial(),
                    agendamentoDTO.getHorarioFinal(), agendamentoDTO.getAlunoId(),
                    agendamento.getCliente() == null || UteisValidacao.emptyNumber(agendamento.getCliente().getCodigoCliente()) ? 0 : agendamento.getCliente().getCodigoCliente()
            );
        }catch (Exception e){
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO);
        }
        if (agendamentos != null && agendamentos.size() > 0) {
            throw new ServiceException(AgendaExcecoes.ERRO_AGENDAMENTO_DUPLICADO);
        }

        if (agendamento.getCliente() != null
                && !UteisValidacao.emptyNumber(agendamento.getCliente().getCodigo())) {
            validarRestricaoTipoEvento(usuarioSimplesDTO.getChave(), agendamento);
        }

        int duracao = 0;
        if (!agendamento.getTipoEvento().getDuracao().name().equals("DURACAO_LIVRE")) {
            duracao = agendamento.getTipoEvento().getDuracaoMinutosMin();
        }

        if (duracao != 0 && (Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) < duracao &&
                !(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()) + 1 == duracao))) {
            int tempoAtual = Math.toIntExact(Calendario.diferencaEmMinutos(agendamento.getInicio(), agendamento.getFim()));
            throw new ServiceException("O tempo mínimo para este tipo de agendamento é de " + duracao +
                " minutos. Tempo atual: " + tempoAtual + " minutos.");
        }

        if (agendamento.getTipoEvento().getDuracaoIntervalo() && !agendamento.getDisponibilidade()) {
            Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
            //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
            if (!verificarIntervaloComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(),
                    agendamento.getTipoEvento().getDuracaoMinutosMax(), 2)) {
                throw new ValidacaoException("mensagem.validacao.duracaoIntervalo");
            }
        }
        if (agendamento.getTipoEvento().getDuracaoPreDefinida() && !agendamento.getDisponibilidade()) {
            Long minutosEntreDatas = Uteis.minutosEntreDatas(agendamento.getInicio(), agendamento.getFim());
            //comparar os minutos, com tolerancia de 2 minutos, pois o sistema pode retirar um minuto no inicio e outro no fim automaticamente
            if (!compararComTolerancia(minutosEntreDatas.intValue(), agendamento.getTipoEvento().getDuracaoMinutosMin(), 2)) {
                throw new ValidacaoException(String.format(getViewUtils().getMensagem("mensagem.validacao.duracaoPreDefinida"),
                        agendamento.getTipoEvento().getDuracaoMinutosMin()));
            }
        }

    }

    @Override
    public List<Agendamento> consultarAgendamentosDoDia(String ctx, Date dataInicio, Date dataFim) throws Exception {
        HashMap<String, Object> p = new HashMap<String, Object>();
        StringBuilder query = new StringBuilder();
        query.append("SELECT obj from Agendamento obj ").append("\n");
        query.append("WHERE ((obj.inicio between :dataInicio and :dataFim) OR (obj.fim between :dataInicio and :dataFim) OR (obj.inicio <= :dataInicio and obj.fim >= :dataFim))").append("\n");
        query.append(" AND obj.disponibilidade is false ").append("\n");
        query.append("AND obj.status <> :status AND obj.status <> :status2  ORDER BY obj.inicio").append("\n");

        p.put("dataInicio", dataInicio);
        p.put("dataFim", dataFim);
        p.put("status", StatusAgendamentoEnum.CANCELADO);
        p.put("status2", StatusAgendamentoEnum.REAGENDADO);
        return obterPorParam(ctx, query.toString(), p);
    }

    @Override
    public List<Agendamento> consultarAgendamentoDataHoraProfessor(String ctx, Date data,
                                                                   String horaInicial,
                                                                   String horaFinal,
                                                                   Integer codigoProfessor) throws Exception {

        Integer hora = Integer.parseInt(horaInicial.substring(0,2));
        Integer minuto = Integer.parseInt(horaInicial.substring(3,5));
        Calendar dataInicial = Calendar.getInstance();
        dataInicial.setTime(data);
        dataInicial.set(Calendar.HOUR, hora);
        dataInicial.set(Calendar.MINUTE, minuto);
        Calendar dataFinal = Calendar.getInstance();
        dataFinal.setTime(data);
        hora = Integer.parseInt(horaFinal.substring(0,2));
        minuto = Integer.parseInt(horaFinal.substring(3,5));
        dataFinal.set(Calendar.HOUR, hora);
        dataFinal.set(Calendar.MINUTE, minuto);

        HashMap<String, Object> p = new HashMap<String, Object>();
        StringBuilder query = new StringBuilder();
        query.append("SELECT obj from Agendamento obj ").append("\n");
        query.append("WHERE ((:horaInicial between obj.inicio and obj.fim ) OR (:horaFinal between obj.inicio and obj.fim) OR (:horaInicial > obj.inicio and :horaFinal < obj.fim) OR (:horaInicial < obj.inicio and :horaFinal > obj.fim))").append("\n");
        query.append("AND obj.professor.codigo = :professor and obj.disponibilidade is false ").append("\n");
        query.append("AND obj.status <> :status AND obj.status <> :status2  ORDER BY obj.inicio").append("\n");

        p.put("horaInicial", dataInicial.getTime());
        p.put("horaFinal", dataFinal.getTime());
        p.put("professor", codigoProfessor);
        p.put("status", StatusAgendamentoEnum.CANCELADO);
        p.put("status2", StatusAgendamentoEnum.REAGENDADO);
        return obterPorParam(ctx, query.toString(), p);
    }

    public Integer obterNsuPorCodigoAgendamento(final String ctx, Integer codigo) {
        try {
            String sql = "select nsu from agendamento a where codigo = " + codigo;
            try (ResultSet rs = agendamentoDao.createStatement(ctx, sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("nsu");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, AgendamentoServiceImpl.class);
        }
        return null;
    }

    @Override
    public List<TreinosExecutadosEAcessosPorDiaVO> calculaMediaFrequenciaAluno(HttpServletRequest request, Integer codigoProfessor, String ctx, Integer periodo) throws Exception {
        Date dataAtual = new Date(System.currentTimeMillis());
        Date dataInicio = getDataInicioPeriodo(periodo, dataAtual);
        List<TreinosExecutadosEAcessosPorDiaVO> treinoRealizado = dashboardBIService.obterTreinosRealizadosAgrupandoPorDiaDaSemana(ctx, dataInicio, codigoProfessor);

        return treinoRealizado;
    }

    @Override
    public ConfiguracoesDiasDeBloqueioDTO configurarDiasBloqueio(ConfiguracoesDiasDeBloqueioDTO configuracoesDiasDeBloqueioDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        String qtdFaltasBloqueioAluno = configuracoesDiasDeBloqueioDTO.getQtd_faltas_bloqueio_aluno();
        String qtdTempoBloqueioAluno = configuracoesDiasDeBloqueioDTO.getQtd_tempo_bloqueio_aluno();
        try (ConfiguracaoSistemaJdbcService config = new ConfiguracaoSistemaJdbcService(getPropsKey(ctx))) {
            if(config.findByConfigEnum(ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO)==null && config.findByConfigEnum(ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO) ==null){
                config.insertConfigValue(ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO, String.valueOf(qtdFaltasBloqueioAluno));
                config.insertConfigValue(ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO, String.valueOf(qtdTempoBloqueioAluno));
            }else{
                config.updateConfigValue(ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO, String.valueOf(qtdFaltasBloqueioAluno));
                config.updateConfigValue(ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO, String.valueOf(qtdTempoBloqueioAluno));
            }
            return configuracoesDiasDeBloqueioDTO;
        } catch (ManyDataBasesException e) {
            throw new ServiceException(AgendaExcecoes.ERRO_CONFIGURAR_DIAS_BLOQUEIO);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_CONFIGURAR_DIAS_BLOQUEIO);
        }

    }
    @Override
    public ConfiguracoesDiasDeBloqueioDTO buscarConfiguracaoDiasBloqueio() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try (ConfiguracaoSistemaJdbcService config = new ConfiguracaoSistemaJdbcService(getPropsKey(ctx))) {

            String qtdFaltasBloqueioAluno = config.findByConfigEnum(ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO);
            String qtdTempoBloqueioAluno = config.findByConfigEnum(ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO);

            ConfiguracoesDiasDeBloqueioDTO configuracoesDiasDeBloqueioDTO = new ConfiguracoesDiasDeBloqueioDTO();
            if(qtdFaltasBloqueioAluno!=null){
                configuracoesDiasDeBloqueioDTO.setQtd_faltas_bloqueio_aluno(qtdFaltasBloqueioAluno);
            }

            if(qtdTempoBloqueioAluno!=null){
                configuracoesDiasDeBloqueioDTO.setQtd_tempo_bloqueio_aluno(qtdTempoBloqueioAluno);
            }

            return configuracoesDiasDeBloqueioDTO;
        } catch (ManyDataBasesException e) {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_CONFIGURACAO_DIAS_BLOQUEIO);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_CONFIGURACAO_DIAS_BLOQUEIO);
        }
    }

    private static Date getDataInicioPeriodo(Integer periodo, Date dataAtual) {
        Date dataInicio;
        if (periodo == 1) {
            dataInicio = Uteis.somarMeses(dataAtual, -1);
        } else if (periodo == 2) {
            dataInicio = Uteis.somarMeses(dataAtual, -2);
        } else if (periodo == 3) {
            dataInicio = Uteis.somarMeses(dataAtual, -3);
        } else {
            throw new IllegalArgumentException("Período inválido! O período deve ser 1, 2 ou 3.");
        }
        return dataInicio;
    }

    @Override
    public FrequenciaAtendimentoAlunoVO frequenciaTreinoEAtendimentoAluno(String ctx, Integer id, Integer periodo, HttpServletRequest request) throws Exception {
        ClienteSintetico clienteSintetico = clienteService.obterPorCodigo(ctx, id);

        if (clienteSintetico == null) {
            throw new Exception("Cliente não encontrado.");
        }

        Date dataAtual = new Date(System.currentTimeMillis());
        Date dataInicio = getDataInicioPeriodo(periodo, dataAtual);
        FrequenciaAtendimentoAlunoVO frequenciaAtendimentoAlunoVO = new FrequenciaAtendimentoAlunoVO();
        frequenciaAtendimentoAlunoVO.setTreinos(programaTreinoService.obterQuantidadeTreinosRealizadosPorAluno(ctx, id, dataInicio, dataAtual));
        frequenciaAtendimentoAlunoVO.setAtendimentos(getQuantidadeAtendimentosPorCliente(ctx, id, dataInicio, dataAtual));
        frequenciaAtendimentoAlunoVO.setPresencas(agendaTotalService.consultarAulasConfirmadas(ctx, clienteSintetico.getMatricula(),
                Uteis.getDataAplicandoFormatacao(dataInicio, "dd/MM/yyyy"), Uteis.getDataAplicandoFormatacao(dataAtual, "dd/MM/yyyy"), null).size());

        return frequenciaAtendimentoAlunoVO;
    }

    private Integer getQuantidadeAtendimentosPorCliente(String ctx, Integer id, Date dataInicio, Date dataAtual) throws Exception {
        StringBuilder where = new StringBuilder(" WHERE inicio BETWEEN :inicio AND :fim  ");
        where.append(" and cliente.codigo = (").append(id).append(") ");
        Map<String, Object> p = new HashMap<String, Object>();
        p.put("inicio", dataInicio);
        p.put("fim", Calendario.fimDoDia(dataAtual));
        return clienteAcompanhamentoDao.findByParam(ctx, where, p).size();
    }

    @Override
    public String[]  buscaHorarioTurno(final String ctx, String turno){
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String[] horarios = new String[2];
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                    "SELECT horarioinicio, horariofim FROM faixahorarioacessocliente WHERE unaccent(upper(descricao)) LIKE '%"+turno+"%'", conZW)) {
                while (rs.next()) {
                    horarios[0] = rs.getString("horarioinicio");
                    horarios[1] = rs.getString("horariofim");
                }
            }
            return horarios;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Override
    public List<ObjetivoAlunoVO> objetivos(String ctx, Integer matricula, Integer status, Boolean primario) throws Exception {
        List<ObjetivoAluno> objetivoAlunos = objetivoAlunoDao.findByMatriculaAlunoEStatus(ctx, matricula, status, primario);
        List<ObjetivoAlunoVO> objetivos = new ArrayList<>();

        for(ObjetivoAluno objetivo : objetivoAlunos) {
            ObjetivoAlunoVO objetivoVO = getObjetivoAlunoVO(ctx, objetivo);

            objetivos.add(objetivoVO);
        }

        return objetivos;
    }

    private ObjetivoAlunoVO getObjetivoAlunoVO(String ctx, ObjetivoAluno objetivo) throws Exception {
        if(objetivo == null) {
            return null;
        }
        ObjetivoAlunoVO objetivoVO = new ObjetivoAlunoVO();
        objetivoVO.setCodigo(objetivo.getCodigo());
        objetivoVO.setDescricao(objetivo.getDescricao());
        objetivoVO.setDataInicio(Uteis.getData(objetivo.getDatainicio(), "dd/MM/yyyy"));
        objetivoVO.setDataFinal(Uteis.getData(objetivo.getDatafinal(), "dd/MM/yyyy"));
        objetivoVO.setPrimario(objetivo.getPrimario());
        objetivoVO.setStatus(StatusObjetivoAlunoEnum.getEnumById(objetivo.getStatus()));
        objetivoVO.setObjetivoPredefinido(new ObjetivoPredefinidoDTO(objetivo.getObjetivoPredefinido()));
        List<ObjetivoIntermediarioAluno> objetivoIntermediarioAlunos = objetivoIntermediarioAlunoDao.findByObjetivoAluno(ctx, objetivo.getCodigo());

        if(objetivoIntermediarioAlunos != null && !objetivoIntermediarioAlunos.isEmpty()) {
            objetivoVO.setObjetivosIntermediarios(new ArrayList<>());

            for (ObjetivoIntermediarioAluno objetivoIntermediario : objetivoIntermediarioAlunos) {
                ObjetivoIntermediarioAlunoVO objetivoIntermediarioVO = getObjetivoIntermediarioAlunoVO(objetivoIntermediario);

                if(objetivoIntermediarioVO != null) {
                    objetivoVO.getObjetivosIntermediarios().add(objetivoIntermediarioVO);
                }
            }
        }
        return objetivoVO;
    }

    private static ObjetivoIntermediarioAlunoVO getObjetivoIntermediarioAlunoVO(ObjetivoIntermediarioAluno objetivoIntermediario) {
        if (objetivoIntermediario == null) {
            return null;
        }
        ObjetivoIntermediarioAlunoVO objetivoIntermediarioVO = new ObjetivoIntermediarioAlunoVO();
        objetivoIntermediarioVO.setCodigo(objetivoIntermediario.getCodigo());
        objetivoIntermediarioVO.setDescricao(objetivoIntermediario.getDescricao());
        objetivoIntermediarioVO.setObjetivoPredefinido(objetivoIntermediario.getObjetivopredefinido().getNome());
        objetivoIntermediarioVO.setDataInicio(Uteis.getDataFormatoBD(objetivoIntermediario.getDatainicio()));
        objetivoIntermediarioVO.setDataFinal(Uteis.getDataFormatoBD(objetivoIntermediario.getDatafinal()));
        objetivoIntermediarioVO.setCategoria(objetivoIntermediario.getCategoria());
        objetivoIntermediarioVO.setObjetivoFinal(objetivoIntermediario.getObjetivofinal());
        objetivoIntermediarioVO.setStatus(StatusObjetivoAlunoEnum.getEnumById(objetivoIntermediario.getStatus()));
        return objetivoIntermediarioVO;
    }

    @Override
    public ObjetivoAlunoVO criarObjetivo(String ctx, Integer matricula, ObjetivoAlunoDTO objetivoDTO) throws ValidacaoException {
        try {
            validarCamposObjetivo(objetivoDTO);

            ObjetivoAluno objetivo = new ObjetivoAluno();
            objetivo.setClienteSintetico(clienteService.consultarPorMatricula(ctx, matricula + ""));
            objetivo.setDescricao(objetivoDTO.getDescricao());
            objetivo.setPrimario(objetivoDTO.getPrimario());
            objetivo.setStatus(objetivoDTO.getStatus().getId());
            objetivo.setDatainicio(Uteis.getDate(objetivoDTO.getDataInicio(), "dd/MM/yyyy"));
            objetivo.setDatafinal(Uteis.getDate(objetivoDTO.getDataFinal(), "dd/MM/yyyy"));
            objetivo.setObjetivoPredefinido(objetivoPredefinidoDao.findById(ctx, objetivoDTO.getObjetivoPredefinido()));
            if (objetivo.getObjetivoPredefinido() == null) {
                throw new ValidacaoException("Não foi possível encontrar o objetivo predefinido.");
            } else if (objetivo.getClienteSintetico() == null) {
                throw new ValidacaoException("Não foi possível encontrar o aluno.");
            }

            return getObjetivoAlunoVO(ctx, objetivoAlunoDao.insert(ctx, objetivo));
        } catch (Exception ex) {
            Uteis.logar(ex, AgendamentoServiceImpl.class);
            throw new ValidacaoException(ex.getMessage());
        }
    }

    @Override
    public ObjetivoAlunoVO alterarObjetivo(String ctx, Integer id, ObjetivoAlunoDTO objetivoDTO) throws ValidacaoException {
        try {
            validarCamposObjetivo(objetivoDTO);

            ObjetivoAluno objetivo = objetivoAlunoDao.findById(ctx, id);
            objetivo.setDescricao(objetivoDTO.getDescricao());
            objetivo.setPrimario(objetivoDTO.getPrimario());
            objetivo.setStatus(objetivoDTO.getStatus().getId());
            objetivo.setDatainicio(Uteis.getDate(objetivoDTO.getDataInicio(), "dd/MM/yyyy"));
            objetivo.setDatafinal(Uteis.getDate(objetivoDTO.getDataFinal(), "dd/MM/yyyy"));
            objetivo.setObjetivoPredefinido(objetivoPredefinidoDao.findById(ctx, objetivoDTO.getObjetivoPredefinido()));
            if (objetivo.getObjetivoPredefinido() == null) {
                throw new ValidacaoException("Não foi possível encontrar o objetivo predefinido.");
            }

            objetivo = objetivoAlunoDao.update(ctx, objetivo);
            return getObjetivoAlunoVO(ctx, objetivo);
        } catch (Exception ex) {
            Uteis.logar(ex, AgendamentoServiceImpl.class);
            throw new ValidacaoException(ex.getMessage());
        }
    }

    @Override
    public void removerObjetivo(String ctx, Integer id) throws Exception {
        ObjetivoAluno objetivoAluno = objetivoAlunoDao.findById(ctx, id);
        List<ObjetivoIntermediarioAluno> objetivosIntermediarios = objetivoIntermediarioAlunoDao.findByObjetivoAluno(ctx, id);
        objetivoIntermediarioAlunoDao.deleteList(ctx, objetivosIntermediarios);
        objetivoAlunoDao.delete(ctx, objetivoAluno);
    }

    @Override
    public ObjetivoIntermediarioAlunoVO criarObjetivoIntermediario(String ctx, ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            validarCamposObjetivoIntermediarioAluno(objetivoIntermediarioDTO);

            ObjetivoIntermediarioAluno objetivoIntermediario = new ObjetivoIntermediarioAluno();
            ObjetivoAluno objetivoAluno = objetivoAlunoDao.findById(ctx, objetivoIntermediarioDTO.getObjetivoAluno());
            if (objetivoAluno == null) {
                throw new ValidacaoException("Objetivo principal/secundário não encontrado.");
            }

            objetivoIntermediario.setObjetivoaluno(objetivoAluno);
            objetivoIntermediario.setDescricao(objetivoIntermediarioDTO.getDescricao());
            objetivoIntermediario.setDatainicio(Uteis.getDate(objetivoIntermediarioDTO.getDataInicio(), "dd/MM/yyyy"));
            objetivoIntermediario.setDatafinal(Uteis.getDate(objetivoIntermediarioDTO.getDataFinal(), "dd/MM/yyyy"));
            ObjetivoPredefinido objetivoPredefinido = objetivoPredefinidoDao.findById(ctx, objetivoIntermediarioDTO.getObjetivoPredefinido());
            if (objetivoPredefinido == null) {
                throw new ValidacaoException("Objetivo predefinido não encontrado.");
            }

            objetivoIntermediario.setObjetivopredefinido(objetivoPredefinido);
            objetivoIntermediario.setCategoria(objetivoIntermediarioDTO.getCategoria());
            objetivoIntermediario.setStatus(objetivoIntermediarioDTO.getStatus().getId());

            objetivoIntermediario = objetivoIntermediarioAlunoDao.insert(ctx, objetivoIntermediario);
            return getObjetivoIntermediarioAlunoVO(objetivoIntermediario);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void validarCamposObjetivoIntermediarioAluno(ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) throws ValidacaoException {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        Date dataInicio = new Date();
        Date dataFinal = new Date();
        try {
            dataInicio = sdf.parse(objetivoIntermediarioDTO.getDataInicio());
            dataFinal = sdf.parse(objetivoIntermediarioDTO.getDataFinal());
        } catch (ParseException e) {
            throw new ValidacaoException("Data inválida.");
        }
        if(dataInicio.after(dataFinal)) {
            throw new ValidacaoException("Data de início não pode ser maior que a data final.");
        }
        try {
            StatusObjetivoAlunoEnum status = StatusObjetivoAlunoEnum.getEnumById(objetivoIntermediarioDTO.getStatus().getId());
        } catch (Exception e) {
            throw new ValidacaoException("Status inválido! Deve ser 1, 2 ou 3.");
        }
    }

    @Override
    public ObjetivoIntermediarioAlunoVO alterarObjetivoIntermediario(String ctx, Integer id, ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            validarCamposObjetivoIntermediarioAluno(objetivoIntermediarioDTO);

            ObjetivoIntermediarioAluno objetivoIntermediario = objetivoIntermediarioAlunoDao.findById(ctx, id);
            if (objetivoIntermediario == null) {
                throw new ValidacaoException("Objetivo intermediário não encontrado.");
            }

            objetivoIntermediario.setDescricao(objetivoIntermediarioDTO.getDescricao());
            objetivoIntermediario.setDatainicio(Uteis.getDate(objetivoIntermediarioDTO.getDataInicio(), "dd/MM/yyyy"));
            objetivoIntermediario.setDatafinal(Uteis.getDate(objetivoIntermediarioDTO.getDataFinal(), "dd/MM/yyyy"));
            ObjetivoPredefinido objetivoPredefinido = objetivoPredefinidoDao.findById(ctx, objetivoIntermediarioDTO.getObjetivoPredefinido());
            if (objetivoPredefinido == null) {
                throw new ValidacaoException("Objetivo predefinido não encontrado.");
            }

            objetivoIntermediario.setObjetivopredefinido(objetivoPredefinido);
            objetivoIntermediario.setCategoria(objetivoIntermediarioDTO.getCategoria());
            objetivoIntermediario.setStatus(objetivoIntermediarioDTO.getStatus().getId());

            objetivoIntermediario = objetivoIntermediarioAlunoDao.update(ctx, objetivoIntermediario);
            return getObjetivoIntermediarioAlunoVO(objetivoIntermediario);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ObjetivoAlunoVO objetivoPorCodigo(String ctx, Integer id) throws Exception {
        ObjetivoAluno objetivo = objetivoAlunoDao.findById(ctx, id);
        return getObjetivoAlunoVO(ctx, objetivo);
    }

    @Override
    public void removerObjetivoIntermediario(String ctx, Integer id) {
        try {
            ObjetivoIntermediarioAluno objetivoIntermediario = objetivoIntermediarioAlunoDao.findById(ctx, id);
            objetivoIntermediarioAlunoDao.delete(ctx, objetivoIntermediario);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ObjetivoIntermediarioAlunoVO objetivoIntermediario(String ctx, Integer id) {
        try {
            ObjetivoIntermediarioAluno objetivoIntermediario = objetivoIntermediarioAlunoDao.findById(ctx, id);
            return getObjetivoIntermediarioAlunoVO(objetivoIntermediario);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void validarCamposObjetivo(ObjetivoAlunoDTO objetivoDTO) throws ValidacaoException {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        Date dataInicio = new Date();
        Date dataFinal = new Date();
        try {
            dataInicio = sdf.parse(objetivoDTO.getDataInicio());
            dataFinal = sdf.parse(objetivoDTO.getDataFinal());
        } catch (ParseException ex) {
            throw new ValidacaoException("A data deve ser informada no formato dd/MM/yyyy.");
        }
        if (dataInicio.after(dataFinal)) {
            throw new ValidacaoException("A data de início não pode ser maior que a data final.");
        }
    }

    private static Map<String, Date> getHorarios(Date dia, String horaInicio, String horaFim) {
        Map<String, Date> ret = new HashMap<>();
        int hora = Integer.parseInt(horaInicio.substring(0,2));
        int minuto = Integer.parseInt(horaInicio.substring(3,5));
        Calendar dataInicial = Calendar.getInstance();
        dataInicial.setTime(Calendario.getDataComHoraZerada(dia));
        dataInicial.set(Calendar.HOUR, hora);
        dataInicial.set(Calendar.MINUTE, minuto);
        ret.put("inicio", dataInicial.getTime());

        hora = Integer.parseInt(horaFim.substring(0,2));
        minuto = Integer.parseInt(horaFim.substring(3,5));
        Calendar dataFinal = Calendar.getInstance();
        dataFinal.setTime(Calendario.getDataComHoraZerada(dia));
        dataFinal.set(Calendar.HOUR, hora);
        dataFinal.set(Calendar.MINUTE, minuto);
        ret.put("fim", dataFinal.getTime());

        return ret;
    }

    private static List<Integer> getDia(AgendamentoDTO agendamentoDTO){
        List<Integer> dia = new ArrayList<>();
        if(agendamentoDTO.getDom()){
            dia.add(1);
        }
        if(agendamentoDTO.getSeg()){
            dia.add(2);
        }
        if(agendamentoDTO.getTer()){
            dia.add(3);
        }
        if(agendamentoDTO.getQua()){
            dia.add(4);
        }
        if(agendamentoDTO.getQui()){
            dia.add(5);
        }
        if(agendamentoDTO.getSex()){
            dia.add(6);
        }
        if(agendamentoDTO.getSab()){
            dia.add(7);
        }
        return dia;
    }

    private boolean contemDiaDisponibilidade(List<HorarioDisponibilidade> lista, Integer dia){

            for (HorarioDisponibilidade horarioDisponibilidade : lista){
                System.out.println(horarioDisponibilidade.getDiaSemana());

                DiasSemana diaEnum = DiasSemana.getDiaSemana(horarioDisponibilidade.getDiaSemana());
                if(diaEnum.getNumeral() == dia){
                    return true;
                }
            }
        return false;

    }

    public static Map<String, List<?>> processarData(int diaSemana, int semanaMes, Date inicio, Date fim, int multiplicador, Date dataSelecionada){

        List<Date> datasEncontradas = new ArrayList<>();
        List<Date> listaMes = new ArrayList<>();

        LocalDate dataInicio = inicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate dataFim;
        try {
            dataFim =  fim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }catch (Exception e){
            dataFim = LocalDate.parse( new SimpleDateFormat("yyyy-MM-dd").format(fim) );
        }

        DayOfWeek diaDaSemana = DayOfWeek.of(diaSemana == 1 ? 7 : diaSemana - 1); // Ajusta para padrão (Domingo = 1)

        LocalDate dataAtual = dataInicio.withDayOfMonth(1); // Começa no primeiro dia do mês inicial

        Map<Date, Integer> listaDeDatasEDias  = new HashMap<Date, Integer>();

        while (!dataAtual.isAfter(dataFim)) {
            // Encontra o primeiro dia da semana desejado no mês
            LocalDate primeiraOcorrencia = dataAtual.with(TemporalAdjusters.firstInMonth(diaDaSemana));

//          LocalDate resultado = obterData(semanaMes, primeiraOcorrencia);
            LocalDate resultado = primeiraOcorrencia;
            if(resultado!= null && !resultado.isBefore(LocalDate.parse( new SimpleDateFormat("yyyy-MM-dd").format(dataSelecionada)))){
                Date date = Date.from(resultado.atStartOfDay(ZoneId.systemDefault()).toInstant());
                datasEncontradas.add(date);
                listaDeDatasEDias.put(date, diaSemana);
                dataAtual = dataAtual.plusMonths(multiplicador);
            }else{
                String mes = MesesEnum.getFromId(primeiraOcorrencia.getMonthValue()).name();
                listaMes.add(Date.from(primeiraOcorrencia.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                dataAtual = dataAtual.plusMonths(multiplicador);
            }
        }

        Map<String, List<?>> resultado = new HashMap<>();
        resultado.put("datasEncontradas", datasEncontradas);
        resultado.put("listaMes", listaMes);
        return resultado;

    }

    public static Map<String, List<?>> processarSemana(int diaSemana, int semanaMes, Date inicio, Date fim, int multiplicador, Date dataSelecionada){

        List<Date> datasEncontradas = new ArrayList<>();
        List<Date> listaMes = new ArrayList<>();

        LocalDate dataInicio = inicio.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate dataFim;
        try {
            dataFim =  fim.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }catch (Exception e){
            dataFim = LocalDate.parse( new SimpleDateFormat("yyyy-MM-dd").format(fim) );
        }

        DayOfWeek diaDaSemana = DayOfWeek.of(diaSemana == 1 ? 7 : diaSemana - 1); // Ajusta para padrão (Domingo = 1)

        LocalDate dataAtual = dataInicio.withDayOfMonth(1); // Começa no primeiro dia do mês inicial

        LocalDate primeiraOcorrencia = dataAtual.with(TemporalAdjusters.firstInMonth(diaDaSemana));

//      LocalDate resultadoData = obterData(semanaMes, primeiraOcorrencia);
        LocalDate resultadoData = primeiraOcorrencia;


        Map<Date, Integer> listaDeDatasEDias  = new HashMap<Date, Integer>();
        int primeiroResultado = 1;
        while (!resultadoData.isAfter(dataFim)) {
            if(resultadoData!= null && !resultadoData.isBefore(LocalDate.parse( new SimpleDateFormat("yyyy-MM-dd").format(dataSelecionada)))){
                Date date = Date.from(resultadoData.atStartOfDay(ZoneId.systemDefault()).toInstant());
                datasEncontradas.add(date);
                listaDeDatasEDias.put(date, diaSemana);
                resultadoData = resultadoData.plusWeeks(multiplicador);
            }else{
                String mes = MesesEnum.getFromId(primeiraOcorrencia.getMonthValue()).name();
                listaMes.add(Date.from(primeiraOcorrencia.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                resultadoData = resultadoData.plusWeeks(multiplicador);
            }
        }

        Map<String, List<?>> resultado = new HashMap<>();
        resultado.put("datasEncontradas", datasEncontradas);
        resultado.put("listaMes", listaMes);
        return resultado;

    }

    public static LocalDate obterData(int semanaDoMes, LocalDate primeiraOcorrencia) {

        LocalDate primeiroDiaDoMes = primeiraOcorrencia.withDayOfMonth(1);

        if (primeiraOcorrencia.isBefore(primeiroDiaDoMes)) {
            primeiraOcorrencia = primeiraOcorrencia.plusWeeks(1); // Ajusta se necessário
        }

        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int semanaPrimeiroData =  primeiraOcorrencia.get(weekFields.weekOfMonth());

        LocalDate semanaRecorente = primeiraOcorrencia.plusWeeks(semanaDoMes-semanaPrimeiroData);
        if(semanaRecorente.getMonthValue() != primeiraOcorrencia.getMonthValue()){
            return null;
        }
        return semanaRecorente;
    }

}
