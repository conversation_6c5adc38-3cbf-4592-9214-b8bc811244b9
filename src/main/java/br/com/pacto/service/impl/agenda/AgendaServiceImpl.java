package br.com.pacto.service.impl.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.ConfigAgenda;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aula.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.OrigemCliente;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.BookingGymPass;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.HorarioGymPass;
import br.com.pacto.bean.gympass.TipoThreadGympassEnum;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.*;
import br.com.pacto.controller.json.aluno.*;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import br.com.pacto.controller.json.aulaDia.AulaAlunoJSON;
import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import br.com.pacto.controller.json.aulaDia.MapaEquipamentoAparelhoDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeSimplesDTO;
import br.com.pacto.controller.json.selfloops.CourseDTO;
import br.com.pacto.controller.json.selfloops.CourseSensorDTO;
import br.com.pacto.controller.json.selfloops.UserDTO;
import br.com.pacto.controller.json.selfloops.UserDetailsDTO;
import br.com.pacto.dao.impl.turma.TurmaDaoImpl;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.agenda.ConfigAgendaDao;
import br.com.pacto.dao.intf.aula.*;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.locacao.AgendamentoLocacaoDao;
import br.com.pacto.dao.intf.nivel.NivelDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.notificacao.EnfileiradorNotificadorRecursoSistemaSingleton;
import br.com.pacto.notificacao.RecursoSistema;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.agendatotal.AgendaTotalServiceImpl;
import br.com.pacto.service.impl.agendatotal.AlunosAulasServiceImpl;
import br.com.pacto.service.impl.aula.AulaServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.configuracoes.ConfiguracaoSistemaServiceImpl;
import br.com.pacto.service.impl.gympass.IntegracaoGymPassBooking;
import br.com.pacto.service.impl.gympass.dto.BookingsDTO;
import br.com.pacto.service.impl.gympass.dto.ClassesDTO;
import br.com.pacto.service.impl.gympass.dto.SlotsDTO;
import br.com.pacto.service.impl.gympass.json.GymPassBookingZWJSON;
import br.com.pacto.service.impl.gympass.json.HorarioTurmaGymPassJSON;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.*;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import br.com.pacto.service.intf.notificacao.NotificacaoAulaAgendadaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.telegram.TelegramService;
import br.com.pacto.threads.ThreadGympass;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class AgendaServiceImpl implements AgendaService {

    @Autowired
    private AulaDiaExclusaoDao aulaDiaExclusaoDao;
    @Autowired
    private AulaAlunoDao aulaAlunoDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AgendaTotalService agendaTotalService;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private AulaHorarioDao aulaHorarioDao;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private AulaDao aulaDao;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConfigAgendaDao configAgendaDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private ProfessorSubstituidoDao professorSubstituidoDao;
    @Autowired
    private ConfigGymPassService configGymPassService;
    @Autowired
    private GymPassBookingService gymPassBookingService;
    @Autowired
    private HorarioGymPassService horarioGymPassService;
    @Autowired
    private BookingGymPassService bookingGymPassService;
    @Autowired
    private CachedManagerInterfaceFacade memcachedService;
    @Autowired
    private LogGymPassService logGymPassService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private AgendamentoLocacaoDao agendamentoLocacaoDao;
    @Autowired
    private AulaServiceImpl aulaServiceImpl;
    @Autowired
    private HorarioDisponibilidadeDao horarioDisponibilidadeDao;
    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;
    @Autowired
    private NotificacaoAulaAgendadaService notificacaoAulaAgendadaService;
    @Autowired
    private AparelhoService aparelhoService;
    public static final String UNIQUE_VIOLATION_SQLSTATE = "23505";
    @Autowired
    private TurmaDaoImpl turmaDaoImpl;
    @Autowired
    private NivelDao nivelDao;


    public Map<Integer, List<Date>> aulasExcluidas(String ctx, Date inicio, Date fim) throws Exception {
        Map<Integer, List<Date>> excluidas;
        try (ResultSet rs = aulaDiaExclusaoDao.createStatement(ctx,
                "select dataauladia, codigohorarioturma from auladiaexclusao " +
                        "where dataauladia between '"
                        + Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(inicio), "yyyy-MM-dd") +
                        "' and '" +
                        Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(fim, "23:59:59"), "yyyy-MM-dd") + "'")) {
            excluidas = new HashMap<Integer, List<Date>>();
            while (rs.next()) {
                List<Date> dias = excluidas.get(rs.getInt("codigohorarioturma"));
                if (dias == null) {
                    dias = new ArrayList<Date>();
                    excluidas.put(rs.getInt("codigohorarioturma"), dias);
                }
                dias.add(Calendario.getDataComHoraZerada(rs.getDate("dataauladia")));
            }
        }
        return excluidas;
    }

    public Map<String, ProfessorSubstituido> professoresSubstituidos(String ctx, Date inicio, Date fim) throws Exception {
        Map<String, ProfessorSubstituido> substituicoes;
        try (ResultSet rs = aulaDiaExclusaoDao.createStatement(ctx,
                "select codigohorarioturma, diaaula, codigoprofessorsubstituto, justificativa from professorsubstituido " +
                        "where diaaula between '"
                        + Uteis.getDataAplicandoFormatacao(Calendario.getDataComHoraZerada(inicio), "yyyy-MM-dd") +
                        "' and '" +
                        Uteis.getDataAplicandoFormatacao(Calendario.getDataComHora(fim, "23:59:59"), "yyyy-MM-dd") + "'")) {
            substituicoes = new HashMap<String, ProfessorSubstituido>();
            while (rs.next()) {
                ProfessorSubstituido profSub = new ProfessorSubstituido();
                profSub.setCodigoProfessorSubstituto(rs.getInt("codigoprofessorsubstituto"));
                profSub.setJustificativa(rs.getString("justificativa"));
                substituicoes.put(Uteis.getData(rs.getDate("diaaula")) + "_" + rs.getInt("codigohorarioturma"), profSub);
            }
        }
        return substituicoes;
    }

    public List<EventoAulaDTO> agendaDia(String ctx, Date dia) throws Exception {
        return agendaDia(dia, ctx);
    }

    public List<EventoAulaDTO> agendaDia(Date dia, String ctx) throws Exception {
        List<EventoAulaDTO> aulasDTO = new ArrayList<EventoAulaDTO>();
        int dayOfWeek = Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK);
        DiasSemana diasSemana = DiasSemana.getDiaSemanaNumeral(dayOfWeek);
        List<Aula> aulas = aulasDiaSemana(ctx, diasSemana.getCodigo());
//        aulaDao.refresh(ctx, aulas);
        Map<Integer, List<Date>> aulasExcluidas = aulasExcluidas(ctx, dia, dia);
        Map<String, ProfessorSubstituido> professoresSubstituidos = professoresSubstituidos(ctx, dia, dia);
        for (Aula a : aulas) {
            if (Calendario.entre(dia, Calendario.anterior(Calendar.DAY_OF_MONTH, a.getDataInicio()), Calendario.proximo(Calendar.DAY_OF_MONTH, a.getDataFim()))) {
                for (AulaHorario ah : a.getHorarios()) {
                    List<Date> excluidas = aulasExcluidas.get(ah.getCodigo());
                    if (excluidas == null || !excluidas.contains(Calendario.getDataComHoraZerada(dia))) {
                        List<AlunoResponseTO> alunoResponseTOS = alunosHorarioDia(ctx, ah, dia);
                        EventoAulaDTO evento;
                        ProfessorSubstituido professorSubstituido = professoresSubstituidos.get(Uteis.getData(dia) + "_" + ah.getCodigo());
                        if (ah.getAtivo()) {
                            if (professorSubstituido == null) {
                                evento = new EventoAulaDTO(ah, dia, a.getProfessor(), null, alunoResponseTOS, SuperControle.independente(ctx));
                            } else {
                                ProfessorSintetico professorSintetico = professorService.obterPorId(ctx, professorSubstituido.getCodigoProfessorSubstituto());
                                evento = new EventoAulaDTO(ah, dia, professorSintetico, professorSubstituido.getJustificativa(), alunoResponseTOS, SuperControle.independente(ctx));
                            }
                            aulasDTO.add(evento);
                        }
                    }
                }
            }

        }

        return aulasDTO;
    }

    private List<EventoAulaDTO> agendaDiaZW(String ctx, Date inicio, Date fim, Integer empresaId) throws Exception {

        Map<Integer, List<Date>> mapaAulasExcluidas = agendaTotalService.obterAulasExcluidas(ctx, inicio, fim);
        Map<Integer, String> mapaProfessores = agendaTotalService.obterMapaProfessores(ctx, empresaId, Boolean.FALSE, Boolean.FALSE);
        Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = agendaTotalService.obterProfessoresSubstituidos(ctx,
                null, inicio, fim);

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<AgendaTotalTO> agendamentos = integracaoWS.consultarAgendamentos( ctx, inicio, fim, empresaId, null);
        List<EventoAulaDTO> eventoAulas = new ArrayList<>();

        for (AgendaTotalTO agenda : agendamentos) {
            EventoAulaDTO eventoAula = new EventoAulaDTO();
            if (agenda.getAulaColetiva()) {
                List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
                if (datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate()))) {
                    continue;
                }
                Map<Date, ProfessorSubstituido> mapaSubstituidos = mapaProfessoresSubstituidos.get(Integer.valueOf(agenda.getId()));
                if (mapaSubstituidos != null) {
                    ProfessorSubstituido subs = mapaSubstituidos.get(Calendario.getDataComHoraZerada(agenda.getStartDate()));
                    if (subs != null) {
                        subs.setNomeProfessorOrigem(agenda.getResponsavel());
                        agenda.setResponsavel(mapaProfessores.get(subs.getCodigoProfessorSubstituto()));
                        agenda.setCodigoResponsavel(subs.getCodigoProfessorSubstituto());
                        agenda.setSubstituiuProfessor(true);
                        agenda.setSubstituido(subs);
                    }
                }
            }
            eventoAula.setId(Integer.parseInt(agenda.getId()));
            AulaResponseDTO aula = new AulaResponseDTO();
            aula.setNome(agenda.getTitulo());
            aula.setOcupacao(String.valueOf(agenda.getOcupacao()));
            aula.setCapacidade(agenda.getNrVagas());
            AmbienteResponseTO ambiente = new AmbienteResponseTO(((AmbienteService) UtilContext.getBean(AmbienteService.class)).consultarPorAmbienteZW(ctx, agenda.getCodigoLocal()), false, false);
            aula.setAmbiente(ambiente);
            ModalidadeResponseTO modalidade = ((ModalidadeService) UtilContext.getBean(ModalidadeService.class)).detalhesModalidade(agenda.getCodigotipo());
            aula.setModalidade(modalidade);
            ProfessorSubstitutoDTO professor = new ProfessorSubstitutoDTO();
            professor.setId(agenda.getCodigoResponsavel());
            professor.setNome(agenda.getResponsavel());
            professor.setImageUri(agenda.getFotoProfessor());
            eventoAula.setProfessorSubstituto(professor);
            JSONObject horarioTurma = new JSONObject(integracaoWS.pesquisarHorarioTurma( ctx, Integer.valueOf(agenda.getId())));
            eventoAula.setHorarioInicio(horarioTurma.optString("horainicial"));
            eventoAula.setHorarioFim(horarioTurma.optString("horafinal"));
            eventoAula.setDia(Calendario.getDate("dd/MM/yyyy", agenda.getDia()).getTime());
            ProfessorResponseTO professorResponsavel = new ProfessorResponseTO();
            ProfessorSintetico prof = professorService.consultarPorCodigoColaborador(ctx, horarioTurma.optInt("professorCodigo"));
            if (prof != null) {
                professorResponsavel.setId(prof.getCodigoColaborador());
                professorResponsavel.setNome(prof.getNome());
                professorResponsavel.setImageUri(prof.getUriImagem());
            }
            aula.setProfessor(professorResponsavel);
            eventoAula.setAula(aula);
            ResultAlunoClienteSinteticoJSON resultAlunos = integracaoWS.obterAlunosDeUmaAula( ctx, Integer.valueOf(agenda.getId()), Calendario.getDate("dd/MM/yyyy", agenda.getDia()));
            List<AlunoResponseTO> alunos = new ArrayList<>();
            for (AgendadoJSON agendadoJSON : resultAlunos.getResultAgendado()) {
                AlunoResponseTO alunoMatriculado = new AlunoResponseTO();
                alunoMatriculado.setId(agendadoJSON.getCodigoCliente());
                alunoMatriculado.setNome(agendadoJSON.getNome());
                alunoMatriculado.setConfirmado(agendadoJSON.getConfirmado());
                alunos.add(alunoMatriculado);
            }
            eventoAula.setAlunos(alunos.toArray(new AlunoResponseTO[alunos.size()]));


            eventoAulas.add(eventoAula);
        }
        return eventoAulas;
    }

    public EventoAulaDTO aulaHorarioEvento(Date dia, Integer codigoHorario, String ctx) throws Exception {
        EventoAulaDTO aula = null;
        int dayOfWeek = Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK);
        DiasSemana diasSemana = DiasSemana.getDiaSemanaNumeral(dayOfWeek);
        List<Aula> aulas = aulasDiaSemana(ctx, diasSemana.getCodigo());
        aulaDao.refresh(ctx, aulas);

        Map<Integer, List<Date>> aulasExcluidas = aulasExcluidas(ctx, dia, dia);
        Map<String, ProfessorSubstituido> professoresSubstituidos = professoresSubstituidos(ctx, dia, dia);

        AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, codigoHorario);

        if (aulaHorario == null) {
            throw new Exception("Não foi possível encontrar a aula ");
        }

        List<Date> excluidas = aulasExcluidas.get(aulaHorario.getCodigo());
        if (excluidas == null || !excluidas.contains(Calendario.getDataComHoraZerada(dia))) {
            List<AlunoResponseTO> alunoResponseTOS = alunosHorarioDia(ctx, aulaHorario, dia);

            ProfessorSubstituido professorSubstituido = professoresSubstituidos.get(Uteis.getData(dia) + "_" + aulaHorario.getCodigo());
            if (professorSubstituido == null) {
                aula = new EventoAulaDTO(aulaHorario, dia, aulaHorario.getAula().getProfessor(), null, alunoResponseTOS, SuperControle.independente(ctx));
            } else {
                ProfessorSintetico professorSintetico = professorService.obterPorId(ctx, professorSubstituido.getCodigoProfessorSubstituto());
                aula = new EventoAulaDTO(aulaHorario, dia, professorSintetico, professorSubstituido.getJustificativa(), alunoResponseTOS, SuperControle.independente(ctx));
            }
        }

        return aula;
    }

    private void alterarSituacaoAlunoAula(Integer aula, Integer aluno, Date dia, Boolean confirmado) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String hql = "select obj from AulaAluno obj where obj.dia between :inicio and :fim and obj.horario.codigo = :horario and obj.cliente.codigo = :aluno";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("aluno", aluno);
            params.put("horario", aula);
            params.put("inicio", Calendario.getDataComHoraZerada(dia));
            params.put("fim", Calendario.getDataComHora(dia, "23:59"));

            AulaAluno alunoAula = aulaAlunoDao.findObjectByParam(ctx, hql, params);
            alunoAula.setPresencaConfirmada(confirmado);
            aulaAlunoDao.update(ctx, alunoAula);

        } catch (Exception e) {
            throw new ServiceException(e);
        }

    }

    @Override
    public TurmaResponseDTO alterarSituacaoAlunoAula(Integer empresaId, Integer aulaHorarioId, Date dia, Integer matriculaAluno, Integer codigoPassivo, Integer codigoIndicado,
                                                     AlunoVinculoAulaEnum alunoVinculoAula, Boolean desmarcar, HttpServletRequest request,
                                                     OrigemSistemaEnum origem, Boolean autorizadoGestaoRede, String codAcessoAutorizado, Integer matriculaAutorizado) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Boolean reposicao = false;

            if (!desmarcar && configuracaoSistemaService.verificaPermiteRedirecionarAulaExperimentalCRMAGENDA(ctx) &&
                    (!UteisValidacao.emptyNumber(codigoPassivo) || !UteisValidacao.emptyNumber(codigoIndicado))) {
                try {
                    JSONObject passivo = clienteService.consultarPassivo(ctx, codigoPassivo);
                    if (passivo != null && passivo.has("codigo")) {
                        JSONObject retorno = chamadaZW(ctx, "/prest/negociacao?operacao=cadastrarClienteVendasOnline&ignoraCpf=true", empresaId, passivo);
                        if (retorno.has("sucesso") && retorno.getString("sucesso").equalsIgnoreCase("ALUNO CADASTRADO COM SUCESSO")) {
                            matriculaAluno = aulaService.atualizarAgendamentoRemovendoPassivo(ctx, aulaHorarioId, dia, passivo.getString("nome"), codigoPassivo);
                        } else {
                            throw new Exception((retorno.has("sucesso") ? retorno.getString("sucesso") : "Erro ao confirmar aula do Lead"));
                        }
                    }else{
                        JSONObject indicado = clienteService.consultarIndicado(ctx, codigoIndicado);
                        if (indicado != null && indicado.has("codigo")) {
                            JSONObject retorno = chamadaZW(ctx, "/prest/negociacao?operacao=cadastrarClienteVendasOnline&ignoraCpf="+(indicado.has("cpf") && UteisValidacao.emptyString(indicado.getString("cpf"))),
                                    empresaId, indicado);
                            if (retorno.has("sucesso") && retorno.getString("sucesso").startsWith("ALUNO CADASTRADO COM SUCESSO")) {
                                matriculaAluno = aulaService.atualizarAgendamentoRemovendoIndicado(ctx, aulaHorarioId, dia, indicado.getString("nome"), codigoIndicado);
                            } else {
                                throw new Exception((retorno.has("sucesso") ? retorno.getString("sucesso") : "Erro ao confirmar aula do Lead"));
                            }
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    throw new Exception(ex.getMessage());
                }
            }

            if (configuracaoSistemaService.verificaPermiteRedirecionarAulaExperimentalCRMAGENDA(ctx) && !UteisValidacao.emptyNumber(matriculaAluno)) {
                marcarCancelarComparecimentoMetaPresenciaisCRM(dia, matriculaAluno, ctx, !desmarcar, origem);
            }

            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, String.valueOf(matriculaAluno));
            TurmaResponseDTO turmaResponse = new TurmaResponseDTO();
            if (cliente == null) {
                cliente = this.aulaService.addAlunoAutomaticamente(ctx, String.valueOf(matriculaAluno));
                if(cliente == null){
                    throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS);
                }

            }
            if (SuperControle.independente(ctx)) {
                alterarSituacaoAlunoAula(aulaHorarioId, cliente.getCodigo(), dia, !desmarcar);
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);
                turmaResponse = aulaToTurmaResponse(
                        ctx,
                        request,
                        aulaHorario.getAula(),
                        aulaHorario,
                        dia
                );
            } else {
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aula = integracaoWS.consultarUmaAula( ctx, Calendario.getData(dia, "dd/MM/yyyy"), aulaHorarioId);
                if (aula != null) {
                    if(AlunoVinculoAulaEnum.INTEGRACAO.equals(alunoVinculoAula)){
                        JSONObject dados = new JSONObject();
                        dados.put("horarioId", aulaHorarioId);
                        dados.put("autorizado", matriculaAluno);
                        dados.put("usuario", usuario.getProfessor().getCodigoColaborador());
                        dados.put("dia", Uteis.getData(dia));
                        dados.put("desmarcar", desmarcar);
                        dados.put("origem", origem.getCodigo());

                        dados.put("autorizadoGestaoRede", autorizadoGestaoRede);
                        dados.put("codAcessoAutorizado", codAcessoAutorizado);
                        dados.put("matriculaAutorizado", matriculaAutorizado);

                        JSONObject retorno = chamadaZW(ctx, "/prest/confirmar-presenca/autorizado", empresaId, null, null, dados);
                        if (!retorno.get("content").equals("sucesso")) {
                            throw new ServiceException(AgendaExcecoes.ERRO_CONFIRMAR_ALUNO_AULA);
                        }
                    } else if (aula.getAulaCheia()) {
                        if (!desmarcar) {
                            String marcarPresenca = agendaTotalService.presencaAlunoAula(
                                    ctx,
                                    cliente.getCodigoCliente(),
                                    aulaHorarioId,
                                    Calendario.getData(dia, "dd/MM/yyyy"),
                                    usuario.getProfessor().getCodigoColaborador(),
                                    origem,
                                    true);
                            if (!marcarPresenca.equals("OK")) {
                                throw new ServiceException(marcarPresenca.replace("ERRO:", ""));
                            }
                        } else {
                            String desmarcarPresenca = agendaTotalService.presencaAlunoAula(
                                    ctx,
                                    cliente.getCodigoCliente(),
                                    aulaHorarioId,
                                    Calendario.getData(dia, "dd/MM/yyyy"),
                                    usuario.getProfessor().getCodigoColaborador(),
                                    origem,
                                    false);
                            if (!desmarcarPresenca.equals("OK")) {
                                throw new ServiceException(AgendaExcecoes.ERRO_DESCONFIRMAR_ALUNO_AULA);
                            }
                        }
                    } else {
                        List<AgendadoTO> alunosReposicao = integracaoWS.consultarReposicoes( ctx, Calendario.getDate("dd/MM/yyyy HH:mm", aula.getInicio()),
                                Calendario.getDate("dd/MM/yyyy HH:mm", aula.getFim()), empresaId);
                        AgendadoTO alunoReposicao = new AgendadoTO();
                        for (AgendadoTO aluno : alunosReposicao) {
                            if (aluno.getCodigoCliente().equals(cliente.getCodigoCliente())
                                    && aluno.getIdAgendamento().equals(aula.getId())) {
                                alunoReposicao = aluno;
                                break;
                            }
                        }

                        List<AgendadoJSON> alunosTurma = integracaoWS.obterAlunosDeUmaTurma( ctx, null, aulaHorarioId, dia);
                        final Integer codigoCliente = cliente.getCodigoCliente();
                        AgendadoJSON alunoTurma = alunosTurma.stream().filter(alunoAula -> alunoAula.getCodigoCliente().equals(codigoCliente)).findAny().get();
                        if (desmarcar) {
                            reposicao = !UteisValidacao.emptyNumber(alunoReposicao.getCodigoCliente());
                        } else {
                            reposicao = !UteisValidacao.emptyNumber(alunoReposicao.getCodigoCliente()) || alunoTurma.isDiaria();
                        }
                        String atualizarPresenca = agendaTotalService.gravarPresenca(
                                ctx,
                                cliente.getCodigoCliente(),
                                Calendario.getDate("dd/MM/yyyy HH:mm", aula.getInicio()),
                                aulaHorarioId,
                                reposicao,
                                desmarcar,
                                usuario.getProfessor().getCodigoColaborador(),
                                origem);
                        if (!atualizarPresenca.equals("sucesso")) {
                            throw new ServiceException(atualizarPresenca.replace("ERRO:", ""));
                        }
                    }
                    turmaResponse = turmaUpdated(ctx, empresaId, true, new AgendaTotalTO(aula), dia.getTime(), request);
                } else {
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
            }
            return turmaResponse;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ATUALIZAR_ALUNO_AULA, e);
        }
    }

    private void marcarCancelarComparecimentoMetaPresenciaisCRM(Date dia, Integer matriculaAluno, String ctx, boolean marcar, OrigemSistemaEnum origem) throws Exception {
        if (OrigemSistemaEnum.AULA_CHEIA.equals(origem)) {
            Integer fecharMetaDetalhado = clienteService.consultarMetaAgendamentoPresencialAluno(ctx, matriculaAluno, dia);
            if (!UteisValidacao.emptyNumber(fecharMetaDetalhado)) {
                JSONObject retorno = chamadaZW(null, "/prest/aulacheia/marcar-cancelar-comparecimento-meta-presenciais-crm?chave=" + ctx +
                        "&marcar=" + marcar + "&fecharMetaDetalhado=" + fecharMetaDetalhado + "&codigoUsuario=" + sessaoService.getUsuarioAtual().getId(), 0, new JSONObject());
                new JSONObject(retorno).has("content");
            }
        }
    }

    @Override
    public TurmaResponseDTO confirmarTodasPresencas(Integer empresaId, Integer aulaHorarioId, Date dia, HttpServletRequest request, OrigemSistemaEnum origem) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            TurmaResponseDTO turmaResponse = new TurmaResponseDTO();
            if (SuperControle.independente(ctx)) {
                String retorno = confirmarTodasPresencasTreinoIndependente(ctx, aulaHorarioId, dia);
                if (retorno.equals("sucesso")) {
                    AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);
                    turmaResponse = aulaToTurmaResponse(
                            ctx,
                            request,
                            aulaHorario.getAula(),
                            aulaHorario,
                            dia
                    );
                } if (retorno.equals("todos_alunos_ja_confirmados")) {
                    throw new ServiceException("Todos alunos já estão confirmados.");
                } else if (!retorno.equals("sucesso")) {
                    throw new ServiceException("ERRO:", "Ocorreu algum problema ao confirmar as presenças.");
                }
            } else {
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aula = integracaoWS.consultarUmaAula( ctx, Calendario.getData(dia, "dd/MM/yyyy"), aulaHorarioId);
                if (aula != null) {
                    JSONObject dados = new JSONObject();
                    dados.put("horarioId", aulaHorarioId);
                    dados.put("isAulaCheia", aula.getAulaCheia());
                    dados.put("usuario", usuario.getProfessor().getCodigoColaborador());
                    dados.put("dia", Uteis.getData(dia));
                    dados.put("origem", origem.getCodigo());
                    JSONObject retorno = chamadaZW(ctx, "/prest/confirmar-presenca/confirmar-todos", empresaId, null, null, dados);
                    if (retorno.get("content").equals("Todos alunos já estão confirmados.")) {
                        throw new ServiceException("Todos alunos já estão confirmados.");
                    } else if (!retorno.get("content").equals("sucesso") && !retorno.get("content").equals("Todos alunos já estão confirmados.")) {
                        throw new ServiceException(retorno.get("content").toString().replace("ERRO:", ""));
                    }
                    turmaResponse = turmaUpdated(ctx, empresaId, true, new AgendaTotalTO(aula), dia.getTime(), request);
                } else {
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
            }
            return turmaResponse;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ATUALIZAR_ALUNO_AULA, e);
        }
    }

    private String confirmarTodasPresencasTreinoIndependente(String ctx, Integer aulaHorarioId, Date dia) throws Exception {
        boolean algumaAlteracao = false;
        String hql = "SELECT obj FROM AulaAluno obj \n" +
                     "WHERE obj.dia BETWEEN :inicio AND :fim \n" +
                     "AND obj.horario.codigo = :horario \n" +
                     "AND (obj.presencaConfirmada IS FALSE OR obj.presencaConfirmada IS NULL)";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("horario", aulaHorarioId);
        params.put("inicio", Calendario.getDataComHoraZerada(dia));
        params.put("fim", Calendario.getDataComHora(dia, "23:59"));
        List<AulaAluno> alunosNaoConfirmados = aulaAlunoDao.findByParam(ctx, hql, params);
        for (AulaAluno aluno : alunosNaoConfirmados) {
            alterarSituacaoAlunoAula(aulaHorarioId, aluno.getCliente().getCodigo(), dia, true);
            algumaAlteracao = true;
        }
        return algumaAlteracao ? "sucesso" : "todos_alunos_ja_confirmados";
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 Long inicio,
                                 Long fim,
                                 JSONObject dados) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(dados != null){
            params.add(new BasicNameValuePair("dados", dados.toString()));
        }
        if(inicio != null){
            params.add(new BasicNameValuePair("inicio", inicio.toString()));
        }
        if(fim != null){
            params.add(new BasicNameValuePair("fim", fim.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 JSONObject dados) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebIntegProp);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();

        if(!UteisValidacao.emptyString(ctx)){
            endpoint = endpoint + "&key=" + ctx;
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            endpoint = endpoint + "&empresa=" + empresa;
        }

        HttpPost httpPost = new HttpPost(url + endpoint);

        StringEntity entity = new StringEntity(dados.toString(), "UTF-8");
        httpPost.setEntity(entity);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    @Override
    public AdicionarAlunoTurmaResponseDTO marcarAlunoHorarioConversasIA(String ctx,
                                                                       Usuario usuario,
                                                                       Integer empresaZw,
                                                                       Integer codigoHorarioTurma,
                                                                       Integer matriculaAluno,
                                                                       Date dia,
                                                                       AcaoAulaTurmaEnum acao,
                                                                       String origem,
                                                                       HttpServletRequest request) throws ServiceException {
        // ESTE FLUXO É EXCLUSIVO PARA O CONVERSA AI ***********
        try {
            if (UteisValidacao.emptyString(ctx)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            }

            if (SuperControle.independente(ctx)) {
                throw new ServiceException("Esta operação não é permitida para treino independente");
            }

            if (!acao.equals(AcaoAulaTurmaEnum.AULA_EXPERIMENTAL)) {
                throw new ServiceException("Esta operação aceita apenas agendamentos experimentais");
            }

            if (!"CONVERSAS_IA".equalsIgnoreCase(origem)) {
                throw new ServiceException("Esta operação é permitida apenas para o CONVERSAS_IA");
            }

            AdicionarAlunoTurmaResponseDTO ret = new AdicionarAlunoTurmaResponseDTO();
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matriculaAluno.toString());
            String situacaoClienteZW = clienteService.obterSituacaoClienteZW(ctx, Integer.valueOf(matriculaAluno.toString()));

            if (!"VI".equalsIgnoreCase(situacaoClienteZW)) {
                throw new ServiceException("Operação permitada apenas para alunos visitantes");
            }

            adicionarOrigemSistema(ctx, origem, cliente);
            Integer codNivelAluno = cliente.getNivelAluno() != null ? cliente.getNivelAluno().getCodigo() : null;
            ret = adicionarAlunoTurmaZWConversasIA(ctx, usuario, empresaZw, codigoHorarioTurma, matriculaAluno, dia, acao, codNivelAluno, request);

            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() != null) {
                throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e.getCause());
            }
            throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e);
        }
    }


    @Override
    public AdicionarAlunoTurmaResponseDTO adicionarAlunoTurma(String ctx, Usuario usuario,
                                                              Integer empresaId, Integer aulaHorarioId, Integer matriculaAluno,
                                                              Date dia, Integer produtoId, Integer aulaDesmarcarId,
                                                              Date diaAulaDesmarcar, AcaoAulaTurmaEnum acao,
                                                              Boolean autorizado, String origem,
                                                              HttpServletRequest request) throws ServiceException {
        return adicionarAlunoTurma(ctx, usuario,
                empresaId, aulaHorarioId, matriculaAluno,
                dia, produtoId, aulaDesmarcarId,
                diaAulaDesmarcar, acao,
                null,
                autorizado,
                origem,
                request);
    }
    @Override
    public AdicionarAlunoTurmaResponseDTO adicionarAlunoTurma(String ctx, Usuario usuario,
                                                              Integer empresaId, Integer aulaHorarioId, Integer matriculaAluno,
                                                              Date dia, Integer produtoId, Integer aulaDesmarcarId,
                                                              Date diaAulaDesmarcar, AcaoAulaTurmaEnum acao,
                                                              String bookingId,
                                                              Boolean autorizado,
                                                              String origem,
                                                              HttpServletRequest request) throws ServiceException {
        try {
            if (UteisValidacao.emptyString(ctx)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            }
            AdicionarAlunoTurmaResponseDTO ret = new AdicionarAlunoTurmaResponseDTO();
            if (SuperControle.independente(ctx)) {
                ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matriculaAluno.toString());
                adicionarOrigemSistema(ctx, origem, cliente);
                AulaAlunoJSON aulaAlunoJSON = adicionarAluno(cliente.getCodigo(), dia, aulaHorarioId);
                if (aulaAlunoJSON != null) {
                    ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                    Map<String, Object> conteudo = new HashMap<>();
                    AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);

                    conteudo.put("turma", aulaToTurmaResponse(
                            ctx,
                            request,
                            aulaHorario.getAula(),
                            aulaHorario,
                            dia
                    ));
                    ret.setConteudo(conteudo);
                } else {
                    throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO);
                }
            } else {
                ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matriculaAluno.toString());
                adicionarOrigemSistema(ctx, origem, cliente);
                ret = adicionarAlunoTurmaZW(ctx, usuario, empresaId, aulaHorarioId, matriculaAluno, dia, produtoId, aulaDesmarcarId, diaAulaDesmarcar,
                        acao,
                        bookingId,
                        autorizado,
                        request);
            }
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            if(e.getCause() != null){
                throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e.getCause());
            }
            throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e);
        }
    }

    private void adicionarOrigemSistema(String ctx, String origem, ClienteSintetico cliente) throws ServiceException {
        if(Objects.nonNull(origem)){
            cliente.setOrigemCliente(OrigemCliente.fromValue(origem));
            clienteService.alterar(ctx, cliente);
        }
    }

    private void validarClientePossuiNivelAulaColetiva(String ctx, Integer codigoHorarioTurma, Integer codigoCliente, Integer codigoNivelAluno) throws ServiceException {
        if (UteisValidacao.emptyNumber(codigoNivelAluno)) {
            codigoNivelAluno = 0;
        }
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT t.niveis \n");
            sql.append("FROM turma t \n");
            sql.append("INNER JOIN horarioturma h ON h.turma = t.codigo \n");
            sql.append("WHERE h.codigo = ").append(codigoHorarioTurma);

            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                while (rs.next()) {
                    String niveis = rs.getString("niveis");
                    if (!UteisValidacao.emptyString(niveis)) {
                        String[] listaNiveis = niveis.split(",");
                        boolean nivelValido = false;
                        for (String nivel : listaNiveis) {
                            if (Integer.parseInt(nivel) == codigoNivelAluno) {
                                nivelValido = true;
                                break;
                            }
                        }
                        if (!nivelValido) {
                            String sufixoMensagem = obterMensagemNomeNiveisAulaColetiva(ctx, niveis);
                            throw new ServiceException("O aluno não possui um dos níveis exigidos para a aula." + sufixoMensagem);
                        }
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao validar o nível do aluno para horário da turma: " + e.getMessage());
        }
    }

    private String obterMensagemNomeNiveisAulaColetiva(String ctx, String listaCodigos) throws ServiceException {
        String nomeNiveis = "";
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT n.nome \n");
            sql.append("FROM nivel n \n");
            sql.append("WHERE n.codigo IN (").append(listaCodigos).append(")");

            try (ResultSet rs = nivelDao.createStatement(ctx, sql.toString())) {
                String nomes = "";
                while (rs.next()) {
                    nomes += ", " + rs.getString("nome");
                }
                if (!UteisValidacao.emptyString(nomes)) {
                    nomeNiveis = nomes.substring(1);
                    return " Para prosseguir o aluno precisa ter um dos seguintes níveis:" + nomeNiveis;
                } else {
                    return "";
                }
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao tentar obter o nome dos níveis da aula: " + e.getMessage());
        }
        return "";

    }

    private AdicionarAlunoTurmaResponseDTO adicionarAlunoTurmaZWConversasIA(String ctx, Usuario usuario, Integer empresaZw,
                                                                            Integer codigoHorarioTurma, Integer matriculaAluno,
                                                                            Date dia, AcaoAulaTurmaEnum acao, Integer codNivelAluno,
                                                                            HttpServletRequest request) throws ServiceException {
        try {
            if (UteisValidacao.emptyString(ctx)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            }
            if (usuario == null) {
                usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            }

            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            AgendaTotalJSON agendaTotalJSON = integracaoWS.consultarUmaAula(ctx, Calendario.getData(dia, "dd/MM/yyyy"), codigoHorarioTurma);

            if (agendaTotalJSON == null) {
                throw new Exception("O aluno não foi adicionado porque a aula " + codigoHorarioTurma + " de "+dia+" não foi encontrada");
            }
            AgendaTotalTO aulaHr = new AgendaTotalTO(agendaTotalJSON);
            Integer todasEmpresas = empresaZw;

            List<AgendadoTO> alunos = integracaoWS.consultarAlunos( ctx, todasEmpresas, "", matriculaAluno, null, false);
            AgendadoTO alunoSelecionado = new AgendadoTO();
            AdicionarAlunoTurmaResponseDTO ret = new AdicionarAlunoTurmaResponseDTO();

            for (AgendadoTO aluno : alunos) {
                if (aluno.getMatricula().equals(matriculaAluno.toString())) {
                    alunoSelecionado = aluno;
                }
            }

            boolean forcarMarcar = false;
            String retorno = "";
            if (acao.equals(AcaoAulaTurmaEnum.AULA_EXPERIMENTAL)) {
                // valida se idade do aluno está dentro do permitiro para o horário
                turmaDaoImpl.validarClienteIdadeEstaValidaParaHorarioTurma(ctx, codigoHorarioTurma, alunoSelecionado.getCodigoCliente());

                if (aulaHr.getAulaColetiva()) {
                    // Caso seja aula coletiva, valida se o aluno possui o nível
                    validarClientePossuiNivelAulaColetiva(ctx, codigoHorarioTurma, alunoSelecionado.getCodigoCliente(), codNivelAluno);
                    retorno = agendaTotalService.inserirAlunoAulaCheiaConversasIA(
                            ctx,
                            alunoSelecionado.getCodigoCliente(),
                            codigoHorarioTurma,
                            Calendario.getDataComHoraZerada(dia),
                            OrigemSistemaEnum.CONVERSAS_IA,
                            usuario,
                            empresaZw,
                            true,
                            false);
                } else {
                    // valida se a turma permite aula experimental
                    if (!turmaDaoImpl.isTurmaPermiteAulaExperimental(ctx, codigoHorarioTurma)) {
                        throw new ServiceException("Esta turma não permite aula experimental");
                    }
                    // valida se aluno já possui compromisso no horário
                    if (turmaDaoImpl.alunoJaPossuiCompromissoReposicao(ctx, codigoHorarioTurma, alunoSelecionado.getCodigoCliente())) {
                        throw new ServiceException("Você já possui compromissos no horário");
                    }
                    retorno = agendaTotalService.incluirAulaExperimental(ctx, matriculaAluno, dia, codigoHorarioTurma, usuario.getUsuarioZW(), null);
                }

                if (retorno.toLowerCase().startsWith("erro")) {
                    throw new ServiceException(retorno.replace("ERRO:", ""));
                } else if (retorno.equals("semPermissaoDiariaFreePass")) {
                    Map<String, Object> conteudo = new HashMap<>();
                    conteudo.put("messageID", retorno);
                    ret.setConteudo(conteudo);
                } else {
                    try {
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                        Map<String, Object> conteudo = new HashMap<>();
                        conteudo.put("turma", turmaUpdated(ctx, empresaZw, true, aulaHr, dia.getTime(), request));
                        ret.setConteudo(conteudo);
                    } catch (Exception e) {
                        e.printStackTrace();
                        Uteis.logarDebug("Não foi possível obter os dados da turma para retornar mas o aluno foi agendado com sucesso: " + e.getMessage());
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                        Map<String, Object> conteudo = new HashMap<>();
                        conteudo.put("turma", "Aluno agendado com sucesso!");
                        ret.setConteudo(conteudo);
                    }
                    return ret;
                }
            }

            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getCause() != null) {
                Uteis.logarDebug(" #### [AgendaService.adicionarAluno] CTX: " + ctx + " - ERRO: " + e.getCause().getMessage());
                throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e.getCause());
            }
            Uteis.logarDebug(" #### [AgendaService.adicionarAluno] CTX: " + ctx + " - ERRO: " + e.getMessage());
            throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e);
        }
    }

    private AdicionarAlunoTurmaResponseDTO adicionarAlunoTurmaZW(String ctx, Usuario usuario, Integer empresaId,
                                                                 Integer aulaHorario, Integer matriculaAluno, Date dia,
                                                                 Integer produtoId, Integer aulaDesmarcarId,
                                                                 Date diaAulaDesmarcar, AcaoAulaTurmaEnum acao,
                                                                 String bookingId,
                                                                 Boolean autorizado,
                                                                 HttpServletRequest request) throws ServiceException {
        try {
            if (UteisValidacao.emptyString(ctx)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            }
            if (usuario == null) {
                usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            }
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            AgendaTotalJSON agendaTotalJSON = integracaoWS.consultarUmaAula(ctx, Calendario.getData(dia, "dd/MM/yyyy"), aulaHorario);
            if(agendaTotalJSON == null){
                throw new Exception("O aluno não foi adicionado porque a aula "+aulaHorario+" de "+dia+" não foi encontrada");
            }
            AgendaTotalTO aulaHr = new AgendaTotalTO(agendaTotalJSON);
            Integer todasEmpresas = empresaId;
            ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = configuracaoSistemaService.consultarPorTipo(ctx,ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
            if(!UteisValidacao.emptyString(bookingId) ||
                    aulaHr.isPermiteAlunoOutraEmpresa() ||
                    (cfgPermAlunoMarcarAulaOutraUnidade != null && cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean())){
                todasEmpresas = 0;
            }
            List<AgendadoTO> alunos = integracaoWS.consultarAlunos( ctx, todasEmpresas, "", matriculaAluno, null, false);
            AgendadoTO alunoSelecionado = new AgendadoTO();
            AdicionarAlunoTurmaResponseDTO ret = new AdicionarAlunoTurmaResponseDTO();
            for (AgendadoTO aluno : alunos) {
                if (aluno.getMatricula().equals(matriculaAluno.toString())) {
                    alunoSelecionado = aluno;
                    verificarSeAlunoPossuiContratoConcomitante(ctx, empresaId, alunoSelecionado, dia, aulaHr.getCodigotipo(), aulaHorario, aulaHr.getAulaColetiva());
                }
            }

            try {
                Uteis.logarDebug("================ ALUNO SELECIONADO: " + new JSONObject(alunoSelecionado));
            } catch (Exception e) {}

            boolean forcarMarcar = false;
            String retorno = "";
            boolean alunoContratoFuturo = autorizado ? false : agendadoContratoFuturo(alunoSelecionado, aulaHr);
            if (acao.equals(AcaoAulaTurmaEnum.AULA_EXPERIMENTAL)) {
                if (UteisValidacao.emptyNumber(produtoId) && aulaHr.getAulaColetiva()) {
                    retorno = agendaTotalService.inserirAlunoAulaCheia(
                            ctx,
                            alunoSelecionado.getCodigoCliente(),
                            aulaHorario,
                            Calendario.getDataComHoraZerada(dia),
                            OrigemSistemaEnum.AULA_CHEIA,
                            usuario,
                            empresaId,
                            true,
                            false,
                            bookingId);
                    if (!UteisValidacao.emptyString(bookingId)) {
                        Uteis.logarDebug("#### [AgendaServiceImpl.adicionarAlunoTurmaZw] RETORNO inserirAlunoAulaCheia BOOKING: " + retorno);
                    }
                } else {
                    retorno = agendaTotalService.incluirAulaExperimental(ctx, matriculaAluno, dia, aulaHorario, usuario.getUsuarioZW(), produtoId);
                }
                if (retorno.toLowerCase().startsWith("erro")) {
                    throw new ServiceException(retorno.replace("ERRO:", ""));
                } else if (retorno.equals("semPermissaoDiariaFreePass")) {
                    Map<String, Object> conteudo = new HashMap<>();
                    conteudo.put("messageID", retorno);
                    ret.setConteudo(conteudo);
                } else {
                    ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                    Map<String, Object> conteudo = new HashMap<>();
                    conteudo.put("turma", turmaUpdated(ctx, empresaId, true, aulaHr, dia.getTime(), request));
                    ret.setConteudo(conteudo);
                    return ret;
                }
            } else if (acao.equals(AcaoAulaTurmaEnum.MARCACAO) || acao.equals(AcaoAulaTurmaEnum.REPOSICAO)) {
                if (!alunoSelecionado.getUsaTurma()) {
                    aulaDesmarcarId = aulaHorario;
                    diaAulaDesmarcar = dia;
                    if (alunoSelecionado.getUsaSaldo() && alunoSelecionado.getSaldoCreditoTreino() <= alunoSelecionado.getQtdReposicoesFuturas()) {
                        throw new ServiceException(AgendaExcecoes.ERRO_CREDITO_EXCEDIDO);
                    }
                } else {
                    if (diaAulaDesmarcar == null) {
                        diaAulaDesmarcar = dia;
                    }
                    forcarMarcar = agendaTotalService.consultarTemAulaExtra(ctx, aulaHr.getCodigotipo(), alunoSelecionado.getCodigoContrato(), alunoSelecionado.getSaldoCreditoTreino());
                }
                if (!UteisValidacao.emptyNumber(aulaDesmarcarId)) {
                    List<AgendaTotalJSON> aulasDesmarcadas = agendaTotalService.consultarAulasDesmarcadas(ctx, matriculaAluno, null);
                    for (AgendaTotalJSON aulaJSON : aulasDesmarcadas) {
                        if (aulaJSON.getId().equals(aulaDesmarcarId.toString())) {
                            alunoSelecionado.setCodigoContrato(aulaJSON.getCodigoContrato());
                            break;
                        }
                    }
                }
                retorno = agendaTotalService.reporAula(ctx, alunoSelecionado.getCodigoCliente(), aulaDesmarcarId, aulaHorario,
                        alunoContratoFuturo ? alunoSelecionado.getCodContratoFuturo() : alunoSelecionado.getCodigoContrato(),
                        OrigemSistemaEnum.AULA_CHEIA, usuario.getUsuarioZW(), empresaId, diaAulaDesmarcar, dia, forcarMarcar);
                if (retorno.toLowerCase().startsWith("erro")) {
                    throw new ServiceException(retorno.replace("ERRO:", ""));
                } else {
                    ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                    Map<String, Object> conteudo = new HashMap<>();
                    TurmaResponseDTO turmaUpdated = turmaUpdated(ctx, empresaId, true, aulaHr, dia.getTime(), request);
                    conteudo.put("turma", turmaUpdated);
                    try {
                        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                        clienteService.enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(ctx, clienteService.obterPorCodigo(ctx, alunoSelecionado.getCodigoCliente()), formatter.format(aulaHr.getDateInicio()), aulaHr.getTitulo(), aulaHorario, turmaUpdated.getProfessor().getNome());
                    } catch (Exception e) {
                        Uteis.logar(null, "Erro ao enviar notificacao: " + e.getMessage());
                    }
                    ret.setConteudo(conteudo);
                    return ret;
                }
            } else if (acao.equals(AcaoAulaTurmaEnum.SELECIONAR_ALUNO)){
                if (aulaHr.getAulaColetiva()) {
                    if(autorizado){
                        retorno = agendaTotalService.inserirAutorizadoAulaCheia(
                                ctx,
                                matriculaAluno,
                                aulaHorario,
                                Calendario.getDataComHoraZerada(dia),
                                OrigemSistemaEnum.AULA_CHEIA,
                                usuario,
                                empresaId);
                    } else {
                        retorno = agendaTotalService.inserirAlunoAulaCheia(
                                ctx,
                                alunoSelecionado.getCodigoCliente(),
                                aulaHorario,
                                Calendario.getDataComHoraZerada(dia),
                                OrigemSistemaEnum.AULA_CHEIA,
                                usuario,
                                empresaId,
                                false,
                                false,
                                null);
                    }


                    alunoSelecionado.setDiaria(false);
                    if(retorno.contains("CONTROLAR_FREE_PASS")){
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.ALUNO_SEM_CONTRATO);
                        inserirAlunoIntegracaoSelfloops(ctx, empresaId, matriculaAluno, aulaHorario);
                        return ret;
                    }else if (retorno.contains("AULAEXPERIMENTAL")) {
                        Map<String, Object> infoAulaExperimental = new HashMap<>();
                        if(retorno.contains("Você não tem essa modalidade")){
                            infoAulaExperimental.put("messageID", "aulaExperimentalModalidade");
                            infoAulaExperimental.put("messageValue", retorno.split("suas")[1].split("aulas")[0].trim());
                        }else if(retorno.contains("não está dentro do horário")){
                            infoAulaExperimental.put("messageID", "aulaExperimentalHorario");
                            infoAulaExperimental.put("messageValue", retorno.split("suas")[1].split("aulas")[0].trim());
                        }
                        String mensagem = retorno.replaceAll("AULAEXPERIMENTAL", "").replaceFirst("ERRO\\:", "").replaceFirst("Você", "O aluno");
                        infoAulaExperimental.put("bodyModal", mensagem);
                        ret.setConteudo(infoAulaExperimental);
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.AULA_EXPERIMENTAL);
                        inserirAlunoIntegracaoSelfloops(ctx, empresaId, matriculaAluno, aulaHorario);
                    } else if (retorno.startsWith("ERRO:")) {
                        throw new ServiceException(retorno.replace("ERRO:", ""));
                    } else {
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                        Map<String, Object> conteudo = new HashMap<>();
                        TurmaResponseDTO turmaUpdated = turmaUpdated(ctx, empresaId, true, aulaHr, dia.getTime(), request);
                        conteudo.put("turma", turmaUpdated);
                        ret.setConteudo(conteudo);
                        inserirAlunoIntegracaoSelfloops(ctx, empresaId, matriculaAluno, aulaHorario);
                        try {
                            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                            clienteService.enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(ctx, clienteService.consultarPorMatricula(ctx, alunoSelecionado.getMatricula()), formatter.format(aulaHr.getDateInicio()), aulaHr.getTitulo(), aulaHorario, turmaUpdated.getProfessor().getNome());
                        } catch (Exception e) {
                            Uteis.logar(null, "Erro ao enviar notificacao: " + e.getMessage());
                        }
                        return ret;
                    }
                } else if (!StringUtils.isBlank(alunoSelecionado.getSituacao()) && !alunoSelecionado.getSituacao().equals("AT") && !alunoContratoFuturo) {
                    ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.ALUNO_SEM_CONTRATO);
                    return ret;
                } else {
                    if (alunoSelecionado.getUsaTurma() && alunoSelecionado.getUsaSaldo() && !alunoContratoFuturo) {
                        forcarMarcar = agendaTotalService.consultarTemAulaExtra(ctx, aulaHr.getCodigotipo(), alunoSelecionado.getCodigoContrato(), alunoSelecionado.getSaldoCreditoTreino());
                        if (forcarMarcar) {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.CREDITOS_EXTRA);
                            Map<String, Object> infoSaldo = new HashMap<>();
                            infoSaldo.put("saldoCredito", alunoSelecionado.getSaldoCreditoTreino());
                            ret.setConteudo(infoSaldo);
                        } else {
                            List<AgendaTotalJSON> aulasDesmarcadas = agendaTotalService.consultarAulasDesmarcadas(ctx, matriculaAluno, null);
                            List<Map<String, Object>> aulasDesmarcadasRet = new ArrayList<>();
                            for (AgendaTotalJSON aulaDesmarcada : aulasDesmarcadas) {
                                aulasDesmarcadasRet.add(aulaDiaAluno(aulaDesmarcada));
                            }
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.MARCAR_REPOSICAO);
                            ret.getConteudo().put("aulasDesmarcadas", aulasDesmarcadasRet);
                        }
                        return ret;
                    } else if (alunoSelecionado.getUsaTurma() && !alunoSelecionado.getUsaSaldo() && !alunoContratoFuturo) {
                        List<AgendaTotalJSON> aulasDesmarcadas = agendaTotalService.consultarAulasDesmarcadas(ctx, matriculaAluno, aulaHr.getCodigotipo());
                        List<Map<String, Object>> aulasDesmarcadasRet = new ArrayList<>();
                        for (AgendaTotalJSON aulaDesmarcada : aulasDesmarcadas) {
                            aulasDesmarcadasRet.add(aulaDiaAluno(aulaDesmarcada));
                        }
                        if (UteisValidacao.emptyList(aulasDesmarcadasRet)) {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.ALUNO_SEM_CONTRATO);
                        } else {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.MARCAR_REPOSICAO);
                            ret.getConteudo().put("aulasDesmarcadas", aulasDesmarcadasRet);
                        }
                    } else if (!alunoSelecionado.getUsaTurma() && alunoSelecionado.getUsaSaldo() && !alunoContratoFuturo) {
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.CREDITOS_PARA_MARCACAO);
                        Map<String, Object> infoSaldo = new HashMap<>();
                        infoSaldo.put("aulasMarcada", alunoSelecionado.getQtdReposicoesFuturas());
                        infoSaldo.put("saldoCredito", alunoSelecionado.getSaldoCreditoTreino());
                        ret.setConteudo(infoSaldo);
                        return ret;
                    } else if (!alunoSelecionado.getUsaTurma() && alunoSelecionado.getUsaSaldo() && alunoContratoFuturo){
                        List<AgendaTotalJSON> aulasDesmarcadas = agendaTotalService.consultarAulasDesmarcadas(ctx, matriculaAluno, null);
                        List<Map<String, Object>> aulasDesmarcadasRet = new ArrayList<>();
                        for (AgendaTotalJSON aulaDesmarcada : aulasDesmarcadas) {
                            aulasDesmarcadasRet.add(aulaDiaAluno(aulaDesmarcada));
                        }
                        if (UteisValidacao.emptyList(aulasDesmarcadasRet)) {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.ALUNO_SEM_CONTRATO);
                        } else {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.MARCAR_REPOSICAO);
                            ret.getConteudo().put("aulasDesmarcadas", aulasDesmarcadasRet);
                        }
                    } else if (alunoSelecionado.getUsaTurma() && !alunoSelecionado.getUsaSaldo() && alunoContratoFuturo) {
                        if (!StringUtils.isBlank(alunoSelecionado.getSituacao()) && !alunoSelecionado.getSituacao().equals("AT")) {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.DIARIA_CONTRATO_FUTURO);
                            return ret;
                        }
                        retorno = agendaTotalService.incluirAulaExperimental(ctx, matriculaAluno, dia, aulaHorario, usuario.getUsuarioZW(), produtoId);
                        if (retorno.toLowerCase().startsWith("erro")) {
                            throw new ServiceException(retorno.replace("ERRO:", ""));
                        } else if (retorno.equals("semPermissaoDiariaFreePass")) {
                            Map<String, Object> conteudo = new HashMap<>();
                            conteudo.put("messageID", retorno);
                            ret.setConteudo(conteudo);
                        } else {
                            ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.SUCESSO);
                            Map<String, Object> conteudo = new HashMap<>();
                            conteudo.put("turma", turmaUpdated(ctx, empresaId, true, aulaHr, dia.getTime(), request));
                            ret.setConteudo(conteudo);
                            return ret;
                        }
                    } else if (alunoSelecionado.getUsaTurma() && alunoSelecionado.getUsaSaldo() && alunoContratoFuturo) {
                        ret.setStatus(SituacaoAdicionarAlunoTurmaDTO.CONTRATO_FUTURO);
                        return ret;
                    }
                }
            }
            return ret;
        } catch (Exception e) {
            e.printStackTrace();
            if(e.getCause() != null){
                Uteis.logarDebug(" #### [AgendaService.adicionarAluno] CTX: " + ctx + " - ERRO: " + e.getCause().getMessage());
                throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e.getCause());
            }
            Uteis.logarDebug(" #### [AgendaService.adicionarAluno] CTX: " + ctx + " - ERRO: " + e.getMessage());
            throw new ServiceException(AgendaExcecoes.ERRO_MARCAR_ALUNO, e);
        }
    }

    private void inserirAlunoIntegracaoSelfloops(String ctx, Integer empresaId, Integer matriculaAluno, Integer aulaHorario) {
        if (aulaTrabalhaComSelfloops(ctx, aulaHorario) && UteisValidacao.emptyString(alunoInseridoSelfloops(ctx, matriculaAluno))) {
            try {
                Empresa empTreino = empresaService.obterPorIdZW(ctx, empresaId);
                if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                    SelfLoopsConfiguracoes configSelf = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                    if (configSelf != null && configSelf.isIntegracaoRelizadaSucesso()) {
                        ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matriculaAluno.toString());
                        UserDTO user = new UserDTO();
                        user.setTeam(configSelf.getEmpresaSelfloops());
                        user.setUser(new UserDetailsDTO());
                        user.getUser().setSource_id(cliente.getMatriculaString());
                        user.getUser().setFirstname(cliente.getPrimeiroNome());
                        user.getUser().setLastname(cliente.getNome().replace(cliente.getPrimeiroNome(), ""));
                        user.getUser().setEmail(cliente.getEmail());
                        user.getUser().setGender(cliente.getSexo());
                        user.getUser().setBirthdate(Uteis.getDataAplicandoFormatacao(cliente.getDataNascimento(), "yyyy-MM-dd"));
                        user.getUser().setNickname(cliente.getNomeAbreviado());

                        JSONObject retorno = selfloopsConfiguracoesService.criarAlunoIntegracaoSelfloops(configSelf.getCodeSelfloops(), configSelf.getRefreshToken(), user, ctx, configSelf.getEmpresa().getCodigo());
                        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                            try (PreparedStatement update = conZW.prepareStatement("update cliente set idSelfloops = ? where codigo = ?;")) {
                                update.setString(1, retorno.getJSONObject("user").getString("id"));
                                update.setInt(2, cliente.getCodigoCliente());
                                update.execute();
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.SEVERE, "Erro inserirAlunoIntegracaoSelfloops: ", ex);
            }
        }
    }

    private String alunoInseridoSelfloops(String ctx, Integer matricula) {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement update = conZW.prepareStatement("select idSelfloops from cliente where codigoMatricula = ?;")) {
                    update.setInt(1, matricula);
                    try (ResultSet clienteRs = update.executeQuery()) {
                        if (clienteRs.next()) {
                            return clienteRs.getString("idSelfloops");
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro alunoInseridoSelfloops: " + ex.getMessage());
        }
        return "";
    }

    private boolean aulaTrabalhaComSelfloops(String ctx, Integer horarioTurma) {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement select = conZW.prepareStatement("select t.aulaIntegracaoSelfloops from horarioturma ht \n" +
                        "inner join turma t on t.codigo = ht.turma \n" +
                        " where ht.codigo = ?;")) {
                    select.setInt(1, horarioTurma);
                    try (ResultSet clienteRs = select.executeQuery()) {
                        if (clienteRs.next()) {
                            return clienteRs.getBoolean("aulaIntegracaoSelfloops");
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro aulaTrabalhaComSelfloops: " + ex.getMessage());
        }
        return false;
    }

    private void verificarSeAlunoPossuiContratoConcomitante(String ctx, Integer empresa, AgendadoTO alunoSelecionado, Date diaAula, Integer modalidadeAula, Integer horarioTurma, Boolean isAulaColetiva) throws Exception {
        // verificar se o aluno possui contrato concomitante e se sim, retorna os dados do contrato correto pela modalidade
        if (!alunoSelecionado.getCodigoContrato().equals(0)) {
            JSONObject dados = new JSONObject();
            dados.put("matricula", alunoSelecionado.getMatricula());
            dados.put("modalidade", modalidadeAula);
            dados.put("horarioTurma", horarioTurma);
            dados.put("dia", diaAula.getTime());

            try {
                JSONObject retorno = chamadaZW(ctx, "/prest/aulacheia/is-aluno-contrato-concomitante", empresa, null, null , dados);
                if (retorno.has("content")) {
                    JSONObject content = retorno.getJSONObject("content");
                    if (content.getBoolean("isContratoConcomitante") && content.has("dadosContrato")) {
                        JSONObject dadosContrato = content.getJSONObject("dadosContrato");
                        Integer codigoContrato = dadosContrato.getInt("codigoContrato");
                        Boolean usaTurma = dadosContrato.getBoolean("usaTurma");
                        Boolean contratoFuturo = dadosContrato.getBoolean("contratoFuturo");

                        alunoSelecionado.setCodigoContrato(codigoContrato);
                        alunoSelecionado.setUsaTurma(usaTurma);
                        alunoSelecionado.setContratoFuturo(contratoFuturo);
                    }
                }
            } catch (Exception ex) {
                Uteis.logar(ex, AgendaService.class);
            }
        }
    }

    private TurmaResponseDTO turmaUpdated(String ctx, Integer empresa,
                                          Boolean consultarClientes,
                                          AgendaTotalTO horarioTurma, Long dia, HttpServletRequest request) throws Exception {
        Map<Integer, String> mapaProfessores = agendaTotalService.obterMapaProfessores(ctx, empresa, Boolean.FALSE, Boolean.FALSE);
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        AgendaTotalJSON aula = integracaoWS.consultarUmaAula( ctx, Calendario.getData(new Date(dia), "dd/MM/yyyy"), Integer.valueOf(horarioTurma.getId()));
        if (aula == null) {
            throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
        }
        AgendaTotalTO aulaHr = new AgendaTotalTO(aula);
        SimpleDateFormat formato = new SimpleDateFormat("yyyy-MM-dd");
        Date dataHoje = formato.parse(Calendario.getData(new Date(dia), "yyyy-MM-dd"));
        Map<Integer, EdicaoAulaTemporaria> mapaEdicoes = obterEdicoesTemporarias(ctx, dataHoje, dataHoje);
        EdicaoAulaTemporaria edicaoAulaTemporaria = mapaEdicoes.get(Integer.valueOf(aula.getId()));
        if(edicaoAulaTemporaria != null){
            aulaHr.setResponsavel(edicaoAulaTemporaria.getProfessorNome());
            aulaHr.setCodigoResponsavel(edicaoAulaTemporaria.getProfessor());
            aulaHr.setNrVagas(edicaoAulaTemporaria.getCapacidade());
            aulaHr.setLocal(edicaoAulaTemporaria.getAmbienteNome());
            aulaHr.setCodigoLocal(edicaoAulaTemporaria.getAmbiente());
            String horarioInicial = formatTime(edicaoAulaTemporaria.getHorarioInicial());
            String horarioFinal = formatTime(edicaoAulaTemporaria.getHorarioFinal());
            aulaHr.setStartDate(Calendario.getDataComHora(aulaHr.getStartDate(), horarioInicial));
            aulaHr.setEndDate(Calendario.getDataComHora(aulaHr.getEndDate(), horarioFinal));
            aulaHr.setNome(edicaoAulaTemporaria.getNome());
        }

        ProfessorSubstituido professorSubstituido = agendaTotalService.obterProfessorSubstituido(ctx, dataHoje, Integer.valueOf(horarioTurma.getId()));
        if (professorSubstituido != null) {
            professorSubstituido.setNomeProfessorOrigem(aulaHr.getResponsavel());
            aulaHr.setResponsavel(mapaProfessores.get(professorSubstituido.getCodigoProfessorSubstituto()));
            aulaHr.setCodigoResponsavel(professorSubstituido.getCodigoProfessorSubstituto());
            aulaHr.setSubstituiuProfessor(true);
            aulaHr.setSubstituido(professorSubstituido);
        }

        List<AgendaTotalTO> aulas = new ArrayList<>();
        aulas.add(aulaHr);
        Map<String, List<AgendadoTO>> mapaAlunos = consultarClientes ?agendaTotalService.montarMapaAgendados(
                ctx,
                Calendario.getDataComHoraZerada(new Date(dia)),
                Calendario.getDataComHora(new Date(dia), "23:59:59"),
                empresa,
                aulas,
                false
                ) : new HashMap<>();
        return agendaZWToTurmaResponseDTO(
                ctx,
                request,
                aulaHr.getIdentificador(),
                aulaHr,
                mapaAlunos
        );
    }

    private Map<String, Object> aulaDiaAluno(AgendaTotalJSON aulaDiaJSON) throws Exception {
        Map<String, Object> aulaDiaAlunoRet = new HashMap<>();
        Date inicio = Calendario.getDate("dd/MM/yyyy HH:mm", aulaDiaJSON.getInicio());
        Date fim = Calendario.getDate("dd/MM/yyyy HH:mm", aulaDiaJSON.getFim());
        aulaDiaAlunoRet.put("id", aulaDiaJSON.getId());
        aulaDiaAlunoRet.put("modalidadeNome", aulaDiaJSON.getTipo());
        aulaDiaAlunoRet.put("data", Calendario.getData(aulaDiaJSON.getDia(), "yyyyMMdd"));
        aulaDiaAlunoRet.put("horarioInicio", Calendario.getHora(inicio, "HH:mm"));
        aulaDiaAlunoRet.put("horarioFim", Calendario.getHora(fim, "HH:mm"));

        return aulaDiaAlunoRet;
    }

    private Boolean agendadoContratoFuturo(AgendadoTO agendadoSelecionado, AgendaTotalTO aula) {
        return  agendadoSelecionado.getContratoFuturo()
                && agendadoSelecionado.getModalidadesContratoFuturo().size() > 0
                && agendadoSelecionado.getModalidadesContratoFuturo().contains(aula.getCodigotipo());
    }

    public Integer nrAlunosHorarioDia(String ctx, Integer horario, Date dia) throws Exception {
        String sql = "select count(codigo) as nr from AulaAluno where horario_codigo = " + horario
                + " and dia = '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "'";
        try (ResultSet rs = aulaAlunoDao.createStatement(ctx, sql)) {
            return rs.next() ? rs.getInt("nr") : 0;
        }
    }

    public List<AlunoResponseTO> alunosHorarioDia(String ctx, AulaHorario aulaHorario, Date dia) throws Exception {
        return alunosHorarioDia(null, ctx, aulaHorario, dia);
    }

    private List<AlunoResponseTO> alunosHorarioDia(HttpServletRequest request, String ctx, AulaHorario aulaHorario, Date dia) throws Exception {
        List<AulaAluno> alunos = aulaAlunoDao.alunosHorarioDia(ctx, aulaHorario.getCodigo(), dia);
        List<AlunoResponseTO> alunosTO = new ArrayList<AlunoResponseTO>();
        for (AulaAluno a : alunos) {
            if (request != null) {
                a.getCliente().setUrlFoto(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, a.getCliente().getPessoa().getFotoKey(), a.getCodigoCliente(), false, ctx, false));
            }
            alunosTO.add(new AlunoResponseTO(a.getCliente(), a.getPresencaConfirmada(), SuperControle.independente(ctx)));
        }
        return alunosTO;
    }

    public List<AulaAluno> aulasAluno(String ctx, Integer matricula, Date inicio, Date fim) throws Exception {
        String hql = "select obj from AulaAluno obj where obj.dia between :inicio and :fim and obj.cliente.matricula = :matricula";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("matricula", matricula);
        params.put("inicio", Calendario.getDataComHoraZerada(inicio));
        params.put("fim", Calendario.getDataComHora(fim, "23:59"));
        return aulaAlunoDao.findByParam(ctx, hql, params);
    }

    public void substituirProfessor(SubstituirProfessorDTO dados, Date dia, Integer horarioAula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            Integer professorAtualId = null;
            if (SuperControle.independente(ctx)) {
                professorAtualId = aulaHorarioDao.findById(ctx, horarioAula).getAula().getProfessor().getCodigo();
            } else {
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aulaCheia = integracaoWS.consultarUmaAula( ctx, Calendario.getData(dia, "dd/MM/yyyy"), horarioAula);
                if (aulaCheia == null) {
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
                professorAtualId = aulaCheia.getCodigoResponsavel();
            }


            aulaService.substituirProfessor(ctx, horarioAula, professorAtualId,
                    dados.getProfessorId(), dia, usuario, dados.getJustificativa());
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SUBSTITUIR, e);
        }

    }

    @Override
    public AulaAlunoJSON adicionarAluno(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return adicionarAluno(ctx, alunoid, dia, horarioAula);
    }

    public AulaAlunoJSON adicionarAluno(String ctx, Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        try {

            if (validarSePodeInserirAlunoAAula(ctx, dia, horarioAula)) {
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, horarioAula);
                AulaAluno aulaAluno = new AulaAluno();
                aulaAluno.setHorario(aulaHorario);
                ClienteSintetico aluno = clienteService.obterPorId(ctx, alunoid);
                aulaAluno.setCliente(aluno);
                aulaAluno.setProfessor(aulaHorario.getAula().getProfessor());
                aulaAluno.setDia(dia);
                aulaAluno = aulaAlunoDao.insert(ctx, aulaAluno);
                return new AulaAlunoJSON(new AgendaTotalJSON(aulaHorarioEvento(dia, horarioAula, ctx), nrAlunosHorarioDia(ctx, horarioAula, dia)), aluno);
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ADICIONAR_ALUNO_AULA, e);
        }
    }

    public String incluirHorarioEquipamentoAluno(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, String equipamento, Integer usuarioId, String dia, Boolean app) throws ServiceException {
        String ret = "";
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                deletarAlunoAulaSensorIntegracaoSelfloops(ctx, empresaId, horarioId, alunoId, Calendario.getDate("yyyyMMdd", dia));
                deletarHorarioEquipamentoAluno(ctx, empresaId, horarioId, alunoId, Calendario.getDate("yyyyMMdd", dia));
                try (PreparedStatement insert = conZW.prepareStatement("insert into horarioequipamentoaluno (horarioturma," +
                        " equipamento, cliente, empresa, diaAula, datalancamento, usuariolancou, app) values (?, ?, ?, ?, ?, ?, ?, ?) returning codigo;")) {
                    insert.setInt(1, horarioId);
                    insert.setString(2, equipamento);
                    insert.setInt(3, alunoId);
                    insert.setInt(4, empresaId);
                    insert.setDate(5, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", dia)));
                    insert.setDate(6, Uteis.getDataJDBC(Calendario.hoje()));
                    if(app) {
                        insert.setNull(7, Types.INTEGER);
                    } else {
                        insert.setInt(7, usuarioId);
                    }
                    insert.setBoolean(8, app);
                    try (ResultSet rs = insert.executeQuery()) {
                        if (rs.next()) {
                            ret = horarioEquipamentoOcupado(ctx, Calendario.getDate("yyyyMMdd", dia), horarioId);
                        }
                    }
                }
            }
            incluirAlunoAulaSensorIntegracaoSelfloops(ctx, empresaId, horarioId, alunoId, equipamento, dia);
            return ret;
        } catch (PSQLException e) {
            if (UNIQUE_VIOLATION_SQLSTATE.equals(e.getSQLState())) {
                throw new ServiceException(AgendaExcecoes.ERRO_EQUIPAMENTO_JA_RESERVADO, e);
            }
            throw new ServiceException(AgendaExcecoes.ERRO_ADICIONAR_ALUNO_AULA, e);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ADICIONAR_ALUNO_AULA, e);
        }
    }

    private void incluirAlunoAulaSensorIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, String equipamento, String dia) {
        try {
            Empresa empTreino = empresaService.obterPorIdZW(ctx, empresaId);
            if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                SelfLoopsConfiguracoes configSelf = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                if (configSelf != null && configSelf.isIntegracaoRelizadaSucesso()) {
                    HashMap<String, String> dadosSelfloops = obterDadosAlunoAulaEquipamentoSelfloops(ctx, alunoId, horarioId, equipamento, dia);
                    if (dadosSelfloops != null) {
                        Aparelho aparelho = aparelhoService.obterPorId(ctx, Integer.valueOf(dadosSelfloops.get("codigoAparelhoTreino")));
                        if (aparelho != null && !UteisValidacao.emptyString(aparelho.getSensorSelfloops())) {
                            CourseDTO course = new CourseDTO();
                            course.setCourse(new CourseSensorDTO());
                            course.getCourse().setSensors(new ArrayList<>());
                            course.getCourse().getSensors().add(aparelho.getSensorSelfloops());
                            JSONObject courseDia = selfloopsConfiguracoesService.obterCourseDoDiaIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                    configSelf.getRefreshToken(), configSelf.getEmpresaSelfloops(), dadosSelfloops.get("courseScheduleId"), dia, ctx, configSelf.getEmpresa().getCodigo());
                            if (courseDia.has("courses")) {
                                JSONObject retorno = selfloopsConfiguracoesService.inserirAlunoAulaSensorIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                        configSelf.getRefreshToken(), dadosSelfloops.get("idSelfloops"), courseDia.getJSONArray("courses").getJSONObject(0).getString("id"), course, ctx, configSelf.getEmpresa().getCodigo());
                                registrarInclusaoAlunoAulaSensorIntegracaoSelfloops(ctx, dadosSelfloops.get("idSelfloops"), dadosSelfloops.get("courseScheduleId"),
                                        aparelho.getSensorSelfloops(), dia, new JSONObject(course), retorno, horarioId, alunoId);
                            }

                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro incluirAlunoAulaSensorIntegracaoSelfloops: " + ex.getMessage());
        }

    }

    private void registrarInclusaoAlunoAulaSensorIntegracaoSelfloops(String ctx, String userIdSelfloops, String courseScheduleId, String sensorIdSelfloops, String diaAula, JSONObject jsonEnvio, JSONObject jsonRetorno, Integer horario, Integer cliente) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement insert = conZW.prepareStatement(
                    "insert into alunoturmahorariosensorintegracaoselfloops (userIdSelfloops, courseScheduleId, sensorIdSelfloops, diaAula, jsonEnvio, jsonRetorno, horarioTurma, cliente) values (?, ? , ?, ?, ?, ?, ?, ?) returning codigo;")) {
                insert.setString(1, userIdSelfloops);
                insert.setString(2, courseScheduleId);
                insert.setString(3, sensorIdSelfloops);
                insert.setDate(4, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", diaAula)));
                insert.setString(5, jsonEnvio.toString());
                insert.setString(6, jsonRetorno.toString());
                insert.setInt(7, horario);
                insert.setInt(8, cliente);
                try (ResultSet rs = insert.executeQuery()) {

                }
            }
        }
    }

    private HashMap<String, String> obterDadosAlunoAulaEquipamentoSelfloops(String ctx, Integer alunoId, Integer horarioId, String equipamento, String dia) {
        try {
            HashMap<String, String> dadosSelfloops = new HashMap<>();
            StringBuilder sql = new StringBuilder();
            sql.append("select c.idSelfloops, tis.coursescheduleid, tep.codigo_aparelhotreino from horarioequipamentoaluno he\n");
            sql.append("inner join cliente c on c.codigo = he.cliente\n");
            sql.append("inner join horarioturma ht on ht.codigo = he.horarioturma\n");
            sql.append("inner join turma t on t.codigo = ht.turma\n");
            sql.append("inner join turmahorariointegracaoselfloops tis on tis.turma = t.codigo and tis.horarioidentificador = concat(ht.codigo, '-', ht.horainicial, '_', ht.horafinal)\n");
            sql.append("inner join turmamapaequipamentoaparelho tep on tep.mapaequipamento = he.equipamento and tep.turma = t.codigo\n");
            sql.append("where he.equipamento = ? and c.codigo = ? and ht.codigo = ? and he.diaaula = ?");
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement select = conZW.prepareStatement(sql.toString())) {
                    select.setString(1, equipamento);
                    select.setInt(2, alunoId);
                    select.setInt(3, horarioId);
                    select.setDate(4, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", dia)));
                    try (ResultSet dados = select.executeQuery()) {
                        if (dados.next()) {
                            dadosSelfloops.put("idSelfloops", dados.getString("idSelfloops"));
                            dadosSelfloops.put("courseScheduleId", dados.getString("courseScheduleId"));
                            dadosSelfloops.put("codigoAparelhoTreino", dados.getString("codigo_aparelhotreino"));
                            return dadosSelfloops;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro obterDadosAlunoAulaEquipamentoSelfloops: " + ex.getMessage());
        }
        return null;
    }

    private String obterCourseScheduleIdAulaDia(String ctx, Integer horarioId) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select tis.coursescheduleid from horarioturma ht\n");
            sql.append("inner join turma t on t.codigo = ht.turma\n");
            sql.append("inner join turmahorariointegracaoselfloops tis on tis.turma = t.codigo\n");
            sql.append("where tis.horarioidentificador = concat(ht.codigo, '-', ht.horainicial, '_', ht.horafinal) and ht.codigo = ?");
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement select = conZW.prepareStatement(sql.toString())) {
                    select.setInt(1, horarioId);
                    try (ResultSet dados = select.executeQuery()) {
                        if (dados.next()) {
                            return dados.getString("courseScheduleId");
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro obterDadosAlunoAulaEquipamentoSelfloops: " + ex.getMessage());
        }
        return null;
    }

    public void deletarHorarioEquipamentoAluno(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, Date dia) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (PreparedStatement delete = conZW.prepareStatement("delete from horarioequipamentoaluno where empresa = " + empresaId +
                        " and horarioturma = " + horarioId + " and cliente = " + alunoId + " and diaAula = '" + Calendario.getData(dia, "dd/MM/yyyy")+"'")) {
                    delete.execute();
                }
            }
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ADICIONAR_ALUNO_AULA, e);
        }
    }

    private void deletarAlunoAulaSensorIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, Date dia) throws ServiceException {
        try {
            Empresa empTreino = empresaService.obterPorIdZW(ctx, empresaId);
            if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                SelfLoopsConfiguracoes configSelf = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                if (configSelf != null && configSelf.isIntegracaoRelizadaSucesso()) {
                    String equipamento = "";
                    try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                        try (PreparedStatement select = conZW.prepareStatement("select equipamento from horarioequipamentoaluno where empresa = " + empresaId +
                                " and horarioturma = " + horarioId + " and cliente = " + alunoId + " and diaAula = '" + Calendario.getData(dia, "dd/MM/yyyy") + "'")) {
                            try (ResultSet dados = select.executeQuery()) {
                                if (dados.next()) {
                                    equipamento = dados.getString("equipamento");
                                }
                            }
                        }
                    }

                    if (!UteisValidacao.emptyString(equipamento)) {
                        HashMap<String, String> dadosSelfloops = obterDadosAlunoAulaEquipamentoSelfloops(ctx, alunoId, horarioId, equipamento, Calendario.getData(dia, "yyyyMMdd"));
                        if (dadosSelfloops != null) {
                            JSONObject courseDia = selfloopsConfiguracoesService.obterCourseDoDiaIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                    configSelf.getRefreshToken(), configSelf.getEmpresaSelfloops(), dadosSelfloops.get("courseScheduleId"), Calendario.getData(dia, "yyyy-MM-dd"), ctx, configSelf.getEmpresa().getCodigo());
                            if (courseDia.has("courses")) {
                                JSONObject retorno = selfloopsConfiguracoesService.deleteAlunoAulaSensorIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                        configSelf.getRefreshToken(), dadosSelfloops.get("idSelfloops"), courseDia.getJSONArray("courses").getJSONObject(0).getString("id"), ctx, configSelf.getEmpresa().getCodigo());
                                if (retorno.has("status") && retorno.getInt("status") == 201) {
                                    deleteRegistroAlunoAulaSensorIntegracaoSelfloops(ctx, dadosSelfloops.get("idSelfloops"), dadosSelfloops.get("courseScheduleId"), Calendario.getData(dia, "yyyyMMdd"), horarioId, alunoId);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro deletarAlunoAulaSensorIntegracaoSelfloops: " + ex.getMessage());
        }
    }

    public void obterAtividadesCourseDoDiaIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, String dia) throws ServiceException {
        try {
            Empresa empTreino = empresaService.obterPorIdZW(ctx, empresaId);
            if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                SelfLoopsConfiguracoes configSelf = selfloopsConfiguracoesService.obterPorEmpresa(ctx, empTreino.getCodigo());
                if (configSelf != null && configSelf.isIntegracaoRelizadaSucesso()) {
                        String courseScheduleId = obterCourseScheduleIdAulaDia(ctx, horarioId);
                        if (!UteisValidacao.emptyString(courseScheduleId)) {
                            JSONObject courseDia = selfloopsConfiguracoesService.obterCourseDoDiaIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                    configSelf.getRefreshToken(), configSelf.getEmpresaSelfloops(), courseScheduleId, Calendario.getData(Calendario.getDate("yyyyMMdd", dia), "yyyy-MM-dd"), ctx, configSelf.getEmpresa().getCodigo());
                            if (courseDia.has("courses")) {
                                String courseId = courseDia.getJSONArray("courses").getJSONObject(0).getString("id");
                                JSONObject retorno = selfloopsConfiguracoesService.obterAtividadesCourseDoDiaIntegracaoSelfloops(configSelf.getCodeSelfloops(),
                                        configSelf.getRefreshToken(), courseId, ctx, configSelf.getEmpresa().getCodigo());

                                try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                                    ConexaoZWServiceImpl.executarConsulta("delete from turmahorarioatividadesintegracaoselfloops where horarioTurma = " + horarioId + " and diaAula = '" + Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", dia)) + "'", conZW);
                                    for (int i = 0; i < retorno.getJSONArray("activities").length(); i++) {
                                        JSONObject atividade = retorno.getJSONArray("activities").getJSONObject(i);

                                        try (PreparedStatement insert = conZW.prepareStatement(
                                                "insert into turmahorarioatividadesintegracaoselfloops (userIdSelfloops, courseScheduleId, courseId, diaAula, jsonRetorno, horarioTurma) values (?, ?, ?, ?, ?, ?) returning codigo;")) {
                                            insert.setString(1, atividade.getString("user_id"));
                                            insert.setString(2, courseScheduleId);
                                            insert.setString(3, courseId);
                                            insert.setDate(4, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", dia)));
                                            insert.setString(5, atividade.toString());
                                            insert.setInt(6, horarioId);
                                            try (ResultSet rs = insert.executeQuery()) {

                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro obterAtividadesCourseDoDiaIntegracaoSelfloops: " + ex.getMessage());
            throw new ServiceException("Erro obterAtividadesCourseDoDiaIntegracaoSelfloops:", ex);
        }
    }

    private void deleteRegistroAlunoAulaSensorIntegracaoSelfloops(String ctx, String userIdSelfloops, String courseScheduleId, String diaAula, Integer horario, Integer cliente) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (PreparedStatement delete = conZW.prepareStatement("delete from alunoturmahorariosensorintegracaoselfloops \n" +
                    " where cliente = ? and horarioTurma = ? and diaAula = ? and courseScheduleId = ? and userIdSelfloops = ?")) {
                delete.setInt(1, cliente);
                delete.setInt(2, horario);
                delete.setDate(3, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", diaAula)));
                delete.setString(4, courseScheduleId);
                delete.setString(5, userIdSelfloops);
                delete.execute();
            }
        }
    }

    public void deletarHorarioEquipamentoAlunoSeExistir(String ctx, Integer empresaId, Integer horarioId, Integer alunoId, Date dia, String equipamento) throws ServiceException {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            String sql = "DELETE FROM horarioequipamentoaluno WHERE empresa = ? AND horarioturma = ? AND cliente = ? AND diaAula = ? AND equipamento = ?";
            try (PreparedStatement delete = conZW.prepareStatement(sql)) {
                // Define os parâmetros na query
                delete.setInt(1, empresaId);
                delete.setInt(2, horarioId);
                delete.setInt(3, alunoId);
                delete.setDate(4, new java.sql.Date(dia.getTime()));
                delete.setString(5, equipamento);

                int rowsAffected = delete.executeUpdate();
                if (rowsAffected == 0) {
                    throw new ServiceException("O equipamento informado não está reservado para o aluno na data e horário informados.");
                }
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_RESERVA_EQUIPAMENTO, e);
        }
    }


    private Boolean validarSePodeInserirAlunoAAula(String ctx, Date dia, Integer horarioAula) throws Exception {
        String usuario = sessaoService.getUsuarioAtual() != null ? sessaoService.getUsuarioAtual().getUsername() : null;
        ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_AGENDAR_COM_ANTECEDENCIA);
        Usuario usuarioAtual;
        if(usuario == null && SuperControle.independente(ctx)){
            usuarioAtual = usuarioService.criarOuConsultarSeExisteRecorrencia(ctx);
        }else{
            usuarioAtual = usuarioService.consultarPorUserName(ctx, usuario);
        }

        int minutosAgendarAntecendencia = cfTipo.getValorAsInteger();
        if (usuarioAtual.getNome().equals("RECORRENCIA") || usuarioAtual.isFuncionalidadeHabilitado(RecursoEnum.USUARIO_MARCAR_ANTECEDENCIA.name())){
            minutosAgendarAntecendencia = 0;
        }
        aulaAlunoDao.getCurrentSession(ctx).clear();
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfg = css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
        AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, horarioAula);
        if (aulaHorario != null && !UteisValidacao.emptyNumber(aulaHorario.getCodigo())) {
            List<AulaAluno> alunosAula = aulaAlunoDao.findListByAttributes(ctx, new String[]{"horario.codigo"}, new Object[]{aulaHorario.getCodigo()}, null, 0, 0);
            if (alunosAula != null && aulaHorario.getAula().getCapacidade() <= alunosAula.size() && !cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.ERRO_AULA_ESTA_CHEIA);
            }

            String[] horaMin = aulaHorario.getInicio().split(":");
            long diferencaEmMinutos = Uteis.minutosEntreDatas(Calendario.hoje(), Uteis.setHoraMinutoSegundo(
                    dia,Integer.parseInt(horaMin[0]),Integer.parseInt(horaMin[1]),0));
            if (diferencaEmMinutos > minutosAgendarAntecendencia && minutosAgendarAntecendencia != 0) {
                Date diaEvento = new Date(dia.getTime());
                Date diaAntecedencia = new Date(dia.getTime());
                diaEvento.setHours(Integer.parseInt(horaMin[0], 10));
                diaEvento.setMinutes(Integer.parseInt(horaMin[1], 10));

                diaAntecedencia.setHours(Integer.parseInt(horaMin[0], 10));
                diaAntecedencia.setMinutes(Integer.parseInt(horaMin[1], 10));

                diaAntecedencia.setMinutes(diaAntecedencia.getMinutes() - minutosAgendarAntecendencia);

                Date dataAcao = Calendario.hoje();
                if (Calendario.menorComHora(diaAntecedencia, dataAcao) && Calendario.menorComHora(dataAcao, diaEvento)) {
                    return true;
                } else {
                    AgendaExcecoes.ERRO_ENCERROU_PRAZO.setDescricaoExcecao(
                            String.format(AgendaExcecoes.ERRO_ENCERROU_PRAZO.getDescricaoExcecao(),
                                    minutosAgendarAntecendencia
                            ));
                    throw new ServiceException(AgendaExcecoes.ERRO_ENCERROU_PRAZO);
                }
            } else {
                return true;
            }
        } else {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_AULA);
        }
    }

    @Override
    public void removerAluno(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        removerAluno(ctx, alunoid, dia, horarioAula);
    }

    public void removerAluno(String ctx, Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        try {
            AulaAluno aulaAluno = obterAulaAluno(ctx, alunoid, horarioAula, dia);
            aulaAlunoDao.delete(ctx, aulaAluno);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_ALUNO_AULA);
        }
    }

    @Override
    public void confirmarPresenca(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AulaAluno aulaAluno = obterAulaAluno(ctx, alunoid, horarioAula, dia);
            aulaAluno.setPresencaConfirmada(true);
            aulaAlunoDao.update(ctx, aulaAluno);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_CONFIRMAR_ALUNO_AULA);
        }
    }

    @Override
    public void removerPresenca(Integer alunoid, Date dia, Integer horarioAula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AulaAluno aulaAluno = obterAulaAluno(ctx, alunoid, horarioAula, dia);
            aulaAluno.setPresencaConfirmada(false);
            aulaAlunoDao.update(ctx, aulaAluno);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_ALUNO_AULA);
        }
    }

    @Override
    public void removerAula(Date dia, Integer aulaHorarioId, String justificativa, Integer empresa) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AulaDiaExclusao aulaDiaExclusao = new AulaDiaExclusao();
            if (SuperControle.independente(ctx)) {
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);
                if (aulaHorario == null) {
                    throw new Exception("Não foi possível obter a aula");
                }
                aulaDiaExclusao.setAula(aulaHorario.getAula());
            } else {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                ResultAlunoClienteSinteticoJSON result = integracaoWS.obterAlunosDeUmaAula(ctx, aulaHorarioId, dia);
                if (result.getResultAgendado().size() > 0) {
                    throw new ServiceException(AgendaExcecoes.ERRO_EXISTE_ALUNOS_NA_AULA);
                }
            }
            aulaDiaExclusao.setCodigoHorarioTurma(aulaHorarioId);
            aulaDiaExclusao.setDataAulaDia(dia);
            aulaDiaExclusao.setUsuario_codigo(sessaoService.getUsuarioAtual().getId());
            aulaDiaExclusao.setDataExclusao(Calendario.hoje());
            aulaDiaExclusao.setJustificativa(justificativa);
            aulaDiaExclusaoDao.insert(ctx, aulaDiaExclusao);
            List<Integer> aulaHorarioSelecionado = new ArrayList<>();
            aulaHorarioSelecionado.add(aulaHorarioId);
            professorSubstituidoDao.removerPorAulaHorario(ctx, aulaHorarioSelecionado);
            //remover aula da gympass
            try {
                removerSlotGympass(ctx, empresa, dia, aulaHorarioId);
            }catch (Exception e){
                Uteis.logar(e, AgendaServiceImpl.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_AULA, e);
        }
    }

    private void removerSlotGympass(String ctx, Integer empresa, Date dia, Integer aulaHorarioId) throws Exception{
        try(ResultSet rs = aulaDao.createStatement(ctx, "select idslotgympass, idclassgympass from horariogympass " +
                "where ativo and codigohorario = " + aulaHorarioId + " and dia = '" +
                Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "'" )){
            if(rs.next()){
                gymPassBookingService.excluirSlots(ctx, empresa, rs.getInt("idslotgympass"),  rs.getInt("idclassgympass"));
            }
        }
    }

    public List<Aula> aulasDiaSemana(String ctx, String dia) throws Exception {
        return aulaDao.aulasDiaSemana(ctx, dia);
    }

    private AulaAluno obterAulaAluno(String ctx, Integer alunoid, Integer horarioAula, Date dia) throws Exception {
        String hql = "select obj from AulaAluno obj where obj.cliente.codigo = :aluno and obj.dia = :dia and obj.horario.codigo = :horario";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("aluno", alunoid);
        params.put("horario", horarioAula);
        params.put("dia", dia);
        return aulaAlunoDao.findObjectByParam(ctx, hql, params);
    }

    @Override
    public Map<String, List<ServicoAgendamentoDTO>> servicosAgendado(Integer empresaId, HttpServletRequest request, Date diaReferencia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Map<String, List<ServicoAgendamentoDTO>> ret = new HashMap<>();
            Date inicio = diaReferencia;
            Date fim = diaReferencia;
            String professoresSelecionados = "";
            String tiposServicoSelecionados = "";
            String statusAgendamentosSelecionados = "";
            if (periodo.equals(PeriodoFiltrarEnum.DIA)) {
                inicio = diaReferencia;
                fim = diaReferencia;
            } else if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
                inicio = Uteis.somarDias(Calendario.inicioSemana(diaReferencia), 1);
                fim = Uteis.somarDias(Calendario.fimSemana(diaReferencia), 1);
            } else if (periodo.equals(PeriodoFiltrarEnum.MES)) {
                inicio = Calendario.inicioMes(diaReferencia);
                fim = Calendario.fimMes(diaReferencia);
            }
            for (Date diaPeriodo : Uteis.getDiasEntreDatas(inicio, fim)) {
                ret.put(Calendario.getData(diaPeriodo, "yyyyMMdd"), new ArrayList<>());
            }
            if (filtros != null) {
                if (!UteisValidacao.emptyList(filtros.getProfessoresId())) {
                    for (Integer professorId : filtros.getProfessoresId()) {
                        professoresSelecionados += "," + professorId;
                    }
                    professoresSelecionados = professoresSelecionados.replaceFirst(",", "");
                }
                if (!UteisValidacao.emptyList(filtros.getTiposServicoId())) {
                    for (Integer tipoServicoId : filtros.getTiposServicoId()) {
                        tiposServicoSelecionados += "," + tipoServicoId;
                    }
                    tiposServicoSelecionados = tiposServicoSelecionados.replaceFirst(",", "");
                }
                if (!UteisValidacao.emptyList(filtros.getStatusAgendamentos())) {
                    for (StatusAgendamentoEnum status : filtros.getStatusAgendamentos()) {
                        statusAgendamentosSelecionados += "," + status.ordinal();
                    }
                    statusAgendamentosSelecionados = statusAgendamentosSelecionados.replaceFirst(",", "");
                }
            }

            if(UteisValidacao.emptyString(tiposServicoSelecionados) || UteisValidacao.emptyString(statusAgendamentosSelecionados)){
                return ret;
            }

            List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    professoresSelecionados,
                    tiposServicoSelecionados,
                    statusAgendamentosSelecionados,
                    null,
                    false,
                    true,
                    null,
                    null,
                    null,
                    null);
            agendamentos.addAll(agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    professoresSelecionados,
                    tiposServicoSelecionados,
                    statusAgendamentosSelecionados,
                    null,
                    false,
                    true,
                    null,
                    null,
                    null, false,
                    null, true));
            for (Agendamento agendamento : agendamentos) {
                agendamentoDao.refresh(ctx, agendamento);
                agendamento.getCliente().setUrlFoto((UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamento.getCliente().getPessoa().getFotoKey(), agendamento.getCliente().getCodigoPessoa(), false, ctx, false)));
                agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), null, false, ctx, false)));
                if (ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")) != null) {
                    ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")).add(new ServicoAgendamentoDTO(agendamento, SuperControle.independente(ctx)));
                } else {
                    List<ServicoAgendamentoDTO> servicosAgendamento = new ArrayList<>();
                    servicosAgendamento.add(new ServicoAgendamentoDTO(agendamento, SuperControle.independente(ctx)));
                    ret.put(Calendario.getData(agendamento.getInicio(), "yyyyMMdd"), servicosAgendamento);
                }
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_LISTAR_AGENDAMENTOS, e);
        }
    }

    public List<ProfessorAgendamentosDTO> agendamentos(Date dia) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Map<Integer, List<AgendamentoEventoDTO>> mapa = new HashMap<Integer, List<AgendamentoEventoDTO>>();
         agendamentoDao.getCurrentSession(ctx).clear();
        List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                Calendario.getDataComHoraZerada(dia),
                Calendario.getDataComHora(dia, "23:59:59"),
                null,
                null,
                null,
                null,
                null,
                false,
                true,
                null,
                null,
                null,
                null);
        for (Agendamento a : agendamentos) {
            List<AgendamentoEventoDTO> mapaagendamentos = mapa.get(a.getProfessor().getCodigo());
            if (mapaagendamentos == null) {
                mapaagendamentos = new ArrayList<AgendamentoEventoDTO>();
                mapa.put(a.getProfessor().getCodigo(), mapaagendamentos);
            }
            mapaagendamentos.add(new AgendamentoEventoDTO(a));
        }
        List<ProfessorAgendamentosDTO> retorno = new ArrayList<ProfessorAgendamentosDTO>();
        for (Integer professor : mapa.keySet()) {
            retorno.add(new ProfessorAgendamentosDTO(professor, mapa.get(professor)));
        }
        return retorno;
    }

    public List<AgendamentosDTO> agendamentosMes(Integer mes, Integer ano, Integer empresaId) throws Exception {
        Map<Integer, AgendamentosDTO> retorno = new HashMap<Integer, AgendamentosDTO>();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Empresa empresa = empresaService.obterPorId(ctx, empresaId);
        Integer empresaZwId = empresa.getCodZW();
        Calendar calendar = Calendario.getInstance();
        calendar.clear();
        String status = "3"; // status de cancelados, esta variavel define os agendamentos listados na agenda
        calendar.set(Calendar.MONTH, mes - 1);
        calendar.set(Calendar.YEAR, ano);
        Date date = calendar.getTime();

        if(!SuperControle.independente(ctx)){
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);

            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            List<AgendaTotalTO> agendamentos = integracaoWS.consultarAgendamentos
                    ( ctx, Calendario.getDataComHoraZerada(date), Uteis.obterUltimoDiaMesUltimaHora(date), empresaZwId, null);

            for (AgendaTotalTO a : agendamentos) {
                AgendamentosDTO agendamentosDTO = retorno.get(Uteis.getDiaMesData(a.getDateInicio()));
                if (agendamentosDTO == null) {
                    agendamentosDTO = new AgendamentosDTO();
                    agendamentosDTO.setDia(Uteis.getDiaMesData(a.getDateInicio()));
                    agendamentosDTO.setMes(mes);
                    agendamentosDTO.setAno(ano);
                    agendamentosDTO.setAgendamentos(new ArrayList<AgendamentoEventoDTO>());
                    retorno.put(Uteis.getDiaMesData(a.getDateInicio()), agendamentosDTO);
                }
            }
                System.out.println(agendamentos);
        }else {

            List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(date),
                    Uteis.obterUltimoDiaMesUltimaHora(date),
                    null,
                    null,
                    null,
                    status,
                    null,
                    false,
                    false,
                    null,
                    null,
                    null,
                    null);
            for (Agendamento a : agendamentos) {
                AgendamentosDTO agendamentosDTO = retorno.get(Uteis.getDiaMesData(a.getInicio()));
                if (agendamentosDTO == null) {
                    agendamentosDTO = new AgendamentosDTO();
                    agendamentosDTO.setDia(Uteis.getDiaMesData(a.getInicio()));
                    agendamentosDTO.setMes(mes);
                    agendamentosDTO.setAno(ano);
                    agendamentosDTO.setAgendamentos(new ArrayList<AgendamentoEventoDTO>());
                    retorno.put(Uteis.getDiaMesData(a.getInicio()), agendamentosDTO);
                }
                agendamentosDTO.getAgendamentos().add(new AgendamentoEventoDTO(a));
            }
        }

        return new ArrayList<AgendamentosDTO>(retorno.values());
    }

    public void gerarOcupacao(String ctx, Date dia) throws Exception {
        List<EventoAulaDTO> horarios = agendaDia(ctx, dia);
        Connection con = aulaDao.getConnection(ctx);

        Random gerador = new Random();
        for (EventoAulaDTO h : horarios) {
            Integer nrAlunosHorarioDia = nrAlunosHorarioDia(ctx, h.getId(), dia);

            Integer limit = h.getAula().getCapacidade() - gerador.nextInt(h.getAula().getCapacidade() / 3) - nrAlunosHorarioDia;
            if (limit > 0) {
                System.out.println(h.getAula().getNome() + " capacidade " + h.getAula().getCapacidade() + " limite " + limit);

                try (ResultSet rs = con.prepareStatement("SELECT codigo FROM clientesintetico limit " + limit).executeQuery()) {
                    while (rs.next()) {
                        adicionarAluno(ctx, rs.getInt("codigo"), dia, h.getId());
                    }
                }
            }

        }

    }

    public String incluirAtualizarConfigAgenda(String config, boolean mes) throws Exception {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);

            ConfigAgenda configAgenda = new ConfigAgenda();
            if (mes) {
                configAgenda.setMes(config);
            } else {
                configAgenda.setDia(config);
            }
            configAgenda.setUsuario(usuario);
            if (!configAgendaDao.exists(ctx, usuario, "nome")) {
                configAgendaDao.insert(ctx, configAgenda);
            } else {
                configAgendaDao.update(ctx, configAgenda);
            }
            return config;
        } catch (ServiceException e) {
            throw new ServiceException(AgendaExcecoes.ERRO_AO_INSERIR_CONFIG_AGENDA);
        }
    }

    public ConfigAgenda obterConfigAgenda() throws Exception {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            ConfigAgenda configAgenda = new ConfigAgenda();
            configAgenda.setUsuario(usuarioService.obterPorId(ctx, idUsuario));

            String sql = "SELECT obj FROM ConfigAgenda obj WHERE obj.usuario.codigo = :codigoUsuario";
            Map<String, Object> params = new HashMap<>();
            params.put("codigoUsuario", configAgenda.getUsuario().getCodigo());

            return configAgendaDao.findObjectByParam(ctx, sql, params);
        } catch (ServiceException e) {
            throw new ServiceException(AgendaExcecoes.ERRO_AO_OBTER_CONFIG_AGENDA);

        }


    }

    @Override
    public Map<String, List<TurmaResponseDTO>> obterTodasTurmas(String ctx, HttpServletRequest request, Integer empresaId,
                                                                Date dia, PeriodoFiltrarEnum periodo, FiltroTurmaDTO filtro) throws ServiceException {


        if (ctx == null) {
            ctx = sessaoService.getUsuarioAtual().getChave();
        }

        try {
            Map<String, List<TurmaResponseDTO>> ret = new HashMap<>();
            Date dataInicio = Calendario.getDataComHoraZerada(dia);
            Date dataFim = Calendario.getDataComHora(dia, "23:59:59");
            if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
                dataInicio = Calendario.inicioSemanaSegunda(dia);
                dataFim = Calendario.fimSemanaDomingo(dia);
            } else if (periodo.equals(PeriodoFiltrarEnum.MES)) {
                dataInicio = Calendario.inicioMes(dia);
                dataFim = Calendario.fimMes(dia);
            } else if (periodo.equals(PeriodoFiltrarEnum.DIAS15)) {
                dataInicio = Calendario.getDataComHoraZerada(dia);
                dataFim = Calendario.getDataComHora(Uteis.somarDias(dia, 15), "23:59:59");
            }
            for (Date diaPeriodo : Uteis.getDiasEntreDatas(dataInicio, dataFim)) {
                ret.put(Calendario.getData(diaPeriodo, "yyyyMMdd"), new ArrayList<>());
            }
            if (SuperControle.independente(ctx)) {
                ret = obterTodasTurmasTW(request, dataInicio, dataFim, ctx, filtro);
            } else {
                Map<String, AgendaTotalTO> mapaAgendamento = agendaTotalService.montarAgenda(ctx,
                        Calendario.getDataComHoraZerada(dataInicio), Calendario.getDataComHora(dataFim, "23:59:59"),
                        empresaId, 0, filtro.getTipo(), filtro.getSearch(), filtro.getSituacaoHorario());

                for (Map.Entry mapa : mapaAgendamento.entrySet()) {
                    String[] keyDia = mapa.getKey().toString().split("_");
                    AgendaTotalTO agendaTotal = (AgendaTotalTO) mapa.getValue();

                    if (!UteisValidacao.emptyNumber(filtro.getTurmaId()) && !agendaTotal.getCodigoTurma().equals(filtro.getTurmaId())) {
                        continue;
                    }

                    TurmaResponseDTO turma = agendaZWToTurmaResponseDTOSimples(ctx, agendaTotal);

                    if ((!UteisValidacao.emptyList(filtro.getModalidadesIds()) && !containCodigo(filtro.getModalidadesIds(), agendaTotal.getCodigotipo())) ||
                            (!UteisValidacao.emptyList(filtro.getAmbientesIds()) && !containCodigo(filtro.getAmbientesIds(), agendaTotal.getCodigoLocal()))) {
                        continue;
                    }
                    Date diaTurma = Calendario.getDate("dd/MM/yy", keyDia[1]);
                    if (agendaTotal.isSubstituiuProfessor()) {
                        if (!UteisValidacao.emptyList(filtro.getProfessoresIds()) && !containCodigo(filtro.getProfessoresIds(), agendaTotal.getSubstituido().getCodigoProfessorSubstituto())) {
                            continue;
                        }
                    } else {
                        if (!UteisValidacao.emptyList(filtro.getProfessoresIds()) && !containCodigo(filtro.getProfessoresIds(), agendaTotal.getCodigoResponsavel())) {
                            continue;
                        }
                    }

                    if (ret.get(Calendario.getData(diaTurma, "yyyyMMdd")) != null) {
                        ret.get(Calendario.getData(diaTurma, "yyyyMMdd")).add(turma);
                    } else {
                        List<TurmaResponseDTO> turmas = new ArrayList<>();
                        turmas.add(turma);
                        ret.put(Calendario.getData(diaTurma, "yyyyMMdd"), turmas);
                    }
                }
            }
            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if(configuracaoSistema.getValorAsBoolean()){
                List<TurmaResponseDTO> listaValidar = new ArrayList<>();
                for(List<TurmaResponseDTO> turmas : ret.values()){
                    listaValidar.addAll(turmas);
                }
                verificarBloqueados(ctx, dataInicio, dataFim, empresaId, listaValidar);
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_AULA, e);
        }
    }

    private Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados(String ctx,
                                                                      Date inicio,
                                                                      Date fim,
                                                                      Integer empresaId) throws Exception{
        JSONObject retorno = chamadaZW(ctx, "/prest/aulacheia/ambientes-agendados", empresaId, inicio.getTime(), fim.getTime(), null);
        JSONArray content = retorno.optJSONArray("content");
        if(content == null || content.length() == 0){
            return new HashMap<>();
        }
        return new HashMap(){{
            for(int i = 0; i < content.length(); i++){
                AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(content.getJSONObject(i));
                List<AmbienteAgendadoTO> agendados = (ArrayList) get(ambienteAgendadoTO.getAmbiente());
                if(agendados == null){
                    agendados = new ArrayList<>();
                    put(ambienteAgendadoTO.getAmbiente(), agendados);
                }
                agendados.add(ambienteAgendadoTO);
            }
        }};
    }

    private Map<Integer, List<AmbienteAgendadoTO>>  ambientesAgendadosLocacao(String ctx, Date inicio, Date fim, Integer empresaId) throws Exception {
        JSONArray content = new JSONArray();
        StringBuilder sql = new StringBuilder();
        sql.append("select a.codigozw as codigoZwAmbiente, al.codigo as codigoLocacao, al.inicio, al.fim \n");
        sql.append("from agendamentolocacao al \n");
        sql.append("inner join ambiente a on a.codigo = al.ambiente \n");
        sql.append("where al.inicio between '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' ");
        sql.append("and '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd") + " 23:59:59' ");

        try (ResultSet rs =  agendamentoLocacaoDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("dia", inicio.getTime());
                json.put("ambiente", rs.getInt("codigoZwAmbiente"));
                json.put("codigoLocacao", rs.getInt("codigoLocacao"));
                json.put("inicio", rs.getString("inicio"));
                json.put("fim", rs.getString("fim"));
                content.put(json);
            }
        }

        return new HashMap(){{
            for(int i = 0; i < content.length(); i++){
                AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(content.getJSONObject(i), true);
                List<AmbienteAgendadoTO> agendados = (ArrayList) get(ambienteAgendadoTO.getAmbiente());
                if(agendados == null){
                    agendados = new ArrayList<>();
                    put(ambienteAgendadoTO.getAmbiente(), agendados);
                }
                agendados.add(ambienteAgendadoTO);
            }
        }};
    }

    private void verificarBloqueados(String ctx,
                                     Date inicio,
                                     Date fim,
                                     Integer empresaId,
                                     List<TurmaResponseDTO> aulas){
        try {
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados = ambientesAgendados(ctx, inicio, fim, empresaId);
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendadosLocacao = ambientesAgendadosLocacao(ctx, inicio, fim, empresaId);
            if (ambientesAgendados.isEmpty() && ambientesAgendadosLocacao.isEmpty()) {
                return;
            }
            HashSet<String> horarioPai = new HashSet<>();
            Ordenacao.ordenarListaReverse(aulas, "numeroAlunos");
            for (TurmaResponseDTO turma : aulas) {
                List<AmbienteAgendadoTO> ambienteAgendadoTOS = ambientesAgendados.get(turma.getAmbiente().getId());
                if (UteisValidacao.emptyList(ambienteAgendadoTOS)) {
                    continue;
                }
                if (turma.getNumeroAlunos() > 0) {
                    horarioPai.add(turma.getHorarioInicio().concat(" às ").concat(turma.getHorarioFim()).concat(" - ").concat(turma.getAmbiente().getNome()));
                    continue;
                }
                Date horarioInicial = Uteis.getDate(turma.getDia() + turma.getHorarioInicio(), "yyyyMMddHH:mm");
                Date horarioFim = Uteis.getDate(turma.getDia() + turma.getHorarioFim(), "yyyyMMddHH:mm");
                for(AmbienteAgendadoTO aa : ambienteAgendadoTOS){
                    boolean bloquear = !turma.getHorarioTurmaId().equals(aa.getHorarioTurma())
                            && (aulas.size() == 1 || horarioPai.contains(turma.getHorarioInicio().concat(" às ").concat(turma.getHorarioFim()).concat(" - ").concat(turma.getAmbiente().getNome())))
                            && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                            || Calendario.entre(horarioInicial, aa.getInicio(), aa.getFim())
                            || Calendario.entre(horarioFim, aa.getInicio(), aa.getFim())
                            || Calendario.entre( aa.getInicio(), horarioInicial, horarioFim));
                    if(bloquear){
                        turma.setBloqueado(true);
                    }
                }
            }

            for (TurmaResponseDTO turma : aulas) {
                if (!turma.getBloqueado()) {
                    List<AmbienteAgendadoTO> ambienteAgendadoLocacaoTOS = ambientesAgendadosLocacao.get(turma.getAmbiente().getId());
                    if (UteisValidacao.emptyList(ambienteAgendadoLocacaoTOS)) {
                        continue;
                    }
                    Date horarioInicial = Uteis.getDate(turma.getDia() + turma.getHorarioInicio(), "yyyyMMddHH:mm");
                    Date horarioFim = Uteis.getDate(turma.getDia() + turma.getHorarioFim(), "yyyyMMddHH:mm");
                    for (AmbienteAgendadoTO aa : ambienteAgendadoLocacaoTOS) {
                        boolean bloquear = aulas.size() == 1
                                && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                                || Calendario.entreENaoIgual(horarioInicial, aa.getInicio(), aa.getFim())
                                || Calendario.entreENaoIgual(horarioFim, aa.getInicio(), aa.getFim())
                                || Calendario.entreENaoIgual(aa.getInicio(), horarioInicial, horarioFim));
                        if (bloquear) {
                            turma.setBloqueado(true);
                        }
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, AgendaServiceImpl.class);
        }
    }

    public void verificarBloqueados(String ctx,
                                     Long inicio,
                                     Long fim,
                                     Integer empresaId,
                                     List<AgendaTotalJSON> agendamentos){
        try {
            ConfiguracaoSistema cfg = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if(!cfg.getValorAsBoolean()){
                return;
            }
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados = ambientesAgendados(ctx, new Date(inicio), new Date(fim), empresaId);
            if(ambientesAgendados.isEmpty()){
                return;
            }
            for (AgendaTotalJSON agenda : new ArrayList<>(agendamentos)) {
                List<AmbienteAgendadoTO> ambienteAgendadoTOS = ambientesAgendados.get(agenda.getCodigoLocal());
                if (UteisValidacao.emptyList(ambienteAgendadoTOS)) {
                    continue;
                }
                Date horarioInicial = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getInicio());
                Date horarioFim = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getFim());
                for(AmbienteAgendadoTO aa : ambienteAgendadoTOS){
                    if(!Integer.valueOf(agenda.getId()).equals(aa.getHorarioTurma())
                            && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                            || Calendario.entre(horarioInicial, aa.getInicio(), aa.getFim())
                            || Calendario.entre(horarioFim, aa.getInicio(), aa.getFim())
                            || Calendario.entre( aa.getInicio(), horarioInicial, horarioFim))){
                        agendamentos.remove(agenda);
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, AgendaService.class);
        }
    }

    private TurmaResponseDTO agendaZWToTurmaResponseDTOSimples(String ctx, AgendaTotalTO agendaTotal) throws Exception {
        TurmaResponseDTO turma = new TurmaResponseDTO();
        turma.setHorarioTurmaId(Integer.parseInt(agendaTotal.getId()));
        turma.setDia(Calendario.getData(agendaTotal.getStartDate(), "yyyyMMdd"));
        EdicaoAulaTemporaria edicaoAula = aulaServiceImpl.consultarEdicoesAulaTemporaria(
                ctx,
                Integer.valueOf(agendaTotal.getId()),
                agendaTotal.getStartDate()
        );
        if (edicaoAula != null && edicaoAula.getNome() != null && !edicaoAula.getNome().trim().isEmpty()) {
            turma.setNome(edicaoAula.getNome());
        } else {
            turma.setNome(agendaTotal.getTitulo());
        }
        turma.setAulaCheia(agendaTotal.getAulaColetiva());
        turma.setHorarioInicio(agendaTotal.getInicio());
        turma.setHorarioFim(agendaTotal.getFim());
        turma.setAmbiente(new AmbienteResponseTO(agendaTotal.getCodigoLocal(), agendaTotal.getLocal()));
        turma.setModalidade(new ModalidadeSimplesDTO(agendaTotal.getCodigotipo(), agendaTotal.getTipo(), agendaTotal.getCorLinha()));
        if (agendaTotal.isSubstituiuProfessor()) {
            ProfessorSintetico professorSubstituto = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getSubstituido().getCodigoProfessorSubstituto());
            if (professorSubstituto == null) {
                turma.setProfessorSubstituto(new ColaboradorSimplesTO(null, agendaTotal.getCodigoResponsavel(), agendaTotal.getResponsavel(), ""));
            } else {
                turma.setProfessorSubstituto(new ColaboradorSimplesTO(professorSubstituto, SuperControle.independente(ctx)));
            }
            ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getSubstituido().getCodigoProfessorOriginal());
            if (professor == null) {
                turma.setProfessor(new ColaboradorSimplesTO(null, agendaTotal.getSubstituido().getCodigoProfessorOriginal(), agendaTotal.getSubstituido().getNomeProfessorOrigem(), ""));
            } else {
                turma.setProfessor(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
            }
        } else {
            ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getCodigoResponsavel());
            if (professor == null) {
                turma.setProfessor(new ColaboradorSimplesTO(null, agendaTotal.getCodigoResponsavel(), agendaTotal.getResponsavel(), ""));
            } else {
                turma.setProfessor(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
            }
        }
        turma.setNivel(new NivelResponseTO(agendaTotal.getCodigoNivel(), agendaTotal.getNivel()));
        turma.setCapacidade(agendaTotal.getNrVagas());
        turma.setNumeroAlunos(agendaTotal.getNrVagasPreenchidas());
        turma.setPermitirAulaExperimental(agendaTotal.getPermitirAulaExperimental());
        turma.setPermiteAlunoOutraEmpresa(agendaTotal.isPermiteAlunoOutraEmpresa());
        turma.setAulaColetiva(agendaTotal.getAulaColetiva());
        turma.setBloquearMatriculasAcimaLimite(agendaTotal.isBloquearMatriculasAcimaLimite());
        return turma;
    }

    @Override
    public TurmaResponseDTO removerAlunoAula(String ctx, Usuario usuario, Integer empresaId,
                                             Integer matriculaAluno, Integer codigoPassivo, Integer aulaHorarioId,
                                             Date diaAulaHorario,
                                             AlunoVinculoAulaEnum alunoVinculoAula,
                                             String justificativa,
                                             HttpServletRequest request) throws ServiceException {
        return removerAlunoAula(ctx, usuario, empresaId,
                matriculaAluno, codigoPassivo, aulaHorarioId,
                diaAulaHorario,
                alunoVinculoAula,
                justificativa,
                request,
                false, "", 0);
    }

    @Override
    public TurmaResponseDTO removerAlunoAula(String ctx, Usuario usuario, Integer empresaId,
                                             Integer matriculaAluno, Integer codigoPassivo, Integer aulaHorarioId,
                                             Date diaAulaHorario,
                                             AlunoVinculoAulaEnum alunoVinculoAula,
                                             String justificativa,
                                             HttpServletRequest request,
                                             Boolean autorizadoGestaoRede,
                                             String codAcessoAutorizado,
                                             Integer matriculaAutorizado) throws ServiceException {
        try {
            if (UteisValidacao.emptyString(ctx)) {
                ctx = sessaoService.getUsuarioAtual().getChave();
            }
            if (usuario == null) {
                usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            }
            ClienteSintetico cliente = !UteisValidacao.emptyNumber(codigoPassivo) ? null : clienteService.consultarPorMatricula(ctx, matriculaAluno.toString());
            TurmaResponseDTO turmaResponse = new TurmaResponseDTO();
            if (SuperControle.independente(ctx)) {
                agendaTotalService.excluirAlunoAulaCheia(ctx, cliente.getCodigoCliente(), null, aulaHorarioId, diaAulaHorario, OrigemSistemaEnum.AULA_CHEIA,
                        usuario.getCodigo(), empresaId, usuario.getCodigo());
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);
                turmaResponse = aulaToTurmaResponse(
                        ctx,
                        request,
                        aulaHorario.getAula(),
                        aulaHorario,
                        diaAulaHorario
                );
            }
            else if(alunoVinculoAula != null && AlunoVinculoAulaEnum.INTEGRACAO.equals(alunoVinculoAula)){
                JSONObject dados = new JSONObject();
                dados.put("horarioId", aulaHorarioId);
                dados.put("autorizado", matriculaAluno);
                dados.put("usuario",  usuario.getUsuarioZW());
                dados.put("dia", Uteis.getData(diaAulaHorario));

                dados.put("autorizadoGestaoRede", autorizadoGestaoRede);
                dados.put("codAcessoAutorizado", codAcessoAutorizado);
                dados.put("matriculaAutorizado", matriculaAutorizado);

                JSONObject retorno = chamadaZW(ctx, "/prest/confirmar-presenca/remover-aula", empresaId, null, null, dados);
                if (!retorno.get("content").equals("sucesso")) {
                    throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_ALUNO_AULA);
                }
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalTO aulaHr = new AgendaTotalTO(integracaoWS.consultarUmaAula( ctx, Calendario.getData(diaAulaHorario, "dd/MM/yyyy"), aulaHorarioId));
                turmaResponse = turmaUpdated(ctx,empresaId, true, aulaHr, diaAulaHorario.getTime(), request);
            }else {

                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalTO aulaHr = new AgendaTotalTO(integracaoWS.consultarUmaAula( ctx, Calendario.getData(diaAulaHorario, "dd/MM/yyyy"), aulaHorarioId));

                String retorno = "";
                if (aulaHr.getAulaColetiva()) {
                    retorno = agendaTotalService.excluirAlunoAulaCheia(ctx, (!UteisValidacao.emptyNumber(codigoPassivo) ? null : cliente.getCodigoCliente()), codigoPassivo, Integer.valueOf(aulaHr.getId()), aulaHr.getDateInicio(), OrigemSistemaEnum.AULA_CHEIA,
                            usuario.getUsuarioZW(), empresaId, usuario.getCodigo());
                } else {
                    List<AgendadoTO> alunos = new ArrayList<>();
                    alunos.addAll(integracaoWS.consultarReposicoes(ctx, Calendario.getDataComHoraZerada(diaAulaHorario), Calendario.getDataComHora(diaAulaHorario, "23:59:59"), empresaId));
                    alunos.addAll(integracaoWS.consultarAgendados(ctx, Calendario.getDataComHoraZerada(diaAulaHorario), Calendario.getDataComHora(diaAulaHorario, "23:59:59"), empresaId));

                    AgendadoTO alunoASerDesmarcado = new AgendadoTO();
                    for (AgendadoTO aluno : alunos) {
                        if (aulaHr.getId().equals(aluno.getIdAgendamento()) && aluno.getCodigoCliente().equals(cliente.getCodigoCliente())) {
                            alunoASerDesmarcado = aluno;
                        }
                    }
                    boolean desmarcarExperimental = alunoASerDesmarcado.isExperimental() || alunoASerDesmarcado.isDiaria() || alunoASerDesmarcado.isGymPass();
                    if (StringUtils.isBlank(aulaHr.getId())) {
                        throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                    }

                    Integer codigoContrato = alunoASerDesmarcado != null ? alunoASerDesmarcado.getCodigoContrato() : cliente.getCodigoContrato();
                    retorno = agendaTotalService.desmarcarAluno(ctx, cliente.getCodigoCliente(), Integer.valueOf(aulaHr.getId()), aulaHr.getDateInicio(), codigoContrato,
                            OrigemSistemaEnum.AULA_CHEIA, usuario.getUsuarioZW(), empresaId, desmarcarExperimental, justificativa);
                }

                if (retorno.startsWith("ERRO:")) {
                    throw new ServiceException(retorno.replace("ERRO:", ""));
                }

                deletarAlunoAulaSensorIntegracaoSelfloops(ctx, empresaId, aulaHorarioId, cliente.getCodigoCliente(), diaAulaHorario);
                deletarHorarioEquipamentoAluno(ctx, empresaId, aulaHorarioId, cliente.getCodigoCliente(), diaAulaHorario);
                turmaResponse = turmaUpdated(ctx,empresaId, true, aulaHr, diaAulaHorario.getTime(), request);
                if (!UteisValidacao.emptyString(turmaResponse.getMapaEquipamentos())) {
                    turmaResponse.setEquipamentosOcupados(horarioEquipamentoOcupado(ctx, diaAulaHorario, aulaHorarioId));
                }
                try {
                    if(!retorno.toLowerCase().startsWith("erro") && !retorno.toLowerCase().contains("aulaexperimental")){
                        ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
                        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, empresaId);
                        if(!UteisValidacao.emptyString(configGymPass.getCodigoGymPass())){
                            iniciaThreadGympassBooked(ctx, aulaHorarioId, empresaId, diaAulaHorario,  configGymPass);
                        }
                    }
                }catch (Exception e){
                    Uteis.logar(e, AgendaTotalServiceImpl.class);
                }
            }
            try {
                notificacaoAulaAgendadaService.cancelaNotificacaoAulaAgendada(ctx, cliente, turmaResponse.getHorarioTurmaId(), diaAulaHorario);
            } catch (Exception e) {
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }
            return turmaResponse;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_REMOVER_ALUNO_AULA, e);
        }
    }

    public void iniciaThreadGympassBooked(String chave, Integer idHorarioTurma, Integer empresaZW, Date dia, ConfigGymPass configGymPass) {
        try {
            ThreadGympass thread = new ThreadGympass(chave, null, empresaZW, null, TipoThreadGympassEnum.SINCRONIZAR_BOOKED);
            thread.setHorarioTurma(idHorarioTurma);
            thread.setConfigGymPass(configGymPass);
            thread.setDia(dia);
            thread.setDaemon(true);
            thread.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public List<Map<String, Object>> turmasAlunoDia(Integer matriculaAluno, Date dia) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<AgendaTotalJSON> aulasDia = new ArrayList<>();
            if (dia == null) {
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);

                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                String retorno = integracaoWS.consultarProximasAulas( ctx, matriculaAluno, true);
                if (retorno.startsWith("ERRO:")) {
                    throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_TURMA);
                }

                aulasDia.addAll(JSONMapper.getList(new JSONArray(retorno), AgendaTotalJSON.class));
            } else {
                aulasDia.addAll(agendaTotalService.consultarAulasDiaAluno(ctx, matriculaAluno, dia, null));
            }


            List<Map<String, Object>> aulasDiaAlunoRet = new ArrayList<>();

            for (AgendaTotalJSON aula : aulasDia) {
                if (aula.getDia().after(new Date())) {
                    aulasDiaAlunoRet.add(aulaDiaAluno(aula));
                }
            }

            aulasDiaAlunoRet.sort(Comparator.comparing(item -> Long.valueOf((String)item.get("data")), Comparator.nullsLast(Comparator.naturalOrder())));
            return aulasDiaAlunoRet;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_TURMA, e);
        }
    }

    public List<ProdutoZWDTO> produtosZW(Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProdutoZWDTO> produtosRet = new ArrayList<>();
            JSONArray produtosJSON = agendaTotalService.produtosFreePass(ctx, empresaId, true);

            for (int i = 0; i < produtosJSON.length(); i++) {
                JSONObject produto = produtosJSON.getJSONObject(i);
                produtosRet.add(new ProdutoZWDTO(produto.getInt("codigo"), produto.getString("descricao"), TipoProdutoZWEnum.getInstance(produto.getString("tipoProduto"))));
            }
            return produtosRet;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_PRODUTO_ZW, e);
        }
    }

    private boolean containCodigo(List<Integer> listCompare, Integer itemComparado) {
        boolean contem = false;
        for (Integer comparador : listCompare) {
            if (comparador.equals(itemComparado)) {
                contem = true;
            }
        }

        return contem;
    }

    private Map<String, List<TurmaResponseDTO>> obterTodasTurmasTW(HttpServletRequest request, Date dataInicio, Date dataFim, String ctx, FiltroTurmaDTO filtro) throws ServiceException {
        try {
            Map<String, List<TurmaResponseDTO>> ret = new HashMap<>();
            for (Date diaPeriodo : Uteis.getDiasEntreDatas(dataInicio, dataFim)) {
                ret.put(Calendario.getData(diaPeriodo, "yyyyMMdd"), new ArrayList<>());
            }
            List<Date> datas = Uteis.getDiasEntreDatas(dataInicio, dataFim);
            for (Date dia : datas) {
                int dayOfWeek = Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK);
                DiasSemana diasSemana = DiasSemana.getDiaSemanaNumeral(dayOfWeek);
                List<Aula> aulas = aulasDiaSemana(ctx, diasSemana.getCodigo());
                Map<Integer, List<Date>> aulasExcluidas = aulasExcluidas(ctx, dia, dia);
                Map<String, ProfessorSubstituido> professoresSubstituidos = professoresSubstituidos(ctx, dia, dia);
                for (Aula aula : aulas) {

                    if (!UteisValidacao.emptyNumber(filtro.getTurmaId()) && !aula.getCodigo().equals(filtro.getTurmaId())) {
                        continue;
                    }

                    if (Calendario.entre(dia, Calendario.anterior(Calendar.DAY_OF_MONTH, aula.getDataInicio()), Calendario.proximo(Calendar.DAY_OF_MONTH, aula.getDataFim()))) {
                        for (AulaHorario ah : aula.getHorarios()) {
                            TurmaResponseDTO turma;
                            List<Date> excluidas = aulasExcluidas.get(ah.getCodigo());
                            if (excluidas == null || !excluidas.contains(Calendario.getDataComHoraZerada(dia))) {
                                ProfessorSubstituido professorSubstituido = professoresSubstituidos.get(Uteis.getData(dia) + "_" + ah.getCodigo());
                                if (ah.getAtivo()) {
                                    if ((!UteisValidacao.emptyList(filtro.getModalidadesIds()) && !containCodigo(filtro.getModalidadesIds(), ah.getAula().getModalidade().getCodigo())) ||
                                            (!UteisValidacao.emptyList(filtro.getAmbientesIds()) && !containCodigo(filtro.getAmbientesIds(), ah.getAula().getAmbiente().getCodigo()))) {
                                        continue;
                                    }
                                    turma = aulaToTurmaResponse(
                                            ctx,
                                            request,
                                            aula,
                                            ah,
                                            dataInicio
                                    );
                                    if (professorSubstituido != null) {
                                        if (!UteisValidacao.emptyList(filtro.getProfessoresIds()) && !containCodigo(filtro.getProfessoresIds(), professorSubstituido.getCodigoProfessorSubstituto())) {
                                            continue;
                                        }
                                    } else {
                                        if (!UteisValidacao.emptyList(filtro.getProfessoresIds()) && !containCodigo(filtro.getProfessoresIds(), aula.getProfessor().getCodigo())) {
                                            continue;
                                        }
                                    }
                                    if (ret.get(Calendario.getData(dia, "yyyyMMdd")) != null) {
                                        ret.get(Calendario.getData(dia, "yyyyMMdd")).add(turma);
                                        turma.setDia(Calendario.getData(dia, "yyyyMMdd"));
                                    } else {
                                        List<TurmaResponseDTO> turmas = new ArrayList<>();
                                        turmas.add(turma);
                                        ret.put(Calendario.getData(dia, "yyyyMMdd"), turmas);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_BUSCAR_AULA, e);
        }
    }

    private TurmaResponseDTO aulaToTurmaResponse(String ctx, HttpServletRequest request, Aula aula, AulaHorario ah, Date dia) throws Exception {
        TurmaResponseDTO turma = new TurmaResponseDTO();
        Map<String, ProfessorSubstituido> professoresSubstituidos = professoresSubstituidos(ctx, Calendario.getDataComHoraZerada(dia), Calendario.getDataComHora(dia, "23:59:59"));
        ProfessorSubstituido professorSubstituido = professoresSubstituidos.get(Uteis.getData(dia) + "_" + ah.getCodigo());
        List<AlunoResponseTO> alunoResponseTOS = alunosHorarioDia(request, ctx, ah, dia);

        turma.setHorarioTurmaId(ah.getCodigo());
        turma.setHorarioInicio(ah.getInicio());
        turma.setHorarioFim(ah.getFim());
        turma.setNome(aula.getNome());
        turma.setAulaCheia(true);
        turma.setModalidade(new ModalidadeSimplesDTO(aula.getModalidade().getCodigo(), aula.getModalidade().getNome(), aula.getModalidade().getCor().getCor()));
        turma.setAmbiente(new AmbienteResponseTO(aula.getAmbiente().getCodigo(), aula.getAmbiente().getNome()));
        turma.setCapacidade(aula.getCapacidade());
        turma.setDia(Calendario.getData(dia, "yyyyMMdd"));
        turma.setNumeroAlunos(nrAlunosHorarioDia(ctx, ah.getCodigo(), dia));
        if (request != null) {
            aula.getProfessor().setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, aula.getProfessor().getPessoa().getFotoKey(), aula.getProfessor().getCodigoPessoa(), false, ctx, false));
        }
        turma.setProfessor(new ColaboradorSimplesTO(aula.getProfessor(), SuperControle.independente(ctx)));
        if (professorSubstituido != null) {
            ProfessorSintetico professor = professorService.obterPorId(ctx, professorSubstituido.getCodigoProfessorSubstituto());
            professor.setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, professor.getPessoa().getFotoKey(), professor.getCodigoPessoa(), false, ctx, false));
            turma.setProfessor(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
        }
        for (AlunoResponseTO aluno : alunoResponseTOS) {
            turma.getAlunos().add(new AlunoTurmaDTO(aluno));
        }
        return turma;
    }

    public ConfigAgendaDao getConfigAgendaDao() {
        return configAgendaDao;
    }

    public void setConfigAgendaDao(ConfigAgendaDao configAgendaDao) {
        this.configAgendaDao = configAgendaDao;
    }

    public String booking(String ctx, String json, Boolean validando) throws Exception {
        String msgFinal = "";
        String bookingNumber = "";
        try {

            JSONObject jsonBooking = new JSONObject(json);
            String operacao = jsonBooking.optString("event_type");

            BookingGymPass bookingGymPass = new BookingGymPass();
            bookingGymPass.setBooking(json);
            bookingGymPass.setOperacao(operacao);
            try {
                bookingGymPassService.incluir(ctx, bookingGymPass);
            }catch (Exception e){
                System.out.println("ERRO AO INCLUIR O LOG DE BOOKING");
                Uteis.logar(e, AgendaServiceImpl.class);
            }


            JSONObject event_data = jsonBooking.getJSONObject("event_data");
            JSONObject slot = event_data.optJSONObject("slot");
            JSONObject user = event_data.getJSONObject("user");

            if(slot == null){
                JSONObject booking = event_data.getJSONObject("booking");
                bookingGymPass.setBookingNumber(booking.optString("booking_number"));
                bookingNumber = bookingGymPass.getBookingNumber();
                BookingGymPass porBookingId = bookingGymPassService.obterPorBookingId(ctx, bookingNumber);

                JSONObject bookingOLd = new JSONObject(porBookingId.getBooking()).getJSONObject("event_data");
                slot = bookingOLd.optJSONObject("slot");
                user = bookingOLd.getJSONObject("user");

            }

            if (slot != null && slot.has("booking_number")) {
                bookingGymPass.setBookingNumber(slot.optString("booking_number"));
                bookingNumber = bookingGymPass.getBookingNumber();
                if(bookingGymPass.isCancelarBooking()){
                    BookingGymPass porBookingId = bookingGymPassService.obterPorBookingId(ctx, bookingNumber);
                    if (porBookingId == null) {
                        throw new Exception("Booking não encontrado");
                    }
                    JSONObject bookingOLd = new JSONObject(porBookingId.getBooking()).getJSONObject("event_data");
                    slot = bookingOLd.optJSONObject("slot");
                    user = bookingOLd.getJSONObject("user");
                }
                try {
                    bookingGymPassService.alterar(ctx, bookingGymPass);
                }catch (Exception e){
                    System.out.println("ERRO AO ALTERAR O LOG DE BOOKING");
                    Uteis.logar(e, AgendaServiceImpl.class);
                }

            }

            ConfigGymPass configGymPass = configGymPassService.obterPorCodigoGymPass(ctx, String.valueOf(slot.optInt("gym_id")));
            configGymPassService.validarConfiguracaoGympass(configGymPass);

            HorarioGymPass horarioGymPass = null;
            Integer status = 2; //Values: Reserved = 2 or Rejected = 3.
            String msgRejeitado = "";

            Usuario usuario = usuarioService.consultarPorUserName(ctx, "pactobr");
            if (usuario == null) {
                List<Usuario> listaUsuario = usuarioService.consultarPorNome(ctx, "PACTO", 1);
                if (!UteisValidacao.emptyList(listaUsuario)) {
                    usuario = listaUsuario.get(0);
                }

                if (usuario == null) {
                    listaUsuario = usuarioService.obterUsuariosProfessores(ctx, configGymPass.getEmpresa().getCodigo());
                    if (!UteisValidacao.emptyList(listaUsuario)) {
                        usuario = listaUsuario.get(0);
                    }
                }
            }
            if (usuario == null) {
                throw new Exception("Usuário não encontrado");
            }

            try {

                ClienteSintetico clienteSintetico = obterClienteSinteticoBooking(ctx, user, configGymPass);
                if (clienteSintetico == null) {
                    throw new Exception("Cliente não encontrado");
                }

                bookingGymPass.setClienteSintetico(clienteSintetico.getCodigo());
                try {
                    bookingGymPassService.alterar(ctx, bookingGymPass);
                }catch (Exception e){
                    System.out.println("ERRO AO ALTERAR O LOG DE BOOKING");
                    Uteis.logar(e, AgendaServiceImpl.class);
                }

                horarioGymPass = horarioGymPassService.obterPorIdSlotGymPass(ctx, slot.optInt("id"));
                if (horarioGymPass == null) {
                    throw new Exception("Horário Gympass não encontrado");
                }

                if (bookingGymPass.isMarcarBooking()) {
                    AdicionarAlunoTurmaResponseDTO alunoTurmaResponseDTO = adicionarAlunoTurma(ctx, usuario,
                            configGymPass.getEmpresa().getCodZW(),
                            horarioGymPass.getCodigoHorario(), clienteSintetico.getMatricula(), horarioGymPass.getDia(),
                            null, null, null,
                            AcaoAulaTurmaEnum.AULA_EXPERIMENTAL,
                            bookingNumber,
                            Boolean.FALSE,
                            null,
                            null);
                    if (alunoTurmaResponseDTO.getStatus().equals(SituacaoAdicionarAlunoTurmaDTO.SUCESSO)) {
                        status = 2;
                        msgRejeitado = "Aula agendada.";
                    } else {
                        throw new Exception("Não foi possível adicionar o aluno a turma. " + alunoTurmaResponseDTO.getStatus().name());
                    }
                } else if (bookingGymPass.isCancelarBooking() || bookingGymPass.isCancelarBookingAposPeriodo()) {
                    TurmaResponseDTO turmaResponseDTO = removerAlunoAula(ctx, usuario, configGymPass.getEmpresa().getCodZW(), clienteSintetico.getMatricula(), null,
                            horarioGymPass.getCodigoHorario(), horarioGymPass.getDia(), null, null, null);
                    status = 2;
                    msgRejeitado = "Aula cancelada.";
                }

                try{
                    notificarRecursoEmpresa(RecursoSistema.BOOKING_GYMPASS, ctx, clienteSintetico);
                }catch (Exception ignore){}

            } catch (Exception ex) {
                Uteis.logarDebug(" #### [AgendaService.booking] CTX: " + ctx + " - ERRO: " + ex.getMessage());
                ex.printStackTrace();
                status = 3;
                msgRejeitado = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                // se é null, ocorreu nullpointer no treino após o aluno ser inserido na aula no zw
                // se é "null", ocorreu nullpointer no zw
                if (msgRejeitado == null || "null".equalsIgnoreCase(msgRejeitado.trim())) {
                    msgRejeitado = "Não foi possivel incluir o aluno na aula, verifique a sincronização PACTO - GYMPASS";
                    // SE CHEGOU ATÉ AQUI, O ALUNO TEM QUE SER REMOVIDO DA AULA CASO TENHA ENTRADO, POIS VAI RETORNAR REJEIÇÃO
                    // PARA GYMPASS E O ALUNO VAI TENTAR NOVAMENTE MAS NÃO VAI CONSEGUIR POR TER ATIGIDO O LIMITE DE AULA DIÁRIA
                    ClienteSintetico clienteSintetico = obterClienteSinteticoBooking(ctx, user, configGymPass);
                    if (clienteSintetico == null) {
                        throw new Exception("Cliente não encontrado");
                    }

                    try {
                        if (isAlunoHorarioTurmaBookingInserido(ctx, clienteSintetico.getCodigoCliente(), horarioGymPass.getDia(), bookingNumber, horarioGymPass.getCodigoHorario())) {
                            removerAlunoAula(
                                    ctx, usuario, configGymPass.getEmpresa().getCodZW(), clienteSintetico.getMatricula(),
                                    null, horarioGymPass.getCodigoHorario(), horarioGymPass.getDia(),
                                    null, "Remoção de aluno após rejeição de booking", null
                            );
                        }
                    } catch (Exception e) { }
                }
            } finally {
                try {
                    logGymPassService.incluir(ctx, ("BOOKING-" + bookingNumber), msgRejeitado, json);
                }catch (Exception e){
                    System.out.println("ERRO AO INCLUIR O LOG DE BOOKING");
                    Uteis.logar(e, AgendaServiceImpl.class);
                }
            }

            BookingsDTO bookingsDTO = new BookingsDTO();
            bookingsDTO.setClass_id(slot.optInt("class_id"));
            bookingsDTO.setStatus(status);
            bookingsDTO.setReason(msgRejeitado);

            if (horarioGymPass != null) {
                TurmaGymPassJSON turmaGymPassJSON = obterDadosTurmaTurmaGymPassJSON(ctx, horarioGymPass.getIdTurma(), null);
                if (turmaGymPassJSON == null) {
                    throw new Exception("Turma não encontrada.");
                }
                bookingsDTO.setVirtual_class_url(turmaGymPassJSON.getUrlTurmaVirtual());
            }

            if(bookingGymPass.isMarcarBooking() && !validando){
                gymPassBookingService.enviarBooking(bookingsDTO, bookingGymPass.getBookingNumber(), configGymPass);
            }

            bookingGymPass.setSucesso(true);
            try {
                bookingGymPassService.alterar(ctx, bookingGymPass);
            }catch (Exception e){
                System.out.println("ERRO AO INCLUIR O LOG DE BOOKING");
                Uteis.logar(e, AgendaServiceImpl.class);
            }


            msgFinal = status == 3 ? msgRejeitado : "ok";
            return msgFinal;
        } catch (Exception ex) {
            msgFinal = ex.getMessage();
            throw ex;
        } finally {
            logGymPassService.incluir(ctx, ("BOOKING-" + bookingNumber), msgFinal, json);
        }
    }

    private boolean isAlunoHorarioTurmaBookingInserido(String ctx, Integer codigoClienteZw, Date dia, String bookingId, Integer codigoHorarioTurma) throws Exception {
        String sql = "select exists( \n" +
                "   select * from alunohorarioturma a \n" +
                "   where a.bookingid = '" + bookingId + "' \n" +
                "   and a.cliente =  " + codigoClienteZw + " \n" +
                "   and a.dia = '" + Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd") + "' " +
                "   and a.horarioturma = " + codigoHorarioTurma + " \n" +
                ") as existe;";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)
        ) {
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
        }
        return false;
    }

    public void notificarRecursoEmpresa(final RecursoSistema recurso, String ctx, ClienteSintetico clienteSintetico) {
        try {
            Empresa empresa = empresaService.obterPorIdZW(ctx, clienteSintetico.getEmpresa());
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    null,
                    ctx,
                    false,
                    Aplicacao.isTrue(Aplicacao.usarUrlRecursoEmpresa),
                    empresa.getCodZW(),
                    "GYMPASS",
                    empresa.getNome(),
                    "",
                    "",
                    ""
            );
        } catch (Exception e) {
            Logger.getLogger(AgendaServiceImpl.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void iniciaThreadGympassRemoverSlot(String chave, Integer codigoTurma, Integer codigoHorario, Integer empresaZW, Integer empresaTR) {
        try {
            if (SuperControle.independente(chave) && UteisValidacao.emptyNumber(empresaTR)) {
                empresaTR = empresaService.obterEmpresaTreinoIndependente(chave).getCodigo();
            }
            ThreadGympass thread = new ThreadGympass(chave, codigoTurma, empresaZW, empresaTR, TipoThreadGympassEnum.EXCLUIR_HORARIO);
            thread.setHorarioTurma(codigoHorario);
            thread.setDaemon(true);
            thread.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void iniciaThreadGympass(String chave, Integer turmaID, Integer empresaZW, Integer empresaTR,
                                    TipoThreadGympassEnum tipoThreadGympassEnum) {
        try {
            if (SuperControle.independente(chave) && UteisValidacao.emptyNumber(empresaTR)) {
                empresaTR = empresaService.obterEmpresaTreinoIndependente(chave).getCodigo();
            }
            ThreadGympass thread = new ThreadGympass(chave, turmaID, empresaZW, empresaTR, tipoThreadGympassEnum);
            thread.setDaemon(true);
            thread.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String sincronizarTurmaGympassBooked(String ctx, Integer empresaZW, Date dia, Integer horarioAulaId, ConfigGymPass configGymPass) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/dados-booked");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("dia", String.valueOf(dia.getTime())));
        params.add(new BasicNameValuePair("idHorarioTurmaBooked", String.valueOf(horarioAulaId)));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        JSONObject dados = new JSONObject(body);
        String idclassegympass = dados.optString("idclassegympass");
        if(UteisValidacao.emptyString(idclassegympass)){
            return "aula_nao_gympass";
        }
        if(empresaZW == null){
            empresaZW = dados.getInt("empresa");
        }
        Empresa empresa = empresaService.obterPorIdZW(ctx, empresaZW);
        String inicio = obterDataFormatoIntegracao(empresa, Calendario.getDataComHoraZerada(Calendario.hoje()));
        String fim = obterDataFormatoIntegracao(empresa, Calendario.getDataComHora(Calendario.hoje(), "23:59"));
        if(configGymPass == null){
            ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
            configGymPass = configGymPassService.obterPorEmpresaZW(ctx, empresaZW);
            if(UteisValidacao.emptyString(configGymPass.getCodigoGymPass())){
               return "nao_usa_gympass";
            }
        }

        Integer idslot = null;
        boolean atualizado = false;
        try {
            TelegramService.enviarMsg(ctx, "buscar idslot: " + horarioAulaId + "_" + Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd")) ;
            List<HorarioGymPass> horarioGymPass = horarioGymPassService.obterPorIdReferencia(ctx, horarioAulaId + "_" + Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
            for(HorarioGymPass hg : horarioGymPass){
                boolean booked = patchSlots(ctx, Integer.valueOf(idclassegympass), hg.getIdSlotGymPass().toString(),
                        dados.getInt("booked"), configGymPass.getCodigoGymPass());
                if(booked){
                    atualizado = true;
                }
                if(idslot == null || hg.getIdSlotGymPass() > idslot){
                    idslot = hg.getIdSlotGymPass();
                }
            }

        }catch (Exception e){
            TelegramService.enviarMsg(ctx, "ERRO: " + e.getMessage()) ;
        }
        if(atualizado){
            return "ok";
        }
        IntegracaoGymPassBooking gymPassService = new IntegracaoGymPassBooking(configGymPass);
        List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(Integer.valueOf(idclassegympass), inicio, fim);
        String horainicial = dados.getString("horainicial");
        TelegramService.enviarMsg(ctx, "slots encontrados : " + slotsDTOS.size()) ;
        for(SlotsDTO slot : slotsDTOS){
            if(idslot == null){
                Date ocorrencia = Uteis.getDate(slot.getOccur_date().replace("T", " ").replace("Z[UTC]", ""),
                        "yyyy-MM-dd HH:mm:ss");
                String horaInicialSlot = Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(ocorrencia,
                                Calendar.HOUR,
                                -3),
                        "HH:mm");
                if(horainicial.equals(horaInicialSlot)){
                    slot.setTotal_booked(dados.getInt("booked"));
                    gymPassService.patchSlots(ctx, Integer.valueOf(idclassegympass), slot, dados.optString("urlturmavirtual"));
                    atualizado = true;
                }
            } else {
                if(slot.getId() == idslot){
                    slot.setTotal_booked(dados.getInt("booked"));
                    gymPassService.patchSlots(ctx, Integer.valueOf(idclassegympass), slot, dados.optString("urlturmavirtual"));
                    atualizado = true;
                }
            }
        }
        if(!atualizado){
            TelegramService.enviarMsg(ctx, "NÃO FOI ATUALIZADO: " + horarioAulaId + "_" + Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd")) ;
        }
        return "ok";

    }

    public boolean patchSlots(String ctx, Integer class_id, String idSlot,
                           Integer total_booked, String gymPass_id) throws Exception {
        try {
            HttpPatch httpPatch = new HttpPatch("https://api.partners.gympass.com" + String.format("/booking/v1/gyms/%s/classes/%s/slots/%s",
                    gymPass_id.trim(), class_id, idSlot));
            JSONObject dto = new JSONObject();
            dto.put("total_booked", total_booked);
            dto.put("virtual_class_url", "");
            StringEntity entity = new StringEntity(dto.toString(), "UTF-8");
            httpPatch.setEntity(entity);
            httpPatch.setHeader("Content-Type", "application/json");
            httpPatch.setHeader("Authorization", "Bearer " + obterToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPatch);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 204) {
                System.out.println("sincronizado com sucesso!!!");
                try {
                    TelegramService.enviarMsg(ctx, "patchSlots : params: " + dto + " - sucesso ");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return true;
            } else {
                String responseBody = EntityUtils.toString(response.getEntity());
                TelegramService.enviarMsg(ctx,responseBody);
                return false;
            }


        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String obterToken() throws Exception {
        try {

            String data = "grant_type=client_credentials&client_id=pacto&client_secret=f3677289-46ca-4c0f-a270-3aeb262db4af";

            StringEntity entity = new StringEntity(data, "UTF-8");

            String urlTokenGymPass = Aplicacao.getProp(Aplicacao.urlAPIGymPassBookingToken);

            HttpPost httpPost = new HttpPost(urlTokenGymPass);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode != 200) {
                throw new Exception(responseBody);
            }

            JSONObject json = new JSONObject(responseBody);
            if (json.has("access_token")) {
                return json.optString("access_token");
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private String obterDataFormatoIntegracao(Empresa empresa, Date dataConverter) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.obterPorId(empresa.getTimeZoneDefault());
        String gmt = timeZoneEnum.getTimeZoneGmt().replace("GMT-", "");
        String retorno = Uteis.getDataAplicandoFormatacao(dataConverter, "yyyy-MM-dd'T'HH:mm:ss");
        retorno += "-0" + gmt + ":00";
        return retorno;
    }

    public List<Map<String, Object>> horariosSincronizados(String ctx) throws Exception{
        List<Map<String, Object>> horarios = new ArrayList<>();
        try (ResultSet rs = aulaHorarioDao.createStatement(ctx, "select max(iniciohorario) as ultimadata, idturma from HorarioGymPass \n" +
                "where ativo \n" +
                "group by idturma")) {
            while (rs.next()) {
                Integer turma = rs.getInt("idturma");
                Date ultimadata = rs.getDate("ultimadata");
                horarios.add(new HashMap() {{
                    put("turma", turma);
                    put("ultimadata", Uteis.getDataAplicandoFormatacao(ultimadata, "dd/MM/yyyy HH:mm"));
                }});
            }
        }
        return horarios;
    }

    public void sincronizarTurmasGympassBooking(String ctx, Integer empresaZW, Integer empresaTW) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                     "select e.nome as nomeempresa, t.codigo, t.empresa from turma t \n" +
                             " inner join empresa e on e.codigo = t.empresa \n" +
                             "where t.datafinalvigencia > '" +
                             Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd") +
                             "'  and t.produtogympass is not null", conZW)
        ) {
            while (rs.next()) {
                try {
                    System.out.println("vou sincronizar booking " + ctx + " turma " + rs.getInt("codigo"));
                    sincronizarTurmaGympassBooking(ctx, Calendario.hoje(), rs.getInt("empresa"), 0, rs.getInt("codigo"));

                    EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                            RecursoSistema.SINCRONIZADOR_GYMPASS,
                            null,
                            ctx,
                            false,
                            Aplicacao.isTrue(Aplicacao.usarUrlRecursoEmpresa),
                            rs.getInt("empresa"),
                            "Booking Gympass - " + rs.getInt("codigo"),
                            rs.getString("nomeempresa"),
                            "",
                            "",
                            ""
                    );
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AgendaService.class);
        }
    }

    public String sincronizarTurmaGympassBooking(String ctx, Integer empresaZW, Integer empresaTW, Integer turmaId) throws Exception {
        return sincronizarTurmaGympassBooking(ctx, Calendario.hoje(), empresaZW, empresaTW, turmaId);
    }
    public String sincronizarTurmaGympassBooking(String ctx, Date inicio, Integer empresaZW, Integer empresaTW, Integer turmaId) throws Exception {
        String msgRetorno = "";
        String json = "";
        IntegracaoGymPassBooking gymPassService = null;
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(empresaTW)) {
                empresa = empresaService.obterPorId(ctx, empresaTW);
            } else if (!UteisValidacao.emptyNumber(empresaZW)) {
                empresa = empresaService.obterPorIdZW(ctx, empresaZW);
            }

            if (empresa == null) {
                throw new Exception("Não foi possível obter empresa");
            }

            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
            if (!configGymPass.isUsarGymPassBooking()) {
                throw new Exception("Não utiliza GymPass Booking");
            }

            if (UteisValidacao.emptyNumber(empresaZW)) {
                empresaZW = empresa.getCodZW();
            }

            FiltroTurmaDTO filtro = new FiltroTurmaDTO(null);
            filtro.setTipo(TipoAulaCheiaOrigemEnum.TODAS);
            filtro.setTurmaId(turmaId);

            TurmaGymPassJSON turmaGymPassJSON = obterDadosTurmaTurmaGymPassJSON(ctx, filtro.getTurmaId(), null);
            if (turmaGymPassJSON == null) {
                throw new Exception("Turma não encontrada");
            }
            System.out.println(ctx + " - vou sincronizar a aula " + turmaGymPassJSON.getCodigo() + " com a Gympass");
            turmaGymPassJSON.setHorarios(new ArrayList<>());
            gymPassService = new IntegracaoGymPassBooking(configGymPass);
            ClassesDTO classesDTO = gymPassService.obterClasse(turmaGymPassJSON.getIdClasseGymPass());
            if(!classesDTO.isBookable() && !classesDTO.isVisible() && !turmaGymPassJSON.isAtivo()){
                msgRetorno = "Aula inativa Pacto e indisponivel Gympass | idTurma " + turmaGymPassJSON.getCodigo() + " | TurmaGymPass " + turmaGymPassJSON.getIdClasseGymPass();
                return msgRetorno;
            } else {
                if (UteisValidacao.emptyNumber(turmaGymPassJSON.getProdutoGymPass())) {
                    gymPassBookingService.inativarUmaAula(ctx, empresaZW, "ZW-B-" + turmaId, 0);
                    return "ok";
                }
                gymPassBookingService.sincronizarTurma(ctx, empresa, turmaGymPassJSON);
                if (UteisValidacao.emptyNumber(turmaGymPassJSON.getIdClasseGymPass())) {
                    throw new Exception("Turma não foi sincronizada");
                }
                preencherIdClassGympass(ctx, turmaGymPassJSON);
                Map<String, List<TurmaResponseDTO>> map = obterTodasTurmas(ctx, null, empresaZW,
                        inicio, PeriodoFiltrarEnum.DIAS15, filtro);
                for (String dia : map.keySet()) {
                    List<TurmaResponseDTO> listaHorarios = map.get(dia);
                    if (listaHorarios == null || listaHorarios.size() == 0) {
                        continue;
                    }
                    for (TurmaResponseDTO horario : listaHorarios) {
                        HorarioTurmaGymPassJSON horarioJSON = new HorarioTurmaGymPassJSON(horario);
                        if(UteisValidacao.emptyString(horarioJSON.getProfessor())) {
                            atualizarNomeProfessorSinteticoTreino(ctx, horario.getProfessor().getId(), horario.getProfessor().getCodigoColaborador());
                        }
                        if (horarioJSON.getProfessorSubstituto() != null) {
                            if (UteisValidacao.emptyString(horarioJSON.getProfessorSubstituto())) {
                                atualizarNomeProfessorSinteticoTreino(ctx, horario.getProfessor().getId(), horario.getProfessor().getCodigoColaborador());
                            }
                        }
                        turmaGymPassJSON.getHorarios().add(horarioJSON);
                    }
                }
                gymPassBookingService.sincronizarHorarios(ctx, inicio, empresa, turmaGymPassJSON);
                msgRetorno = "Turma sincronizada com GymPass | idTurma " + turmaGymPassJSON.getCodigo() + " | TurmaGymPass " + turmaGymPassJSON.getIdClasseGymPass();
                notificarSincronizacao(ctx, RecursoSistema.SUCESSO_SINC_GYMPASS, empresaZW);
                json = turmaGymPassJSON.toJSON();
                return msgRetorno;
            }
        } catch (Exception ex) {
            msgRetorno = ex.getMessage();
            ex.printStackTrace();
            if(msgRetorno != null && !msgRetorno.equals("Não utiliza GymPass Booking")){
                notificarSincronizacao(ctx, RecursoSistema.ERRO_SINC_GYMPASS, empresaZW);
            }
        } finally {
            logGymPassService.incluir(ctx, turmaId.toString(), msgRetorno, json);
        }
        return msgRetorno;
    }

    private void notificarSincronizacao(String ctx, RecursoSistema recurso, Integer empresaZW){
        try{
            Empresa empresa = empresaService.obterPorIdZW(ctx, empresaZW);
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    null,
                    ctx,
                    false,
                    Aplicacao.isTrue(Aplicacao.usarUrlRecursoEmpresa),
                    empresa.getCodZW(),
                    "GYMPASS",
                    empresa.getNome(),
                    "",
                    "",
                    ""
            );
        }catch (Exception ignore){}
    }

    private ClienteSintetico obterClienteSinteticoBooking(String ctx, JSONObject jsonUser, ConfigGymPass configGymPass) throws Exception {

        String unique_token = jsonUser.optString("unique_token");
        String name = jsonUser.optString("name");
        String email = jsonUser.optString("email");
        String phone_number = jsonUser.optString("phone_number");


        //consultar primeiro local se já existe..
        ClienteSintetico clienteSintetico = clienteService.consultarGympassBooking(ctx, configGymPass.getEmpresa(), unique_token, email);


        //não encontrou o aluno..
        //vou cadastrar ele...
        if (clienteSintetico == null) {
            if (SuperControle.independente(ctx)) {

                AlunoDTO alunoDTO = new AlunoDTO();
                alunoDTO.setNome(name);
                alunoDTO.setSexo(SexoEnum.N);
                alunoDTO.setSituacaoAluno(SituacaoAlunoEnum.VISITANTE.name());
                alunoDTO.setUsarApp(false);

                String telefoneCliente = null;
                if (!UteisValidacao.emptyString(phone_number)) {
                    String numero = phone_number;
                    if (numero.startsWith("+55") || numero.startsWith("55")) {
                        numero = numero.replaceFirst("\\+55", "").replaceFirst("55", "");
                    }
                    telefoneCliente = Uteis.aplicarMascara(numero, "(99) 9999-9999");
                }

                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                telefoneDTO.setNumero(telefoneCliente);

                List<TelefoneDTO> fones = new ArrayList<>();
                fones.add(telefoneDTO);
                alunoDTO.setFones(fones);

                List<String> emails = new ArrayList<>();
                emails.add(email);
                alunoDTO.setEmails(emails);

                List<ProfessorSintetico> listaProfessor = professorService.consultarPorCodigoOuNome(ctx, configGymPass.getEmpresa().getCodigo(), "PACTO");
                if (UteisValidacao.emptyList(listaProfessor)) {
                    listaProfessor = professorService.consultarProfessores(ctx, configGymPass.getEmpresa().getCodigo());
                }

                if (UteisValidacao.emptyList(listaProfessor)) {
                    throw new Exception("Professor não encontrado.");
                }

                alunoDTO.setProfessorId(listaProfessor.get(0).getCodigo());

                AlunoResponseTO responseTO = null;
                try {
                    responseTO = clienteService.cadastrarAluno(ctx, alunoDTO, configGymPass.getEmpresa().getCodigo());
                } catch (Exception ex) {
                    logGymPassService.incluir(ctx, ("GYM-UNIQ-" + unique_token), ex.getMessage(), jsonUser.toString());
                    responseTO = null;
                }

                if (responseTO == null) {
                    throw new Exception("Cliente não cadastrado");
                }

                clienteSintetico = clienteService.obterPorId(ctx, responseTO.getId());

            } else {

                GymPassBookingZWJSON jsonEnvio = new GymPassBookingZWJSON();
                jsonEnvio.setOperacao("consultaAluno");
                jsonEnvio.setChave(ctx);
                jsonEnvio.setEmpresaZw(configGymPass.getEmpresa().getCodZW());
                jsonEnvio.setClienteUniqueToken(unique_token);
                jsonEnvio.setClienteNome(name);
                jsonEnvio.setClienteEmail(email);
                jsonEnvio.setClienteTelefone(phone_number);

                final String urlZW = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWeb);
                StringEntity entity = new StringEntity(new JSONObject(jsonEnvio).toString(), "UTF-8");
                HttpPost httpPost = new HttpPost(urlZW + "/prest/gympassbooking");
                httpPost.setEntity(entity);
                httpPost.setHeader("Content-Type", "application/json");
                HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                HttpResponse response = client.execute(httpPost);

                int statusCode = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity());

                JSONObject jsonRetorno = new JSONObject(responseBody);
                Integer codigoCliente = 0;
                if (jsonRetorno.optBoolean("sucesso")) {
                    String msg = jsonRetorno.optString("msg");
                    if(!UteisValidacao.emptyString(msg) && msg.contains("ERRO:")){
                        throw new Exception(msg);
                    }
                    JSONObject jsonRetornoCliente = new JSONObject(jsonRetorno.optString("msg"));
                    codigoCliente = Integer.parseInt(jsonRetornoCliente.optString("cliente"));
                    clienteSintetico = clienteService.obterPorCodigoCliente(ctx, codigoCliente);
                } else {
                    return null;
                }
            }
        }

        if (clienteSintetico == null) {
            throw new Exception("Cliente não encontrado");
        }

        if (UteisValidacao.emptyString(clienteSintetico.getGympassUniqueToken())) {
            clienteSintetico.setGympassUniqueToken(unique_token);
            clienteSintetico = clienteService.alterarAlgunsCampos(ctx, clienteSintetico, new String[]{"gympassUniqueToken"}, new Object[]{clienteSintetico.getGympassUniqueToken()});
        }

        return clienteSintetico;
    }

    public TurmaGymPassJSON obterDadosTurmaTurmaGymPassJSON(String ctx, Integer turma, Integer produtoGymPass) throws Exception {
        if (SuperControle.independente(ctx)) {
            Empresa empresa = empresaService.obterEmpresaTreinoIndependente(ctx);
            Aula aula = aulaService.obterPorId(ctx, turma);
            if (aula == null) {
                return null;
            } else {
                return new TurmaGymPassJSON(aula, empresa.getCodigo());
            }
        } else {

            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                         "select * from turma t \n" +
                                 "where codigo = " + turma, conZW)
            ){
                if(rs.next()){
                    return new TurmaGymPassJSON(rs);
                }
            }
            return null;
        }
    }

    private void preencherIdClassGympass(String ctx, TurmaGymPassJSON turmaGymPassJSON) throws Exception {
        if (SuperControle.independente(ctx)) {
            Aula aula = aulaService.obterPorId(ctx, turmaGymPassJSON.getCodigo());
            if (aula != null) {
                aula.setIdClasseGymPass(turmaGymPassJSON.getIdClasseGymPass());
                aulaService.alterar(ctx, aula);
            } else {
                throw new Exception("Aula não encontrada");
            }
        } else {

            GymPassBookingZWJSON jsonEnvio = new GymPassBookingZWJSON();
            jsonEnvio.setOperacao("atualizar");
            jsonEnvio.setChave(ctx);
            jsonEnvio.setTurma(turmaGymPassJSON.getCodigo());
            jsonEnvio.setIdClasseGymPass(turmaGymPassJSON.getIdClasseGymPass());

            final String urlZW = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWeb);
            StringEntity entity = new StringEntity(new JSONObject(jsonEnvio).toString(), "UTF-8");
            HttpPost httpPost = new HttpPost(urlZW + "/prest/gympassbooking");
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json");
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            JSONObject jsonRetorno = new JSONObject(responseBody);
            if (!jsonRetorno.optBoolean("sucesso")) {
                throw new Exception(responseBody);
            }
        }
    }

    public String excluirHorariosGympassBooking(String ctx, Integer empresaZW, Integer empresaTW,
                                                Integer turmaId, boolean excluirTodas) throws Exception {

        Empresa empresa = null;
        if (!UteisValidacao.emptyNumber(empresaTW)) {
            empresa = empresaService.obterPorId(ctx, empresaTW);
        } else if (!UteisValidacao.emptyNumber(empresaZW)) {
            empresa = empresaService.obterPorIdZW(ctx, empresaZW);
        }

        if (empresa == null) {
            throw new Exception("Não foi possível obter empresa");
        }

        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        if (!configGymPass.isUsarGymPassBooking()) {
            throw new Exception("Não utiliza GymPass Booking");
        }

        TurmaGymPassJSON turmaGymPassJSON =  null;
        if (!UteisValidacao.emptyNumber(turmaId)) {
            turmaGymPassJSON = obterDadosTurmaTurmaGymPassJSON(ctx, turmaId, null);
        }

        return gymPassBookingService.excluirHorarios(ctx, empresa, excluirTodas, turmaGymPassJSON);
    }


    public String excluirHorarioGympassBooking(String ctx, Integer empresaZW, Integer empresaTW, Integer codigoHorario, Integer codigoTurma) throws Exception {

        Empresa empresa = null;
        if (!UteisValidacao.emptyNumber(empresaTW)) {
            empresa = empresaService.obterPorId(ctx, empresaTW);
        } else if (!UteisValidacao.emptyNumber(empresaZW)) {
            empresa = empresaService.obterPorIdZW(ctx, empresaZW);
        }

        if (empresa == null) {
            throw new Exception("Não foi possível obter empresa");
        }

        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        if (!configGymPass.isUsarGymPassBooking()) {
            throw new Exception("Não utiliza GymPass Booking");
        }

        TurmaGymPassJSON turmaGymPassJSON =  null;
        if (!UteisValidacao.emptyNumber(codigoTurma)) {
            turmaGymPassJSON = obterDadosTurmaTurmaGymPassJSON(ctx, codigoTurma, null);
        }

        return gymPassBookingService.excluirHorarioEspecifico(ctx, empresa, turmaGymPassJSON, codigoHorario, codigoTurma);
    }

    @Override
    public TurmaResponseDTO aulaDetalhada(String key, Integer empresaId,
                                          Integer aulaHorarioId,
                                          Date dia, Boolean consultarClientes, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = key == null ? sessaoService.getUsuarioAtual().getChave() : key;
            Boolean reposicao = false;
            TurmaResponseDTO turmaResponse = new TurmaResponseDTO();
            if (SuperControle.independente(ctx)) {
                AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, aulaHorarioId);
                turmaResponse = aulaToTurmaResponse(
                        ctx,
                        request,
                        aulaHorario.getAula(),
                        aulaHorario,
                        dia
                );
            } else {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                AgendaTotalJSON aula = integracaoWS.consultarUmaAula(ctx, Calendario.getData(dia, "dd/MM/yyyy"), aulaHorarioId);
                if (aula != null) {
                    turmaResponse = turmaUpdated(ctx, empresaId, consultarClientes, new AgendaTotalTO(aula), dia.getTime(), request);
                    try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                         ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                 "select permitefixar from turma t \n" +
                                         " inner join horarioturma ht on ht.turma = t.codigo " +
                                         " where ht.codigo = " + turmaResponse.getHorarioTurmaId(), conZW)
                    ) {
                        if (rs.next()) {
                            turmaResponse.setPermiteFixar(rs.getBoolean("permitefixar"));
                        }
                    }
                } else {
                    if (request.getRequestURI().endsWith("aula-excluida")) {
                        return turmaResponse;
                    }
                    throw new ServiceException(AulaExcecoes.AULA_NAO_ENCONTRADA);
                }
            }
            ConfiguracaoSistema configuracaoSistema = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if(configuracaoSistema.getValorAsBoolean()){
                turmaResponse.setAtualizarAgenda(true);
                List<TurmaResponseDTO> listFake = new ArrayList<>();
                listFake.add(turmaResponse);
                verificarBloqueados(ctx,
                        dia,
                        dia,
                        empresaId,
                        listFake);

                Date horarioInicial = Uteis.getDate(turmaResponse.getDia() + turmaResponse.getHorarioInicio(), "yyyyMMddHH:mm");
                Date horarioFim = Uteis.getDate(turmaResponse.getDia() + turmaResponse.getHorarioFim(), "yyyyMMddHH:mm");
                String query = "SELECT ag.codigo, inicio, fim FROM agendamento ag " +
                        " JOIN horariodisponibilidade hd ON ag.horariodisponibilidade_codigo = hd.codigo" +
                        " JOIN ambiente a ON a.codigozw = " + turmaResponse.getAmbiente().getId() +
                        " WHERE hd.ambiente_codigo = a.codigo" +
                        " AND ag.disponibilidade = false" +
                        " AND (('" + horarioInicial + "' > ag.inicio AND '" + horarioInicial + "' < ag.fim) OR" +
                        " ('" + horarioFim + "' > ag.inicio AND '" + horarioFim + "' <= ag.fim) OR" +
                        " (ag.inicio >= '" + horarioInicial + "' AND ag.inicio < '" + horarioFim + "'))";
                List<Agendamento> agendamentos = new ArrayList<>();

                try (ResultSet rs = horarioDisponibilidadeDao.createStatement(ctx, query)) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                    while (rs.next()) {
                        LocalDateTime localDateTimeInicio = LocalDateTime.parse(rs.getString("inicio"), formatter);
                        LocalDateTime localDateTimeFim = LocalDateTime.parse(rs.getString("fim"), formatter);

                        Agendamento ag = new Agendamento();
                        ag.setCodigo(rs.getInt("codigo"));
                        ag.setInicio(Timestamp.valueOf(localDateTimeInicio));
                        ag.setFim(Timestamp.valueOf(localDateTimeFim));

                        agendamentos.add(ag);
                    }
                }

                if (!agendamentos.isEmpty()) {
                    turmaResponse.setBloqueado(true);
                }
            }
            return turmaResponse;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_ATUALIZAR_ALUNO_AULA, e);
        }
    }

    public String formatTime(String input) {
        StringBuilder formattedTime = new StringBuilder(input);
        formattedTime.insert(2, ':');
        return formattedTime.toString();
    }

    public String replaceHour(String dateTime, String newHour) {
        String[] parts = dateTime.split(" ");
        String date = parts[0];
        String time = parts[1];
        return date + " " + newHour;
    }

    private TurmaResponseDTO agendaZWToTurmaResponseDTO(String ctx, HttpServletRequest request, String keyHorario, AgendaTotalTO agendaTotal,
                                                        Map<String, List<AgendadoTO>> mapaAlunos) throws Exception {
        TurmaResponseDTO turma = new TurmaResponseDTO();
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<Integer> alunosPresentesId = integracaoWS.alunosPresentes( ctx, Integer.valueOf(agendaTotal.getId()), agendaTotal.getDateInicio());
        turma.setHorarioTurmaId(Integer.parseInt(agendaTotal.getId()));
        turma.setDia(Calendario.getData(agendaTotal.getStartDate(), "yyyyMMdd"));
        EdicaoAulaTemporaria edicaoAula = aulaServiceImpl.consultarEdicoesAulaTemporaria(
                ctx,
                Integer.valueOf(agendaTotal.getId()),
                agendaTotal.getStartDate()
        );
        if (edicaoAula != null && edicaoAula.getNome() != null && !edicaoAula.getNome().trim().isEmpty()) {
            turma.setNome(edicaoAula.getNome());
        } else {
            turma.setNome(agendaTotal.getTitulo());
        }
        turma.setAulaCheia(agendaTotal.getAulaColetiva());
        turma.setHorarioInicio(agendaTotal.getInicio());
        turma.setHorarioFim(agendaTotal.getFim());
        turma.setAmbiente(new AmbienteResponseTO(agendaTotal.getCodigoLocal(), agendaTotal.getLocal()));
        turma.setModalidade(new ModalidadeSimplesDTO(agendaTotal.getCodigotipo(), agendaTotal.getTipo(), agendaTotal.getCorLinha()));
        turma.setBloquearMatriculasAcimaLimite(agendaTotal.isBloquearMatriculasAcimaLimite());
        if (agendaTotal.isSubstituiuProfessor()) {
            ProfessorSintetico professorSubstituto = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getSubstituido().getCodigoProfessorSubstituto());
            if (professorSubstituto == null) {
                turma.setProfessorSubstituto(new ColaboradorSimplesTO(null, agendaTotal.getCodigoResponsavel(), agendaTotal.getResponsavel(), ""));
            } else {
                professorSubstituto.setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, professorSubstituto.getPessoa().getFotoKey(), professorSubstituto.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                turma.setProfessorSubstituto(new ColaboradorSimplesTO(professorSubstituto, SuperControle.independente(ctx)));
            }
            ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getSubstituido().getCodigoProfessorOriginal());
            if (professor == null) {
                turma.setProfessor(new ColaboradorSimplesTO(null, agendaTotal.getSubstituido().getCodigoProfessorOriginal(), agendaTotal.getSubstituido().getNomeProfessorOrigem(), ""));
            } else {
                professor.setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, professor.getPessoa().getFotoKey(), professor.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                turma.setProfessor(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
            }
        } else {
            ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(ctx, agendaTotal.getCodigoResponsavel());
            if (professor == null) {
                turma.setProfessor(new ColaboradorSimplesTO(null, agendaTotal.getCodigoResponsavel(), agendaTotal.getResponsavel(), ""));
            } else {
                professor.setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, professor.getPessoa().getFotoKey(), professor.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                turma.setProfessor(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
            }
        }
        turma.setNivel(new NivelResponseTO(agendaTotal.getCodigoNivel(), agendaTotal.getNivel()));
        turma.setCapacidade(agendaTotal.getNrVagas());
        turma.setNumeroAlunos(agendaTotal.getNrVagasPreenchidas());
        turma.setPermitirAulaExperimental(agendaTotal.getPermitirAulaExperimental());
        turma.setPermiteAlunoOutraEmpresa(agendaTotal.isPermiteAlunoOutraEmpresa());

        List<MapaEquipamentoAparelhoDTO> listaMapa = aulaServiceImpl.montarListaEquipamentoAparelho(ctx, agendaTotal.getCodigoTurma(), agendaTotal.getMapaEquipamentos());
        turma.setMapaEquipamentos(agendaTotal.getMapaEquipamentos());
        turma.setListaMapaEquipamentoAparelho(listaMapa);

        if(!UteisValidacao.emptyString(turma.getMapaEquipamentos())) {
            turma.setEquipamentosOcupados(horarioEquipamentoOcupado(ctx, agendaTotal.getStartDate(), turma.getHorarioTurmaId()));
        }

        if(mapaAlunos.get(keyHorario) == null){
            return turma;
        }
        List<Integer> dependentes = new ArrayList<>();
        try {
            AgendaModoBDServiceImpl agendaAulasService;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
                dependentes = agendaAulasService.dependentes(mapaAlunos.get(keyHorario));
            }
        }catch (Exception e){
            Uteis.logar(e, AgendaServiceImpl.class);
        }

        for (AgendadoTO aluno : mapaAlunos.get(keyHorario)) {
            AlunoTurmaDTO alunoTurma = new AlunoTurmaDTO();
            alunoTurma.setId(aluno.getCodigoCliente());
            alunoTurma.setNome(aluno.getNome());
            Boolean autorizado = aluno.getMatricula() != null && aluno.getMatricula().startsWith("AUT");
            if(autorizado){
                alunoTurma.setMatriculaZW(aluno.getCodigoCliente());
            } else if (aluno.getMatricula() != null) {
                alunoTurma.setMatriculaZW(Integer.valueOf(aluno.getMatricula()));
            }
            alunoTurma.setJustificativa(aluno.getJustificativa());

            alunoTurma.setSituacaoContrato(SituacaoContratoZWEnum.getInstance(aluno.getSituacaoContrato()));
            alunoTurma.setSituacaoAluno(SituacaoAlunoEnum.getInstance(aluno.getSituacao()));
            ClienteSintetico cliente = autorizado ? null : clienteDao.consultarSimplificadoPorCodigoCliente(ctx, aluno.getCodigoCliente());
            if (cliente != null
                    && !UteisValidacao.emptyString(cliente.getPessoa().getFotoKey())
                    && !"fotoPadrao.jpg".equals(cliente.getPessoa().getFotoKey())) {
                alunoTurma.setImageUri(Aplicacao.obterUrlFotoDaNuvem(cliente.getPessoa().getFotoKey()));
            }
            if (aluno.isDesmarcado()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DESMARCADO);
            } else if (aluno.isDiaria() && !aluno.isGymPass()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA);
            } else if (aluno.isGymPass()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA_GYMPASS);
            } else if (aluno.isTotalPass()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DIARIA_TOTALPASS);
            } else if (aluno.isVisitante()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.VISITANTE);
            } else if (aluno.getDesafio()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DESAFIO);
            } else if (aluno.isReposicao() && !aluno.getUsaSaldo() && !aluno.getExperimental()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.REPOSICAO);
            } else if (aluno.isReposicao() && aluno.getUsaSaldo() && !aluno.getExperimental()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.MARCACAO);
            } else if (aluno.isExperimental() && !aluno.getDesafio() && !aluno.isGymPass()) {
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.AULA_EXPERIMENTAL);
            } else if(dependentes.contains(aluno.getCodigoCliente())){
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.DEPENDENTE);
            }
            if(autorizado){
                alunoTurma.setVinculoComAula(AlunoVinculoAulaEnum.INTEGRACAO);
                alunoTurma.setUnidade(aluno.getCidade());
            }
            alunoTurma.setConfirmado(alunosPresentesId.contains(aluno.getCodigoCliente()) || aluno.isConfirmado());
            turma.getAlunos().add(alunoTurma);
        }
        turma.setAlunos(new ArrayList<>(Ordenacao.ordenarLista(turma.getAlunos(), "nome")));

        return turma;
    }

    public String horarioEquipamentoOcupado(String ctx, Date dia,
                                            Integer horarioTurma) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                String equipamentosOcupados = "";
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        "select aht.cliente, p.nome from alunohorarioturma aht" +
                                " inner join cliente c on c.codigo = aht.cliente" +
                                " inner join pessoa p on p.codigo = c.pessoa " +
                                " where aht.horarioturma = " + horarioTurma +
                                " and aht.dia = '" + Calendario.getData(dia, "dd/MM/yyyy")+"'", conZW)) {
                    while (rs.next()) {
                        try (ResultSet rs2 = ConexaoZWServiceImpl.criarConsulta(
                                "select equipamento from horarioequipamentoaluno where horarioturma = " + horarioTurma +
                                        " and cliente = " + rs.getInt("cliente") + " and diaAula = '" + Calendario.getData(dia, "dd/MM/yyyy")+"'", conZW)) {
                            if (rs2.next()) {
                                equipamentosOcupados = equipamentosOcupados + ";" + rs2.getString("equipamento")+"&"+rs.getString("nome");
                            }
                        }
                    }
                }

                return equipamentosOcupados;
            }
        } catch (Exception e) {
            System.out.println("Erro alunosAula");
            Uteis.logar(e, AlunosAulasServiceImpl.class);
            throw new ServiceException(e.getMessage());
        }
    }

    public String horarioEquipamento(String ctx, Integer aulaId, Integer empresa) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        " select mapaEquipamentos from turma where codigo = " + aulaId + " and empresa = " + empresa, conZW)) {
                    while (rs.next()) {
                        return rs.getString("mapaEquipamentos");
                    }
                }

                return "";
            }
        } catch (Exception e) {
            System.out.println("Erro buscar mapa equipamentos");
            Uteis.logar(e, AgendaServiceImpl.class);
            throw new ServiceException(e.getMessage());
        }
    }

    private void atualizarNomeProfessorSinteticoTreino(String ctx,Integer codigoProfessor, Integer codigoColaborador) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                        "select p.nome from colaborador c inner join pessoa p on c.pessoa = p.codigo where c.codigo =" + codigoColaborador, conZW);
                while (rs.next()) {
                    ProfessorSintetico professorSintetico = professorService.obterPorId(ctx,codigoProfessor);
                    professorSintetico.setNome(rs.getString("nome"));
                    professorService.alterar(ctx, professorSintetico);
                }
            } catch (Exception e) {
                Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
            }
        }

    }

    public Map<Integer, EdicaoAulaTemporaria> obterEdicoesTemporarias(String ctx, Date inicio, Date fim) throws Exception {
        Map<Integer, EdicaoAulaTemporaria> mapaEds = new HashMap<>();
        List<EdicaoAulaTemporaria> edicoes = aulaService.consultarEdicoesAulaTemporariaPeriodo(ctx, inicio, fim);
        for (EdicaoAulaTemporaria ed : edicoes) {
            mapaEds.put(ed.getHorarioTurma(), ed);
        }
        return mapaEds;
    }

    public List<Map<String, Object>> listarAlunosFaltosos(PaginadorDTO paginadorDTO, Integer empresaId, FiltroAlunosFaltososDTO filtro) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<Map<String, Object>> listaRet = new ArrayList<>();

        int maxResults = 50;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            StringBuilder sql = new StringBuilder();

            sql.append("SELECT aht.dia AS dataFalta, ");
            sql.append("m.nome AS nomeModalidade, ");
            sql.append("p.nome AS nomeAluno, ");
            sql.append("t.identificador AS nomeTurma, ");
            sql.append("ht.codigo AS codigoHorarioTurma ");
            sql.append("FROM alunohorarioturma aht ");
            sql.append("JOIN horarioturma ht ON aht.horarioturma = ht.codigo ");
            sql.append("JOIN turma t ON ht.turma = t.codigo ");
            sql.append("JOIN cliente c ON aht.cliente = c.codigo ");
            sql.append("JOIN pessoa p ON c.pessoa = p.codigo ");
            sql.append("JOIN modalidade m ON t.modalidade = m.codigo ");
            sql.append("WHERE NOT EXISTS ( ");
            sql.append("  SELECT 1 FROM aulaconfirmada ac ");
            sql.append("  WHERE ac.cliente = aht.cliente ");
            sql.append("  AND ac.horario = aht.horarioturma ");
            sql.append("  AND ac.diaaula = aht.dia ");
            sql.append(") ");
            sql.append("AND NOT EXISTS ( ");
            sql.append("  SELECT 1 FROM periodoacessocliente pa ");
            sql.append("  WHERE pa.pessoa = c.pessoa ");
            sql.append("  AND pa.datainicioacesso = aht.dia ");
            sql.append(") ");
            sql.append( "AND aht.dia <=now() ");
            if (filtro.getParametro() != null && !filtro.getParametro().trim().equals("")) {
                sql.append(" and upper(p.nome) like '%").append(filtro.getParametro().toUpperCase()).append("%'");
            }
            if(filtro.getDataInicio() !=null && filtro.getDataFim()==null){
                sql.append(" and  aht.dia = '"+ Uteis.getDataAplicandoFormatacao(filtro.getDataInicio(), "yyyy-MM-dd") + "' ");
            }else{
                if(filtro.getDataInicio() !=null && filtro.getDataFim() !=null){
                    sql.append(" and  aht.dia between'"+ Uteis.getDataAplicandoFormatacao(filtro.getDataInicio(), "yyyy-MM-dd") + "' and '"+Uteis.getDataAplicandoFormatacao(filtro.getDataFim(), "yyyy-MM-dd")+"' ");
                }
            }


            if(paginadorDTO.getSQLOrderBy().equals("")){
                sql.append(" ORDER BY nomeAluno ASC ");
            }else{
                sql.append(paginadorDTO.getSQLOrderBy());
            }

            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);

            ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString().concat(" limit ").concat(String.valueOf(maxResults))
                    .concat(" offset ").concat(String.valueOf(indiceInicial)), conZW);
            while (rs.next()) {
                Map<String, Object> alunoFaltoso = new HashMap<>();
                alunoFaltoso.put("dataFalta", Uteis.getDataAplicandoFormatacao(rs.getDate("dataFalta"), "dd/MM/yyyy"));
                alunoFaltoso.put("nomeModalidade", rs.getString("nomeModalidade"));
                alunoFaltoso.put("nomeAluno", rs.getString("nomeAluno"));
                alunoFaltoso.put("nomeTurma", rs.getString("nomeTurma"));
                alunoFaltoso.put("codigoHorarioTurma", rs.getInt("codigoHorarioTurma"));

                listaRet.add(alunoFaltoso);
            }

            if (paginadorDTO != null) {
                try (ResultSet rsCount = ConexaoZWServiceImpl.criarConsulta(
                        "select COUNT(*) as cont from ( " + sql + ") as f", conZW)) {
                    if (rsCount.next()) {
                        paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                    } else {
                        paginadorDTO.setQuantidadeTotalElementos(10l);
                    }
                }
            }

        } catch (Exception e) {
            throw new ServiceException("Erro ao listar alunos faltosos", e);
        }

        return listaRet;
    }

    @Override
    public void validarHorarioEquipamento(String ctx, Integer empresaId, Integer aulaHorarioId, String equipamento, Usuario u, String dia) throws Exception {
        if (u == null || u.getCliente() == null) {
            throw new ServiceException("Usuário do cliente não encontrado");
        }
        Date date = Calendario.getDate("yyyyMMdd", dia);
        String dataFormatada = Calendario.getData(date, "dd/MM/yyyy");

        List<AulaAlunoDTO> aulaAlunoDTOS = ps.consultarAulasAgendadasPorAluno(u.getCliente().getMatricula(), dataFormatada, dataFormatada, ctx, null);

        boolean alunoMatriculado = false;
        for (AulaAlunoDTO aulaAlunoDTO : aulaAlunoDTOS) {
            if (aulaAlunoDTO.getCodigoHorarioTurma().equals(aulaHorarioId)) {
                alunoMatriculado = true;
                break;
            }
        }

        if (!alunoMatriculado) {
            throw new ServiceException("Aluno não está matriculado na aula");
        }

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select mapaequipamentos from turma t join horarioturma ht on t.codigo = ht.turma where ht.codigo = " + aulaHorarioId, conZW)) {
                if (rs.next()) {
                    String mapaEquipamentos = rs.getString("mapaequipamentos");
                    if(!mapaEquipamentos.contains(equipamento)){
                        throw new ServiceException("Equipamento não permitido para a aula");
                    }
                }
            }
        }

    }

    public JSONArray obterAgendamentoAvaliacaoDia(String ctx, String dia, Boolean confirmado, Integer empresaZw) throws Exception {
        JSONArray array = new JSONArray();
        StringBuilder query = new StringBuilder();
        HashMap<String, Object> p = new HashMap<>();
        query.append("SELECT obj from Agendamento obj ").append("\n");
        query.append("INNER JOIN obj.tipoEvento te ").append("\n");
        query.append("WHERE ((:horaInicial between obj.inicio and obj.fim ) OR (:horaFinal between obj.inicio and obj.fim) OR (:horaInicial > obj.inicio and :horaFinal < obj.fim) OR (:horaInicial < obj.inicio and :horaFinal > obj.fim))").append("\n");
        query.append("AND te.comportamento = :comportamento").append("\n");
        query.append("AND obj.cliente.codigo is not null ").append("\n");
        query.append("AND obj.cliente.empresa = :empZw ").append("\n");
        if (confirmado != null && confirmado) {
            query.append("AND obj.status = :status ").append("\n");
            p.put("status", StatusAgendamentoEnum.EXECUTADO);
        }
        p.put("horaInicial", Uteis.getDataJDBCTimestamp(Uteis.getDate(dia + " 00:00:00", "yyyy-MM-dd HH:mm:ss")));
        p.put("horaFinal", Uteis.getDataJDBCTimestamp(Uteis.getDate(dia + " 23:59:59", "yyyy-MM-dd HH:mm:ss")));
        p.put("comportamento", TipoAgendamentoEnum.AVALIACAO_FISICA);
        p.put("empZw", empresaZw);

        List<Agendamento> agendamentos = agendamentoService.obterPorParam(ctx, query.toString(), p);
        if (agendamentos != null) {
            for (Agendamento ag : agendamentos) {
                JSONObject obj = new JSONObject();
                obj.put("codigoCliente", ag.getCliente().getCodigoCliente());
                array.put(obj);
            }
        }
        return array;
    }

    public JSONArray obterAgendamentoAvaliacaoDiaConfirmadoUmaVezAno(String ctx, String dia, Integer empresaZw) throws Exception {
        JSONArray array = new JSONArray();
        StringBuilder query = new StringBuilder();
        HashMap<String, Object> p = new HashMap<>();
        query.append("SELECT obj FROM Agendamento obj").append("\n");
        query.append("INNER JOIN obj.tipoEvento te ").append("\n");
        query.append("WHERE ((:horaInicial between obj.inicio and obj.fim ) OR (:horaFinal between obj.inicio and obj.fim) OR (:horaInicial > obj.inicio and :horaFinal < obj.fim) OR (:horaInicial < obj.inicio and :horaFinal > obj.fim))").append("\n");
        query.append("AND te.comportamento = :comportamento").append("\n");
        query.append("AND obj.cliente.codigo is not null ").append("\n");
        query.append("AND obj.cliente.empresa = :empZw ").append("\n");
        query.append("AND obj.status = :status ").append("\n");
        query.append("AND obj.inicio = ( ").append("\n");
        query.append("SELECT MIN(a.inicio) FROM Agendamento a").append("\n");
        query.append("WHERE a.cliente.codigo = obj.cliente.codigo").append("\n");
        query.append("AND a.status = :status ").append("\n");
        query.append("AND DATE_PART('year', a.inicio) = :ano)").append("\n");
        query.append("ORDER BY obj.cliente.codigo, obj.inicio ASC").append("\n");

        p.put("status", StatusAgendamentoEnum.EXECUTADO);
        p.put("horaInicial", Uteis.getDataJDBCTimestamp(Uteis.getDate(Uteis.getAnoData(Uteis.getDate(dia, "yyyy-MM-dd"))+"-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss")));
        p.put("horaFinal", Uteis.getDataJDBCTimestamp(Uteis.getDate(Uteis.getAnoData(Uteis.getDate(dia, "yyyy-MM-dd"))+"-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss")));
        p.put("comportamento", TipoAgendamentoEnum.AVALIACAO_FISICA);
        p.put("ano", Uteis.getAnoData(Uteis.getDate(dia, "yyyy-MM-dd")));
        p.put("empZw", empresaZw);

        List<Agendamento> agendamentos = agendamentoService.obterPorParam(ctx, query.toString(), p);
        if (agendamentos != null) {
            for (Agendamento ag : agendamentos) {
                JSONObject obj = new JSONObject();
                obj.put("codigoCliente", ag.getCliente().getCodigoCliente());
                obj.put("dataAgendamento", Uteis.getData(ag.getInicio()));
                array.put(obj);
            }
        }
        return array;
    }

}
