/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.bi.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.bean.gestao.ItemGrupoIndicadores;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.gestao.TreinoRealizadoAppDTO;
import br.com.pacto.controller.json.gestao.TreinosExecutadosEAcessosPorDiaVO;
import br.com.pacto.controller.json.gestao.AvaliacaoTreinoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.service.exception.ServiceException;
import java.util.Date;
import java.util.List;

import org.json.JSONObject;
import servicos.integracao.zw.json.ClienteBIJSON;
import servicos.integracao.zw.json.ModalidadeJSON;

/**
 *
 * <AUTHOR>
 */
public interface DashboardBIService {

    static final String SERVICE_NAME = "DashboardBIService";

    DashboardBI processarGestao(final String key, final Integer professor, final Date dataProcessamento, final FiltrosDashboard filtros, final boolean processarDadosZW, boolean treinoIndependente, int codEmpresa) throws ServiceException;

    List<DiasSemanaDashboardBI> obterDias(final String key, final Integer ano, final Integer mes, final Integer professor, final Integer empresa) throws Exception;

    DashboardBI obterBI(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final Integer empresa) throws Exception;

    DashboardBI obterBIGeral(final String key, final Integer professor, final Integer empresa) throws Exception;

    DashboardBI obterBIGeral(final String key, final Integer professor, final Integer empresa, Integer maxResults) throws Exception;

    boolean existeBI(final String key, final Integer dia, final Integer mes, final Integer ano, final Integer professor, final Integer empresa) throws Exception;

    void processarAlunosTreinos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, boolean semTreino) throws Exception;

    List<TreinoRealizado> obterListaTreinos(final String key, final Integer professor,
            final FiltrosDashboard filtros, DashboardBI dash, Date fim) throws Exception;

    List<ClienteSintetico> consultarClientesRenovaramOuNao(final String key, DashboardBI dash, final Integer professor,
            FiltrosDashboard filtros, Boolean naoRenovou) throws Exception;

    List<ClienteBIJSON> consultarClientesAtivosForaTreino(final String key, final Integer codigoEmpresa,
            final List<ModalidadeJSON> modalidades)throws Exception;

    List<ClienteBIJSON> consultarClientesForaTreino(final String key, final Integer codigoEmpresa)throws Exception;

    List<ModalidadeJSON> consultarModalidadesClientesAtivosForaTreino(final String key, final Integer codigoEmpresa)throws Exception;

    MicroCharts montarMicroCharts(final String key, final Integer professor,
            final FiltrosDashboard filtros) throws Exception;

    DashboardBI obterUltimoBI(final String key, final Integer professor, final Integer empresa) throws ServiceException;

    List<ClienteSintetico> consultarAlunos(final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception;

    List<ClienteSintetico> consultarAlunosTreino(final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception;

    List<TreinoRealizado> consultarTreinos(final String key, DashboardBI dash, IndicadorDashboardEnum indicador) throws Exception;

    ItemMediaBI obterItemMediaTreino(final String key, final Integer professor, boolean maior) throws Exception;

    List<ClienteSintetico> preencherDataPrograma(final String key, List<ClienteSintetico> clientes) throws ServiceException;

    List<TipoEventoDisponibilidadeBI> obterTipos(final String key, final Integer ano, final Integer mes, final Integer professor, final Integer empresa) throws Exception;

    List<AcessosExecucoesBI> obterAcessos(final String key, final Date limite, final Integer professor,
            final Integer empresa) throws Exception;

    List<TreinoRealizado> obterListaExecucoes(final String key, boolean smartphone, Date dia, DashboardBI dash) throws Exception;

    List<AcessoBIJSON> obterListaAcesso(final String key,Date dia, DashboardBI dash, Boolean apenasTreino) throws Exception;

    List<ProfessorSintetico> obterProfessoresAgenda(final String key,
                                                    DashboardBI dash,
                                                    final FiltrosDashboard filtros,
                                                    final Date dia,
                                                    PaginadorDTO paginadorDTO,
                                                    String filter,
                                                    Date inicio,
                                                    Date fim) throws Exception;

    List<Agendamento> obterAgendamentos(final String key, final Integer professor,
                                               DashboardBI dash,
                                               Date inicio,
                                               Date fim,
                                               Boolean disponibilidade,
            TipoAgendamentoEnum tipo, StatusAgendamentoEnum status, PaginadorDTO paginadorDTO, String filter) throws Exception;

    List<ProgramaTreino> obterTreinosAgenda(final String key, final Integer professor,
            DashboardBI dash, Date inicio, Date fim, boolean renovados, PaginadorDTO paginadorDTO, String filter) throws Exception;

    List<HistoricoRevisaoProgramaTreino> obterRevisoes(final String key, final Integer professor,
            DashboardBI dash, Date inicio, Date fim, PaginadorDTO paginadorDTO, String filter) throws Exception;

    List<DashboardBI> obterListaParaGrafico(final String ctx, final Integer empresa, final Integer professor) throws ServiceException;

    void gravarGrupoIndicador(String ctx, String nome, List<ItemGraficoTO> itens, List<String> profs) throws ServiceException;

    void excluirGrupo(final String ctx, final String nome) throws ServiceException;

    List<ItemGrupoIndicadores> obterGruposIndicadores(final String ctx) throws ServiceException;

   ConfiguracaoRankingProfessores gravarCfgRanking(final String ctx, final ConfiguracaoRankingProfessores cfg) throws ServiceException;

   void excluirCfgRanking(final String ctx, final ConfiguracaoRankingProfessores cfg) throws ServiceException;

   List<ConfiguracaoRankingProfessores> obterCfgRanking(final String ctx, PaginadorDTO paginadorDTO, final Integer empresa, JSONObject filters, boolean apenasAtivas) throws ServiceException;

    void processarBIProfessores(String chave, Date data) throws ServiceException;

    List<DashboardBI> obterBIsPeriodo(final String key, final Integer professor, final Integer empresa,final String mesAnoInicio,final String mesAnofim) throws ServiceException ;

    void ajustarAcessosExecucoesBI(String chave, Date data) throws ServiceException;

    void importarAlunosForaTreino(String chave, Integer empresa) throws ServiceException;

    void processarRankingBIProfessoresEmpresa(String chave, Date data, Integer empresa) throws ServiceException;

    void processarAgenda(final String key, final Integer professor,
                         DashboardBI dash,
                         Date inicio,
                         Date fim,
                         Integer empresa, Date dataProcessamento) throws Exception;

    public void processarAlunosAtivosSemTreino(String ctx, Integer codigoProfessor, FiltrosDashboard filtros, DashboardBI dashboardBI) throws Exception;

    List<AvaliacaoTreinoDTO> obterAvaliacoesTreino(final String key, Integer idProfessor, DashboardBI dash, Date inicio, Date fim, PaginadorDTO paginadorDTO, String filter, Integer empresaId, IndicadorDashboardEnum indicadorSelecionado) throws Exception;

    void importarAlunosForaTreinoAll(final String chave, final Integer empresa) throws ServiceException;

    String importarAlunoForaTreino(final String chave, final Integer empresa, final Integer matricula) throws Exception;

    void replicarTreinoEmCasa(final String chave, final String documentKey) throws ServiceException;

    List<ClienteSintetico> consultarAlunosTreinoPrograma(
            final String key, DashboardBI dash, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, Integer idProfessor) throws Exception;

    void atualizarHorariosGympass(String ctx, Integer empresaId);

    Integer obterNumeroProgramasAgendamento(final String key, final Integer professor,
                                            Integer codEmpresaZw, Date inicio, Date fim, TipoAgendamentoEnum tipo) throws Exception;

    List<IndicadorRanking> reloadIndicadores(final String ctx) throws ServiceException;

    List<TreinoRealizadoAppDTO> obterTreinosRealizadosPeloAppAgrupandoPorDiaDaSemana(String ctx, Date diaLimite, Integer idProfessor, Integer empresaId) throws Exception;

    List<TreinosExecutadosEAcessosPorDiaVO> obterTreinosRealizadosAgrupandoPorDiaDaSemana(String ctx, Date dataInicio, Integer matricula) throws Exception;

    List<AlunoSimplesDTO> consultarAlunosTreinoPrograma(String ctx, Date diaLimite, Integer empresa, Integer dia, String periodo, JSONObject filters, PaginadorDTO paginadorDTO);
}

