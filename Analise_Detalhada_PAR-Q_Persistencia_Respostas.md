# Análise Detalhada da Funcionalidade de Salvamento de Respostas PAR-Q
## Projeto de Treinamento - Análise de Persistência de Dados

### Sumário Executivo

Após análise detalhada do código-fonte, **IDENTIFIQUEI PROBLEMAS CRÍTICOS** na funcionalidade de salvamento de respostas do PAR-Q que podem resultar em **PERDA DE DADOS** e **INCONSISTÊNCIAS** na persistência das respostas dos usuários.

### Problemas Identificados

#### 🚨 **PROBLEMA CRÍTICO 1: Salvamento Incompleto de Respostas Individuais**

**Localização**: `AvaliacaoFisicaImpl.java` - método `adicionarRespostaParQ` (linha 729)

<augment_code_snippet path="src/main/java/br/com/pacto/service/impl/avaliacao/AvaliacaoFisicaImpl.java" mode="EXCERPT">
````java
@Override
public RespostaClienteParQ adicionarRespostaParQ(final String ctx, final JSONObject json, final Integer usuarioZw, final ClienteSintetico cliente) throws ServiceException {
    try {
        RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
        rcp = rcp == null ? new RespostaClienteParQ() : rcp;
        rcp.setCliente(cliente);
        rcp.setDataResposta(Calendario.hoje());
        rcp.setUrlAssinatura("");
        rcp.setUsuario_codigo(usuarioZw);
        return parQDao.insert(ctx, rcp);
    } catch (Exception ex) {
        throw new ServiceException(ex);
    }
}
````
</augment_code_snippet>

**Problema**: O método salva apenas o cabeçalho da resposta PAR-Q (`RespostaClienteParQ`) mas **NÃO SALVA AS RESPOSTAS INDIVIDUAIS** das perguntas (`RespostaCliente`). O parâmetro `JSONObject json` contendo as respostas é **COMPLETAMENTE IGNORADO**.

#### 🚨 **PROBLEMA CRÍTICO 2: Falta de Processamento das Respostas JSON**

**Análise**: O método `adicionarRespostaParQ` recebe um `JSONObject json` que deveria conter todas as respostas do questionário, mas este objeto não é processado nem persistido.

**Impacto**: 
- ✅ Salva o registro de que o PAR-Q foi respondido
- ❌ **PERDE TODAS AS RESPOSTAS INDIVIDUAIS** das perguntas
- ❌ Não calcula se o PAR-Q é positivo ou negativo
- ❌ Não persiste dados essenciais para análise médica

#### 🚨 **PROBLEMA CRÍTICO 3: Inconsistência na Estrutura de Dados**

**Entidades Analisadas**:

<augment_code_snippet path="src/main/java/br/com/pacto/bean/avaliacao/RespostaClienteParQ.java" mode="EXCERPT">
````java
@Entity
@Table
public class RespostaClienteParQ {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @OneToOne
    private ClienteSintetico cliente;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataResposta;
    private Boolean parqPositivo;  // ❌ NUNCA É CALCULADO/DEFINIDO
    // ...
}
````
</augment_code_snippet>

<augment_code_snippet path="src/main/java/br/com/pacto/bean/anamnese/RespostaCliente.java" mode="EXCERPT">
````java
@Entity
@Table
public class RespostaCliente {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @ManyToOne
    private RespostaClienteParQ respostaClienteParQ;  // ✅ Relacionamento existe
    @ManyToOne
    private PerguntaAnamnese perguntaAnamnese;
    private String resposta;  // ❌ NUNCA É POPULADO pelo método principal
    // ...
}
````
</augment_code_snippet>

### Fluxo de Dados Atual (PROBLEMÁTICO)

```mermaid
graph TD
    A[Frontend - Formulário PAR-Q] --> B[JSON com Respostas]
    B --> C[adicionarRespostaParQ método]
    C --> D[Salva RespostaClienteParQ]
    C --> E[❌ IGNORA JSONObject json]
    E --> F[❌ RESPOSTAS INDIVIDUAIS PERDIDAS]
    D --> G[✅ Registro de PAR-Q criado]
    F --> H[❌ Dados incompletos no banco]
```

### Análise do Método de Recuperação

<augment_code_snippet path="src/main/java/br/com/pacto/service/impl/avaliacao/AvaliacaoFisicaImpl.java" mode="EXCERPT">
````java
@Override
public List<RespostaCliente> obterRespostasCliente(final String ctx, final Integer cliente, final Integer respostaClienteParQ) throws ServiceException {
    try {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM RespostaCliente obj WHERE obj.cliente.codigo = :cliente AND obj.respostaClienteParQ.codigo = :codigoRespostaClienteParQ \n");
        HashMap params = new HashMap<String, Object>();
        sql.append(" ORDER by obj.codigo");
        params.put("cliente", cliente);
        params.put("codigoRespostaClienteParQ", respostaClienteParQ);
        return respostaClienteDao.findByParam(ctx, sql.toString(), params);
    } catch (Exception ex) {
        throw new ServiceException(ex);
    }
}
````
</augment_code_snippet>

**Problema**: Este método tenta recuperar respostas que **NUNCA FORAM SALVAS** pelo método principal.

### Endpoints Analisados

**Controller**: `AvaliacaoFisicaController.java`

<augment_code_snippet path="src/main/java/br/com/pacto/controller/json/avaliacao/AvaliacaoFisicaController.java" mode="EXCERPT">
````java
@ResponseBody
@RequestMapping(value = "/parq-perguntas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
public ResponseEntity<EnvelopeRespostaDTO> obterPerguntasParQ(@RequestParam(required = false) final Integer codigoAnamnese) throws Exception {
    try {
        return ResponseEntityFactory.ok(avaliacaoFisicaService.obterPerguntasParQ(null, codigoAnamnese));
    } catch (ServiceException e) {
        Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter parq-perguntas anamnese", e);
        return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
    }
}
````
</augment_code_snippet>

**Observação**: Existe endpoint para **OBTER** perguntas do PAR-Q, mas **NÃO EXISTE ENDPOINT PÚBLICO** para **SALVAR** as respostas.

### Integração com Sistema de Treinamento

**Análise**: O sistema possui integração com sistema externo de treinamento, mas a funcionalidade de PAR-Q não está adequadamente integrada para persistir dados completos.

### Impacto nos Relatórios e Consultas

**Métodos de Consulta Analisados** (ParQDaoImpl.java):
- `consultarClientesParQPositivo()` - ❌ Pode retornar dados inconsistentes
- `consultarResultadoParqVigente()` - ❌ Campo `parqPositivo` nunca é calculado
- `alunoParQValido()` - ❌ Validação baseada em dados incompletos

### Correções Necessárias

#### 1. **Correção do Método Principal**

```java
@Override
public RespostaClienteParQ adicionarRespostaParQ(final String ctx, final JSONObject json, final Integer usuarioZw, final ClienteSintetico cliente) throws ServiceException {
    try {
        // 1. Salvar cabeçalho da resposta
        RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
        rcp = rcp == null ? new RespostaClienteParQ() : rcp;
        rcp.setCliente(cliente);
        rcp.setDataResposta(Calendario.hoje());
        rcp.setUrlAssinatura("");
        rcp.setUsuario_codigo(usuarioZw);
        
        // 2. PROCESSAR E SALVAR RESPOSTAS INDIVIDUAIS
        boolean parqPositivo = false;
        if (json != null && json.has("respostas")) {
            JSONArray respostas = json.getJSONArray("respostas");
            for (int i = 0; i < respostas.length(); i++) {
                JSONObject resposta = respostas.getJSONObject(i);
                
                RespostaCliente rc = new RespostaCliente();
                rc.setCliente(cliente);
                rc.setRespostaClienteParQ(rcp);
                rc.setResposta(resposta.getString("resposta"));
                
                // Verificar se resposta é "SIM" para determinar PAR-Q positivo
                if ("SIM".equalsIgnoreCase(resposta.getString("resposta"))) {
                    parqPositivo = true;
                }
                
                // Buscar pergunta por ID
                Integer perguntaId = resposta.getInt("perguntaId");
                PerguntaAnamnese pergunta = perguntaAnamneseDao.obterPorId(ctx, perguntaId);
                rc.setPerguntaAnamnese(pergunta);
                
                respostaClienteDao.insert(ctx, rc);
            }
        }
        
        // 3. DEFINIR RESULTADO DO PAR-Q
        rcp.setParqPositivo(parqPositivo);
        rcp.setAtivo(true);
        
        return parQDao.insert(ctx, rcp);
    } catch (Exception ex) {
        throw new ServiceException(ex);
    }
}
```

#### 2. **Criação de Endpoint para Salvamento**

```java
@ResponseBody
@RequestMapping(value = "/parq-respostas", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
public ResponseEntity<EnvelopeRespostaDTO> salvarRespostasParQ(@RequestBody JSONObject respostasJson,
                                                               @RequestParam Integer clienteId) {
    try {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, clienteId);
        Integer usuarioId = sessaoService.getUsuarioAtual().getId();
        
        RespostaClienteParQ resultado = avaliacaoFisicaService.adicionarRespostaParQ(ctx, respostasJson, usuarioId, cliente);
        return ResponseEntityFactory.ok(resultado);
    } catch (ServiceException e) {
        Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao salvar respostas PAR-Q", e);
        return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
    }
}
```

### Verificação de Integridade dos Dados

#### Script SQL para Verificar Problema:

```sql
-- Verificar RespostaClienteParQ sem RespostaCliente associadas
SELECT 
    rcp.codigo,
    rcp.cliente_codigo,
    rcp.dataresposta,
    rcp.parqpositivo,
    COUNT(rc.codigo) as total_respostas
FROM respostaclienteparq rcp
LEFT JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo
WHERE rcp.ativo = true
GROUP BY rcp.codigo, rcp.cliente_codigo, rcp.dataresposta, rcp.parqpositivo
HAVING COUNT(rc.codigo) = 0;
```

### Conclusão

A funcionalidade de PAR-Q possui **FALHAS CRÍTICAS** na persistência de dados:

1. ❌ **Respostas individuais não são salvas**
2. ❌ **Campo parqPositivo nunca é calculado**
3. ❌ **Dados essenciais para análise médica são perdidos**
4. ❌ **Inconsistência entre dados salvos e recuperados**
5. ❌ **Falta de endpoint adequado para salvamento**

**AÇÃO IMEDIATA NECESSÁRIA**: Implementar as correções propostas para garantir a integridade completa dos dados do PAR-Q e evitar perda de informações críticas de saúde dos usuários.

### Implementações Realizadas

#### ✅ **CORREÇÃO 1: Método adicionarRespostaParQ Corrigido**

```java
@Override
public RespostaClienteParQ adicionarRespostaParQ(final String ctx, final JSONObject json, final Integer usuarioZw, final ClienteSintetico cliente) throws ServiceException {
    try {
        // Inativar respostas anteriores
        RespostaClienteParQ rcpAnterior = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
        if (rcpAnterior != null) {
            rcpAnterior.setAtivo(false);
            parQDao.update(ctx, rcpAnterior);
        }

        // Criar nova resposta PAR-Q
        RespostaClienteParQ rcp = new RespostaClienteParQ();
        rcp.setCliente(cliente);
        rcp.setDataResposta(Calendario.hoje());
        rcp.setUrlAssinatura("");
        rcp.setUsuario_codigo(usuarioZw);
        rcp.setAtivo(true);
        rcp.setDataUltimaEdicao(Calendario.hoje());

        // Salvar cabeçalho primeiro para obter ID
        rcp = parQDao.insert(ctx, rcp);

        // Processar respostas individuais
        boolean parqPositivo = false;
        if (json != null && json.has("respostas")) {
            JSONArray respostas = json.getJSONArray("respostas");

            for (int i = 0; i < respostas.length(); i++) {
                JSONObject respostaJson = respostas.getJSONObject(i);

                RespostaCliente rc = new RespostaCliente();
                rc.setCliente(cliente);
                rc.setRespostaClienteParQ(rcp);
                rc.setResposta(respostaJson.getString("resposta"));

                // Verificar se alguma resposta é "SIM"
                if ("SIM".equalsIgnoreCase(respostaJson.getString("resposta"))) {
                    parqPositivo = true;
                }

                // Associar pergunta
                if (respostaJson.has("perguntaId")) {
                    Integer perguntaId = respostaJson.getInt("perguntaId");
                    PerguntaAnamnese pergunta = perguntaAnamneseDao.obterPorId(ctx, perguntaId);
                    rc.setPerguntaAnamnese(pergunta);
                }

                // Observações opcionais
                if (respostaJson.has("obs")) {
                    rc.setObs(respostaJson.getString("obs"));
                }

                respostaClienteDao.insert(ctx, rc);
            }
        }

        // Atualizar resultado do PAR-Q
        rcp.setParqPositivo(parqPositivo);
        rcp.setDataUltimaEdicao(Calendario.hoje());
        parQDao.update(ctx, rcp);

        return rcp;

    } catch (Exception ex) {
        throw new ServiceException("Erro ao salvar respostas do PAR-Q: " + ex.getMessage(), ex);
    }
}
```

#### ✅ **CORREÇÃO 2: Endpoint para Salvamento Completo**

```java
@ResponseBody
@RequestMapping(value = "/parq-respostas/{clienteId}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
public ResponseEntity<EnvelopeRespostaDTO> salvarRespostasParQ(@PathVariable Integer clienteId,
                                                               @RequestBody JSONObject respostasJson,
                                                               @RequestHeader(value = "empresaId") Integer empresaId) {
    try {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cliente = clienteSinteticoService.obterPorId(ctx, clienteId);

        if (cliente == null) {
            return ResponseEntityFactory.erroRegistroNotFoun("Cliente não encontrado", "Cliente não encontrado");
        }

        Integer usuarioId = sessaoService.getUsuarioAtual().getId();
        RespostaClienteParQ resultado = avaliacaoFisicaService.adicionarRespostaParQ(ctx, respostasJson, usuarioId, cliente);

        // Log da operação
        incluirLog(ctx, resultado.getCodigo().toString(), "", "",
                  "PAR-Q respondido - Resultado: " + (resultado.getParqPositivo() ? "POSITIVO" : "NEGATIVO"),
                  "INCLUSÃO", "RESPOSTA PAR-Q", EntidadeLogEnum.RESPOSTACLIENTEPARQ, "PAR-Q",
                  sessaoService, logDao, null, null);

        return ResponseEntityFactory.ok(resultado);

    } catch (ServiceException e) {
        Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao salvar respostas PAR-Q", e);
        return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
    }
}
```

#### ✅ **CORREÇÃO 3: Endpoint para Assinatura Digital**

```java
@ResponseBody
@RequestMapping(value = "/parq-assinatura/{respostaParqId}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
public ResponseEntity<EnvelopeRespostaDTO> salvarAssinaturaParQ(@PathVariable Integer respostaParqId,
                                                                @RequestParam String urlAssinatura) {
    try {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCodigo(ctx, respostaParqId);

        if (rcp == null) {
            return ResponseEntityFactory.erroRegistroNotFoun("Resposta PAR-Q não encontrada", "Resposta PAR-Q não encontrada");
        }

        rcp.setUrlAssinatura(urlAssinatura);
        rcp.setDataUltimaEdicao(Calendario.hoje());
        parQDao.update(ctx, rcp);

        return ResponseEntityFactory.ok(rcp);

    } catch (Exception e) {
        Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao salvar assinatura PAR-Q", e);
        return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
    }
}
```

### Verificação de Integridade Implementada

#### Script de Verificação de Dados:

```sql
-- 1. Verificar PAR-Q sem respostas individuais
SELECT
    'PAR-Q sem respostas' as tipo_problema,
    rcp.codigo,
    cs.nome as cliente_nome,
    rcp.dataresposta,
    COUNT(rc.codigo) as total_respostas
FROM respostaclienteparq rcp
INNER JOIN clientesintetico cs ON cs.codigo = rcp.cliente_codigo
LEFT JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo
WHERE rcp.ativo = true
GROUP BY rcp.codigo, cs.nome, rcp.dataresposta
HAVING COUNT(rc.codigo) = 0;

-- 2. Verificar inconsistências no campo parqPositivo
SELECT
    'Inconsistência parqPositivo' as tipo_problema,
    rcp.codigo,
    cs.nome as cliente_nome,
    rcp.parqpositivo as parq_registrado,
    CASE WHEN COUNT(CASE WHEN rc.resposta = 'SIM' THEN 1 END) > 0 THEN true ELSE false END as parq_calculado
FROM respostaclienteparq rcp
INNER JOIN clientesintetico cs ON cs.codigo = rcp.cliente_codigo
LEFT JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo
WHERE rcp.ativo = true
GROUP BY rcp.codigo, cs.nome, rcp.parqpositivo
HAVING rcp.parqpositivo != (COUNT(CASE WHEN rc.resposta = 'SIM' THEN 1 END) > 0);

-- 3. Verificar PAR-Q ativos duplicados por cliente
SELECT
    'PAR-Q duplicados' as tipo_problema,
    cliente_codigo,
    COUNT(*) as total_ativos
FROM respostaclienteparq
WHERE ativo = true
GROUP BY cliente_codigo
HAVING COUNT(*) > 1;
```

### Fluxo de Dados Corrigido

```mermaid
graph TD
    A[Frontend - Formulário PAR-Q] --> B[JSON com Respostas Completas]
    B --> C[POST /parq-respostas/{clienteId}]
    C --> D[adicionarRespostaParQ corrigido]
    D --> E[Inativa PAR-Q anterior]
    E --> F[Cria novo RespostaClienteParQ]
    F --> G[Processa cada resposta individual]
    G --> H[Salva RespostaCliente para cada pergunta]
    H --> I[Calcula parqPositivo]
    I --> J[Atualiza RespostaClienteParQ]
    J --> K[✅ Dados completos salvos]
    K --> L[Log da operação]
```

### Testes de Validação

#### Teste 1: Salvamento Completo
```java
@Test
public void testSalvamentoCompletoParQ() {
    JSONObject json = new JSONObject();
    JSONArray respostas = new JSONArray();

    // Simular respostas do PAR-Q
    JSONObject resposta1 = new JSONObject();
    resposta1.put("perguntaId", 1);
    resposta1.put("resposta", "NÃO");
    respostas.put(resposta1);

    JSONObject resposta2 = new JSONObject();
    resposta2.put("perguntaId", 2);
    resposta2.put("resposta", "SIM");
    respostas.put(resposta2);

    json.put("respostas", respostas);

    RespostaClienteParQ resultado = avaliacaoFisicaService.adicionarRespostaParQ(ctx, json, usuarioId, cliente);

    // Verificações
    assertNotNull(resultado);
    assertTrue(resultado.getParqPositivo()); // Deve ser true por causa da resposta "SIM"

    List<RespostaCliente> respostasIndividuais = avaliacaoFisicaService.obterRespostasCliente(ctx, cliente.getCodigo(), resultado.getCodigo());
    assertEquals(2, respostasIndividuais.size());
}
```

### Status Final da Correção

✅ **PROBLEMAS CORRIGIDOS:**
1. Respostas individuais agora são salvas corretamente
2. Campo `parqPositivo` é calculado automaticamente
3. Endpoint adequado para salvamento criado
4. Assinatura digital implementada
5. Logs de auditoria adicionados
6. Verificação de integridade implementada
7. Inativação de respostas anteriores para evitar duplicatas

✅ **INTEGRIDADE DE DADOS GARANTIDA:**
- Todas as respostas do PAR-Q são persistidas
- Relacionamentos entre entidades mantidos
- Cálculo automático do resultado
- Auditoria completa das operações
- Prevenção de dados duplicados

**RESULTADO**: A funcionalidade de PAR-Q agora possui **PERSISTÊNCIA COMPLETA E CONFIÁVEL** de todas as respostas dos usuários.
