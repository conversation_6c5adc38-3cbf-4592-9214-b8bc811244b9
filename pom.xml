<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>br.com.pacto.treino</groupId>
    <artifactId>TreinoWeb</artifactId>
    <packaging>war</packaging>
    <version>1.6.1495</version><!--USED-BY-BUMPVERION-->
    <name>TreinoWeb</name>
    <url>http://www.pactosolucoes.com.br</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <contexto>TreinoWeb</contexto>
        <outputDirectory>D:/suporte/3Versoes/ZillyonWeb/deploy/war/RC</outputDirectory>
        <hostDeploy>localhost</hostDeploy>
        <portDeploy>8085</portDeploy>
        <deployUser>admin</deployUser>
        <deployPwd></deployPwd>
        <sshHost>********</sshHost>
        <sshPort>22</sshPort>
        <sshUser>root</sshUser>
        <sshPwd></sshPwd>
        <urlFeedPacto>http://www.pacto.vc/feed/</urlFeedPacto>
        <urlOAMD>http://app.pactosolucoes.com.br/oamd</urlOAMD>
        <urlOAMDSegura>https://app.pactosolucoes.com.br/oamd</urlOAMDSegura>
        <urlAdmApp>https://app.pactosolucoes.com.br/AdmAPP</urlAdmApp>
        <urlAppRoot>https://app.pactosolucoes.com.br/app</urlAppRoot>
        <urlImportAdmWS>${urlAppRoot}/AdmWS?wsdl</urlImportAdmWS>
        <urlImportZW>${urlAppRoot}/IntegracaoCadastrosWS?wsdl</urlImportZW>
        <urlImportTurmas>${urlAppRoot}/IntegracaoTurmasWS?wsdl</urlImportTurmas>
        <urlImportConvite>${urlAppRoot}/ConviteAulaExperimentalWS?wsdl</urlImportConvite>
        <URL_ZW_INTEGRACAO>${urlAppRoot}</URL_ZW_INTEGRACAO>
        <keyUnitTests>in-memory</keyUnitTests>
        <urlObterBanners>http://app.pactosolucoes.com.br/app/UpdateServlet</urlObterBanners>
        <urlLOGIN>https://app.pactosolucoes.com.br/login</urlLOGIN>
        <urlPushWeb></urlPushWeb>
        <urlOAMD2>**************************************</urlOAMD2>
        <userOAMD2>postgres</userOAMD2>
        <pwdOAMD2>pactodb</pwdOAMD2>
        <instanceSchedulingName></instanceSchedulingName>
        <redirecionarLogin></redirecionarLogin>
        <instanciasPropagar></instanciasPropagar>
        <loadFactoryOnStart>false</loadFactoryOnStart>
        <redir>false</redir>
        <restart-cluster></restart-cluster>
        <url_redirecionar>http://app.pactosolucoes.com.br</url_redirecionar>
        <loadInstancesFromCloud>false</loadInstancesFromCloud>
        <forceUrlFotos>false</forceUrlFotos>
        <COOKIE_FAIL>true</COOKIE_FAIL>
        <dirMidiasEmbedded></dirMidiasEmbedded>
        <FOTOS_NUVEM>false</FOTOS_NUVEM>
        <TIPO_MIDIA>AWS_S3</TIPO_MIDIA>
        <URL_FOTOS_NUVEM>https://cdn1.pactorian.net</URL_FOTOS_NUVEM>
        <URL_ARQUIVOS_NUVEM>https://cdn1.pactorian.net</URL_ARQUIVOS_NUVEM>
        <BUSCAR_CONHECIMENTO_UCP>true</BUSCAR_CONHECIMENTO_UCP>
        <USAR_URL_RECURSO_EMPRESA>true</USAR_URL_RECURSO_EMPRESA>
        <MY_URL_UP_BASE>https://app.pactosolucoes.com.br/myUCP</MY_URL_UP_BASE>
        <DISCOVERY_URL>https://discovery.ms.pactosolucoes.com.br</DISCOVERY_URL>
        <AUTH_SECRET_PATH>/root/.ssh/auth-secret</AUTH_SECRET_PATH>
        <AUTH_SECRET_PERSONA_PATH>/root/.ssh/auth-secret-persona-treino</AUTH_SECRET_PERSONA_PATH>

        <URL_HTTP_PLATAFORMA_PACTO>http://zw101.pactosolucoes.com.br:8011/ccl</URL_HTTP_PLATAFORMA_PACTO>
        <URL_HTTPS_PLATAFORMA_PACTO>https://zw101.pactosolucoes.com.br:9011/ccl/</URL_HTTPS_PLATAFORMA_PACTO>
        <USE_BETA_TESTERS>false</USE_BETA_TESTERS>
        <SERVIDOR_MEMCACHED>DISABLED</SERVIDOR_MEMCACHED>
        <NUMEROS_WHATSAPP_PACTO>5562983294142,5562994490545,5562981730707</NUMEROS_WHATSAPP_PACTO>
        <INSTANCE_ALLOW_DDL></INSTANCE_ALLOW_DDL>
        <ENABLE_NEW_LOGIN>true</ENABLE_NEW_LOGIN>
        <!-- Spring -->
        <spring.version>4.0.3.RELEASE</spring.version>

        <!-- Novo Treino -->
        <aspectj.version>1.8.9</aspectj.version>
        <jwt-auth0.version>3.4.0</jwt-auth0.version>
        <jackson.version>2.12.2</jackson.version>
        <AMBIENTE_TESTE>false</AMBIENTE_TESTE>
        <LOGAR_DAO>false</LOGAR_DAO>
        <TOKENS_ACESSO_API_CLIENTE>gP6pV2pS6lC8sY7nH6vG8tN4xT0vR9tU</TOKENS_ACESSO_API_CLIENTE>
        <TOKEN_GYMPASS_V3></TOKEN_GYMPASS_V3>
        <CONSULTA_CONTRATO_ASSINATURA>false</CONSULTA_CONTRATO_ASSINATURA>
        <CONSULTA_USUARIOS_APP_PELO_FIREBASE>false</CONSULTA_USUARIOS_APP_PELO_FIREBASE>
        <MODO_CONSULTA_AGENDA>consultaBD</MODO_CONSULTA_AGENDA>
        <MODO_CONSULTA_DASHBI>consultaBD</MODO_CONSULTA_DASHBI>
        <AES_SECRET_APP_TREINO>icbvhmZoTFDP3TmhP3jKtain97ED21kWBwa7X0NxP7c=</AES_SECRET_APP_TREINO>
        <URL_API_SELFLOOPS>https://api.selfloops.com/fitness-prod-v1</URL_API_SELFLOOPS>
        <URL_OAUTH_TOKEN_SELFLOOPS>https://selfloops.com/oauth/token</URL_OAUTH_TOKEN_SELFLOOPS>
        <PACTO_CLIENT_ID_SELFLOOPS>****************************************************************</PACTO_CLIENT_ID_SELFLOOPS>
        <PACTO_SECRET_ID_SELFLOOPS>****************************************************************</PACTO_SECRET_ID_SELFLOOPS>
    </properties>

    <repositories>
        <repository>
            <id>nexusLocal</id>
            <name>Pacto Maven Repository</name>
            <url>https://mussum.ath.cx/nexus/content/groups/public</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>nexusSolutioIn</id>
            <name>Solutio-in Maven Repository</name>
            <url>https://mussum.ath.cx/nexus/content/repositories/solutio</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>jaspersoft-third-party</id>
            <url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
        </repository>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Internal Snapshots</name>
            <url>https://mussum.ath.cx/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>Internal Releases</name>
            <url>https://mussum.ath.cx/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>tomcat7</id>
            <properties>
                <profile>tomcat7</profile>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <instanciasPropagar>localhost:8082</instanciasPropagar>
                <loadFactoryOnStart>false</loadFactoryOnStart>
                <DISCOVERY_URL>http://localhost:8101</DISCOVERY_URL>
                <urlAppRoot>http://localhost:8200/ZillyonWeb</urlAppRoot>
                <urlImportAdmWS>${urlAppRoot}/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>${urlAppRoot}/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>${urlAppRoot}/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <urlImportConvite>${urlAppRoot}/ConviteAulaExperimentalWS?wsdl</urlImportConvite>
                <URL_ZW_INTEGRACAO>${urlAppRoot}</URL_ZW_INTEGRACAO>
                <AUTH_SECRET_PATH>C:\\git\\pacto-solucoes\\treino\\docker\\keys\\auth-secret</AUTH_SECRET_PATH>
                <SERVIDOR_MEMCACHED>DISABLED</SERVIDOR_MEMCACHED>
            </properties>
            <dependencies>
                <!-- JAX-WS -->
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.2.8</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>jta</artifactId>
                    <version>1.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-api</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-hibernate3</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jdbc</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jms</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jta</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-jbossweb</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat7</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-runtime</artifactId>
                    <version>1.0.0.RC1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atomikos-util</artifactId>
                    <version>3.9.1</version>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>activateWebServicesForTomcat7</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Ativar WebServices do Pacto Treino para Tomcat7" />
                                        <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>
                                        <xmltask outputter="simple" source="${basedir}/target/${project.name}/WEB-INF/web.xml"
                                                 dest="${basedir}/target/${project.name}/WEB-INF/web.xml">
                                            <insert path="/web-app/servlet-mapping[last()]" position="after">
                                                <![CDATA[
                                                    <listener>
                                                      <listener-class>com.sun.xml.ws.transport.http.servlet.WSServletContextListener</listener-class>
                                                    </listener>
                                                    <servlet>
                                                        <servlet-name>TreinoWS</servlet-name>
                                                        <servlet-class>com.sun.xml.ws.transport.http.servlet.WSServlet</servlet-class>
                                                        <load-on-startup>1</load-on-startup>
                                                    </servlet>
                                                    <servlet-mapping>
                                                        <servlet-name>TreinoWS</servlet-name>
                                                        <url-pattern>/TreinoWS</url-pattern>
                                                    </servlet-mapping>
                                                ]]>
                                            </insert>
                                        </xmltask>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>j2se</id>
            <properties>
                <profile>j2se</profile>
            </properties>
            <dependencies>
                <!-- JAX-WS -->
                <dependency>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-rt</artifactId>
                    <version>2.2.8</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>jta</artifactId>
                    <version>1.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-api</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-hibernate3</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jdbc</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jms</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>transactions-jta</artifactId>
                    <version>3.9.1</version>
                </dependency>

                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atomikos-util</artifactId>
                    <version>3.9.1</version>
                </dependency>
                <dependency>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                    <version>2.5</version>

                </dependency>
            </dependencies>
            <build>
                <plugins>

                </plugins>
            </build>
        </profile>

        <profile>
            <id>glassfish3</id>
            <properties>
                <profile>glassfish3</profile>
                <INSTANCE_ALLOW_DDL>server,tr-01</INSTANCE_ALLOW_DDL>
                <TOKENS_ACESSO_API_CLIENTE>${env.TOKENS_ACESSO_API_CLIENTE}</TOKENS_ACESSO_API_CLIENTE>
                <TOKEN_GYMPASS_V3>${env.TOKEN_GYMPASS_V3}</TOKEN_GYMPASS_V3>
                <CONSULTA_CONTRATO_ASSINATURA>true</CONSULTA_CONTRATO_ASSINATURA>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-jbossweb</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-compat-tomcat7</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
                <dependency>
                    <groupId>br.com.pacto.lib</groupId>
                    <artifactId>atmosphere-runtime</artifactId>
                    <version>1.1.0.RC4</version>
                </dependency>
            </dependencies>
            <build>

            </build>
        </profile>

        <profile>
            <id>distribution</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.4</version>
                        <configuration>
                            <warName>${contexto}</warName>
                            <outputDirectory>${outputDirectory}</outputDirectory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>deployment</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>redeploy-shell</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Executando redeploy para Glassfish..."/>
                                        <scp trust="true" file="${basedir}/target/${project.name}.war"
                                             port="${sshPort}"
                                             verbose="true"
                                             keyfile="${user.home}/.ssh/id_rsa"
                                             passphrase=""
                                             todir="${sshUser}@${sshHost}:/opt/${contexto}.war"/>

                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${sshHost}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /opt/redeploy.sh ${contexto}"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <properties>

            </properties>
        </profile>

        <profile>
            <id>infra-zw</id>
            <properties>
                <sufixoInfra>60</sufixoInfra>
                <sshHost>***********${sufixoInfra}</sshHost>
                <outputDirectory>/opt/deploy</outputDirectory>
                <sshPort>22</sshPort>
                <urlOAMD2>**************************************</urlOAMD2>
                <userOAMD2>zillyonweb</userOAMD2>
                <pwdOAMD2>pactodb</pwdOAMD2>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <redir>false</redir>
                <loadFactoryOnStart>false</loadFactoryOnStart>
                <loadInstancesFromCloud>false</loadInstancesFromCloud>
                <FOTOS_NUVEM>false</FOTOS_NUVEM>
                <instanciasPropagar>localhost:28084</instanciasPropagar>
                <contexto>TreinoWeb</contexto>
                <urlImportAdmWS>http://zw${sufixoInfra}.pactosolucoes.com.br/app/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://zw${sufixoInfra}.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://zw${sufixoInfra}.pactosolucoes.com.br/app/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <URL_ZW_INTEGRACAO>http://zw${sufixoInfra}.pactosolucoes.com.br/app</URL_ZW_INTEGRACAO>
                <AUTH_SECRET_PATH>/home/<USER>/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/home/<USER>/.ssh/auth-secret-persona-treino</AUTH_SECRET_PERSONA_PATH>
                <USE_BETA_TESTERS>true</USE_BETA_TESTERS>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
            </properties>
        </profile>

        <profile>
            <id>infra-zw-port</id>
            <properties>
                <sufixoInfra>100</sufixoInfra>
                <sufixoPorta>10</sufixoPorta>
                <urlImportAdmWS>http://zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoPorta}/app/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoPorta}/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoPorta}/app/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <URL_ZW_INTEGRACAO>http://zw${sufixoInfra}.pactosolucoes.com.br:80${sufixoPorta}/app</URL_ZW_INTEGRACAO>
                <sshHost>10.33.205.${sufixoInfra}</sshHost>
                <outputDirectory>/opt/deploy</outputDirectory>
                <sshPort>22</sshPort>
                <urlOAMD2>******************************${sufixoPorta}/OAMD2</urlOAMD2>
                <userOAMD2>zillyonweb</userOAMD2>
                <pwdOAMD2>pactodb</pwdOAMD2>
                <loadFactoryOnStart>false</loadFactoryOnStart>
                <loadInstancesFromCloud>false</loadInstancesFromCloud>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <FOTOS_NUVEM>false</FOTOS_NUVEM>
                <AUTH_SECRET_PATH>/home/<USER>/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/home/<USER>/.ssh/auth-secret-persona-treino</AUTH_SECRET_PERSONA_PATH>
                <USE_BETA_TESTERS>true</USE_BETA_TESTERS>
                <SERVIDOR_MEMCACHED>localhost:11211</SERVIDOR_MEMCACHED>
            </properties>
        </profile>

        <profile>
            <id>infra-local</id>
            <properties>
                <sshHost>buritifit.dyndns.org</sshHost>
                <httpPort>8080</httpPort>
                <urlImportAdmWS>http://${sshHost}:${httpPort}/app/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://${sshHost}:${httpPort}/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://${sshHost}:${httpPort}/app/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <URL_ZW_INTEGRACAO>http://localhost:${httpPort}/app</URL_ZW_INTEGRACAO>
                <instanciasPropagar>localhost:${httpPort}</instanciasPropagar>
                <outputDirectory>/opt/deploy</outputDirectory>
                <sshPort>22</sshPort>
                <urlOAMD2>**************************************</urlOAMD2>
                <userOAMD2>zillyonweb</userOAMD2>
                <pwdOAMD2>pactodb</pwdOAMD2>
                <loadFactoryOnStart>false</loadFactoryOnStart>
                <loadInstancesFromCloud>false</loadInstancesFromCloud>
                <instanceSchedulingName>disable</instanceSchedulingName>
                <FOTOS_NUVEM>false</FOTOS_NUVEM>
                <AUTH_SECRET_PATH>/root/.ssh/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/root/.ssh/auth-secret-persona-treino</AUTH_SECRET_PERSONA_PATH>
                <USE_BETA_TESTERS>true</USE_BETA_TESTERS>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
                <URL_FOTOS_NUVEM>${URL_ZW_INTEGRACAO}/zw-photos</URL_FOTOS_NUVEM>
                <TIPO_MIDIA>ZW_INTERNAL</TIPO_MIDIA>
            </properties>
        </profile>

        <profile>
            <id>restart-cluster</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>restart-cluster-shell</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Reiniciando cluster Glassfish...${restart-cluster}"/>

                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${sshHost}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="asadmin stop-cluster ${restart-cluster}"/>

                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${sshHost}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="asadmin start-cluster ${restart-cluster}"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>deploy-tomcat7</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.tomcat.maven</groupId>
                        <artifactId>tomcat7-maven-plugin</artifactId>
                        <version>2.1</version>
                        <executions>
                            <execution>
                                <id>tomcat-deploy</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>redeploy</goal>
                                </goals>
                                <configuration>
                                    <url>http://${hostDeploy}:${portDeploy}/manager/text</url>
                                    <username>${deployUser}</username>
                                    <password>${deployPwd}</password>
                                    <path>/${contexto}</path>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>restart-tomcat7-shell</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo message="Restart TOMCAT 7..."/>
                                        <sshexec port="${sshPort}" verbose="false"
                                                 trust="true" host="${sshHost}"
                                                 username="${sshUser}"
                                                 keyfile="${user.home}/.ssh/id_rsa"
                                                 passphrase=""
                                                 command="sh /opt/restart-tomcat7.sh"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>homolog</id>
            <properties>
                <FOTOS_NUVEM>true</FOTOS_NUVEM>
            </properties>

        </profile>

        <profile>
            <id>desenv</id>
            <properties>
                <urlOAMD2>**************************************</urlOAMD2>
                <loadInstancesFromCloud>false</loadInstancesFromCloud>
                <forceUrlFotos>false</forceUrlFotos>
                <AUTH_SECRET_PATH>C:\\opt\\secret\\secret.txt</AUTH_SECRET_PATH>
                <urlImportAdmWS>http://localhost:8081/ZillyonWeb/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://localhost:8081/ZillyonWeb/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://localhost:8081/ZillyonWeb/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <urlImportConvite>http://localhost:8081/ZillyonWeb/ConviteAulaExperimentalWS?wsdl</urlImportConvite>
                <URL_ZW_INTEGRACAO>http://localhost:8081/ZillyonWeb</URL_ZW_INTEGRACAO>

                <urlOAMD>http://localhost:8202/NewOAMD</urlOAMD>
                <URL_HTTP_PLATAFORMA_PACTO>http://localhost:4200/</URL_HTTP_PLATAFORMA_PACTO>
                <URL_HTTPS_PLATAFORMA_PACTO>http://localhost:4200/</URL_HTTPS_PLATAFORMA_PACTO>
                <AMBIENTE_TESTE>true</AMBIENTE_TESTE>
                <LOGAR_DAO>true</LOGAR_DAO>
                <URL_API_SELFLOOPS>https://api.selfloops.com/fitness-dev-v1</URL_API_SELFLOOPS>
                <URL_OAUTH_TOKEN_SELFLOOPS>https://stage.selfloops.com/oauth/token</URL_OAUTH_TOKEN_SELFLOOPS>
                <PACTO_CLIENT_ID_SELFLOOPS>****************************************************************</PACTO_CLIENT_ID_SELFLOOPS>
                <PACTO_SECRET_ID_SELFLOOPS>****************************************************************</PACTO_SECRET_ID_SELFLOOPS>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <inherited>true</inherited>
                        <dependencies>
                            <dependency>
                                <groupId>com.oopsconsultancy</groupId>
                                <artifactId>xmltask</artifactId>
                                <version>1.16</version>
                            </dependency>
                            <dependency>
                                <groupId>ant</groupId>
                                <artifactId>ant-jsch</artifactId>
                                <version>1.6.5</version>
                            </dependency>
                            <dependency>
                                <groupId>com.jcraft</groupId>
                                <artifactId>jsch</artifactId>
                                <version>0.1.55</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>docker</id>
            <properties>
                <urlOAMD2>*************************************</urlOAMD2>
                <urlOAMD>http://app.pactosolucoes.com.br/oamd</urlOAMD>
                <urlImportAdmWS>http://app.pactosolucoes.com.br/app/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://app.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://app.pactosolucoes.com.br/app/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <urlImportConvite>http://app.pactosolucoes.com.br/app/ConviteAulaExperimentalWS?wsdl</urlImportConvite>
                <URL_ZW_INTEGRACAO>http://devapp.pactosolucoes.com.br:8087/ZillyonWeb</URL_ZW_INTEGRACAO>
                <AUTH_SECRET_PATH>/keys/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>/keys/auth-secret-persona</AUTH_SECRET_PERSONA_PATH>
                <BUSCAR_CONHECIMENTO_UCP>false</BUSCAR_CONHECIMENTO_UCP>
                <USAR_URL_RECURSO_EMPRESA>false</USAR_URL_RECURSO_EMPRESA>
                <instanciasPropagar>treino:8080</instanciasPropagar>
                <SERVIDOR_MEMCACHED>DISABLED</SERVIDOR_MEMCACHED>
                <AMBIENTE_TESTE>true</AMBIENTE_TESTE>
                <LOGAR_DAO>true</LOGAR_DAO>
            </properties>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <urlOAMD>http://host.docker.internal:8202/NewOAMD</urlOAMD>
                <urlImportAdmWS>http://host.docker.internal:8200/ZillyonWeb/AdmWS?wsdl</urlImportAdmWS>
                <urlImportZW>http://host.docker.internal:8200/ZillyonWeb/IntegracaoCadastrosWS?wsdl</urlImportZW>
                <urlImportTurmas>http://host.docker.internal:8200/ZillyonWeb/IntegracaoTurmasWS?wsdl</urlImportTurmas>
                <urlImportConvite>http://host.docker.internal:8200/ZillyonWeb/ConviteAulaExperimentalWS?wsdl</urlImportConvite>
                <URL_ZW_INTEGRACAO>http://host.docker.internal:8200/ZillyonWeb</URL_ZW_INTEGRACAO>
                <AUTH_SECRET_PATH>C:/PACTO/treino/docker/keys/auth-secret</AUTH_SECRET_PATH>
                <AUTH_SECRET_PERSONA_PATH>C:/PACTO/treino/docker/keys/auth-secret-persona</AUTH_SECRET_PERSONA_PATH>
            </properties>
        </profile>
    </profiles>


    <dependencies>

        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
            <version>2.12.1</version>
        </dependency>

        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.16.0</version>
        </dependency>


        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.5.1</version>
        </dependency>

        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-fonts</artifactId>
            <version>6.0.0</version>
        </dependency>


        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>antlr</artifactId>
            <version>2.7.6</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>asm</artifactId>
            <version>1.5.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>asm-attrs</artifactId>
            <version>1.5.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>bootstrap</artifactId>
            <version>1.0.10</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.6</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>cron4j</artifactId>
            <version>2.2.5</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>ehcache</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>guava</artifactId>
            <version>16.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>3.6.10.Final</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.javax.persistence</groupId>
                    <artifactId>hibernate-jpa-2.0-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate-jpa-2.0-api</artifactId>
            <version>1.0.1.Final</version>
        </dependency>-->
        <dependency>
            <groupId>org.hibernate.javax.persistence</groupId>
            <artifactId>hibernate-jpa-2.1-api</artifactId>
            <version>1.0.0.Final</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hibernate3</artifactId>
            <version>3.6.10.Final</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>hsqldb</artifactId>
            <version>1.8.0.10</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.18.2-GA</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <version>1.7.5</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jdom</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jsf-api</artifactId>
            <version>2.2.5</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jsf-impl</artifactId>
            <version>2.2.5</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>qrgen</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>rome</artifactId>
            <version>0.9</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>1.7.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>prettyfaces-jsf2</artifactId>
            <version>3.3.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>poi</artifactId>
            <version>3.8-20120326</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-tools</artifactId>
            <version>3.6.0.Final</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.javax.persistence</groupId>
                    <artifactId>hibernate-jpa-2.0-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>jdbc2_0-stdext</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.18</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>4.0.3</version>
        </dependency>
        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>reflections-0.9.9-RC1-uberjar</artifactId>
            <version>0.9.9</version>
            <classifier>uberjar</classifier>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib</groupId>
            <artifactId>standard</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
            <version>2.5</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>
        <!-- AMAZON AWS SDK -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.836</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ec2</artifactId>
            <version>1.11.836</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.30</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.10.10</version>
        </dependency>

        <!--AMAZON AWS SDK END -->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4</version>
        </dependency>

        <!-- CAPTURA FOTOS -->
        <dependency>
            <groupId>backport-util-concurrent</groupId>
            <artifactId>backport-util-concurrent</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-bootstrap</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-common</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-core</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-opt</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-proxy</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>br.com.pacto.lib.flex</groupId>
            <artifactId>flex-messaging-remoting</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.9.2</version>
        </dependency>

        <!-- Google -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>2.0</version>
        </dependency>

        <!-- ASPECTJ -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>${jwt-auth0.version}</version>
        </dependency>

        <dependency>
            <groupId>org.olap4j</groupId>
            <artifactId>olap4j</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.6.1</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.6.1</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.0.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>1.0</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <packagingExcludes>
                        WEB-INF/lib/hsqldb*.jar,
                        WEB-INF/lib/junit*.jar,
                        WEB-INF/lib/xalan-2.6.0.jar,
                        WEB-INF/lib/xercesImpl-2.6.2.jar,
                        WEB-INF/lib/xml-apis-1.0.b2.jar,
                        WEB-INF/lib/xmlParserAPIs-2.6.2.jar
                    </packagingExcludes>
                    <webXml>src/main/webapp/WEB-INF/web.xml</webXml>
                    <warSourceExcludes>**/.svn/**</warSourceExcludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jvnet.jax-ws-commons</groupId>
                <artifactId>jaxws-maven-plugin</artifactId>
                <version>2.2</version>

                <executions>
                    <execution>
                        <id>generate-ws-treino</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>wsgen</goal>
                        </goals>
                        <configuration>
                            <sei>br.com.pacto.webservice.TreinoWS</sei>
                            <genWsdl>true</genWsdl>
                            <keep>false</keep>
                            <source>1.6</source>
                            <target>1.6</target>
                        </configuration>
                    </execution>


                </executions>

                <dependencies>
                    <dependency>
                        <groupId>com.sun.xml.ws</groupId>
                        <artifactId>jaxws-ri</artifactId>
                        <version>2.2.6</version>
                        <type>pom</type>
                    </dependency>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                    <dependency>
                        <groupId>commons-beanutils</groupId>
                        <artifactId>commons-beanutils</artifactId>
                        <version>1.7.0</version>
                    </dependency>
                    <dependency>
                        <groupId>backport-util-concurrent</groupId>
                        <artifactId>backport-util-concurrent</artifactId>
                        <version>3.1</version>
                    </dependency>

                </dependencies>

            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <inherited>true</inherited>
                <dependencies>
                    <dependency>
                        <groupId>com.oopsconsultancy</groupId>
                        <artifactId>xmltask</artifactId>
                        <version>1.16</version>
                    </dependency>
                    <dependency>
                        <groupId>ant</groupId>
                        <artifactId>ant-jsch</artifactId>
                        <version>1.6.5</version>
                    </dependency>
                    <dependency>
                        <groupId>com.jcraft</groupId>
                        <artifactId>jsch</artifactId>
                        <version>0.1.55</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>setProprerties</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Setar propriedades da Aplicacao ${basedir}" />
                                <copy failonerror="false" verbose="false" file="${basedir}/src/main/resources/META-INF/persistence-${profile}.xml"
                                      tofile="${basedir}/target/classes/META-INF/persistence.xml"/>

                                <copy failonerror="false" verbose="false" file="${basedir}/src/main/webapp/WEB-INF/applicationContext-${profile}.xml"
                                      tofile="${basedir}/target/${project.name}/WEB-INF/applicationContext.xml"/>

                                <copy failonerror="true" verbose="false" file="${basedir}/src/main/webapp/WEB-INF/glassfish-web-original.xml"
                                      overwrite="true"
                                      tofile="${basedir}/src/main/webapp/WEB-INF/glassfish-web.xml"/>

                                <copy verbose="false" file="${basedir}/src/main/resources/br/com/pacto/util/resources/OpcoesGlobais.properties"
                                      tofile="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais-temp.properties">
                                    <filterchain>
                                        <replacetokens>
                                            <token key="URL_LOGIN" value="${urlLOGIN}"/>
                                            <token key="URL_FEED" value="${urlFeedPacto}"/>
                                            <token key="URL_OAMD" value="${urlOAMD}"/>
                                            <token key="URL_OAMD_SEGURA" value="${urlOAMDSegura}"/>
                                            <token key="URL_ADM_APP" value="${urlAdmApp}"/>
                                            <token key="KEY_TESTS" value="${keyUnitTests}"/>
                                            <token key="URL_BANNERS" value="${urlObterBanners}"/>
                                            <token key="INSTANCE_SCHEDULER" value="${instanceSchedulingName}"/>
                                            <token key="LOAD_FACTORY_START" value="${loadFactoryOnStart}"/>
                                            <token key="REDIR" value="${redir}"/>
                                            <token key="URL_REDIR" value="${url_redirecionar}"/>
                                            <token key="INSTANCIAS_NOTIFICAR" value="${instanciasPropagar}"/>
                                            <token key="LOAD_INSTANCES_CLOUD" value="${loadInstancesFromCloud}"/>
                                            <token key="FORCE_URL_FOTOS" value="${forceUrlFotos}"/>
                                            <token key="COOKIE_FAIL" value="${COOKIE_FAIL}"/>
                                            <token key="DIR_MIDIAS_EMBEDDED" value="${dirMidiasEmbedded}"/>
                                            <token key="REDIRECIONAR_LOGIN" value="${redirecionarLogin}"/>
                                            <token key="FOTOS_NUVEM" value="${FOTOS_NUVEM}"/>
                                            <token key="URL_FOTOS_NUVEM" value="${URL_FOTOS_NUVEM}"/>
                                            <token key="URL_ARQUIVOS_NUVEM" value="${URL_ARQUIVOS_NUVEM}"/>
                                            <token key="TIPO_MIDIA" value="${TIPO_MIDIA}"/>
                                            <token key="BUSCAR_CONHECIMENTO_UCP" value="${BUSCAR_CONHECIMENTO_UCP}"/>
                                            <token key="USAR_URL_RECURSO_EMPRESA" value="${USAR_URL_RECURSO_EMPRESA}"/>
                                            <token key="MY_URL_UP_BASE" value="${MY_URL_UP_BASE}"/>
                                            <token key="DISCOVERY_URL" value="${DISCOVERY_URL}"/>
                                            <token key="AUTH_SECRET_PATH" value="${AUTH_SECRET_PATH}"/>
                                            <token key="SERVIDOR_MEMCACHED" value="${SERVIDOR_MEMCACHED}"/>
                                            <token key="AUTH_SECRET_PERSONA_PATH" value="${AUTH_SECRET_PERSONA_PATH}" />
                                            <token key="VERSION" value="${project.version}" />
                                            <token key="URL_HTTP_PLATAFORMA_PACTO" value="${URL_HTTP_PLATAFORMA_PACTO}"/>
                                            <token key="URL_HTTPS_PLATAFORMA_PACTO" value="${URL_HTTPS_PLATAFORMA_PACTO}"/>
                                            <token key="NUMEROS_WHATSAPP_PACTO" value="${NUMEROS_WHATSAPP_PACTO}"/>
                                            <token key="URL_ZW_INTEGRACAO" value="${URL_ZW_INTEGRACAO}"/>
                                            <token key="USE_BETA_TESTERS" value="${USE_BETA_TESTERS}"/>
                                            <token key="INSTANCE_ALLOW_DDL" value="${INSTANCE_ALLOW_DDL}"/>
                                            <token key="ENABLE_NEW_LOGIN" value="${ENABLE_NEW_LOGIN}" />
                                            <token key="AMBIENTE_TESTE" value="${AMBIENTE_TESTE}" />
                                            <token key="TOKENS_ACESSO_API_CLIENTE" value="${TOKENS_ACESSO_API_CLIENTE}" />
                                            <token key="LOGAR_DAO" value="${LOGAR_DAO}" />
                                            <token key="TOKEN_GYMPASS_V3" value="${TOKEN_GYMPASS_V3}"/>
                                            <token key="MODO_CONSULTA_AGENDA" value="${MODO_CONSULTA_AGENDA}"/>
                                            <token key="CONSULTA_CONTRATO_ASSINATURA" value="${CONSULTA_CONTRATO_ASSINATURA}"/>
                                            <token key="CONSULTA_USUARIOS_APP_PELO_FIREBASE" value="${CONSULTA_USUARIOS_APP_PELO_FIREBASE}"/>
                                            <token key="MODO_CONSULTA_DASHBI" value="${MODO_CONSULTA_DASHBI}"/>
                                            <token key="AES_SECRET_APP_TREINO" value="${AES_SECRET_APP_TREINO}"/>
                                            <token key="URL_OAUTH_TOKEN_SELFLOOPS" value="${URL_OAUTH_TOKEN_SELFLOOPS}"/>
                                            <token key="URL_API_SELFLOOPS" value="${URL_API_SELFLOOPS}"/>
                                            <token key="PACTO_CLIENT_ID_SELFLOOPS" value="${PACTO_CLIENT_ID_SELFLOOPS}"/>
                                            <token key="PACTO_SECRET_ID_SELFLOOPS" value="${PACTO_SECRET_ID_SELFLOOPS}"/>
                                        </replacetokens>
                                    </filterchain>
                                </copy>
                                <move verbose="false" file="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais-temp.properties"
                                      tofile="${basedir}/target/classes/br/com/pacto/util/resources/OpcoesGlobais.properties"/>

                                <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>
                                <xmltask outputter="simple" source="${basedir}/src/main/webapp/WEB-INF/web.xml" dest="${basedir}/target/${project.name}/WEB-INF/web.xml">

                                </xmltask>
                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>fillOAMD2</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Configurando URL do OAMD2 para ${urlOAMD2}..." />
                                <taskdef name="xmltask" classname="com.oopsconsultancy.xmltask.ant.XmlTask"/>
                                <xmltask outputter="simple" source="${basedir}/target/classes/br/com/pacto/base/oamd/cfgBD.xml"
                                         dest="${basedir}/target/classes/br/com/pacto/base/oamd/cfgBD.xml">
                                    <replace path="/aplicacao/bd/url-oamd/text()"
                                             withText="${urlOAMD2}"/>
                                    <replace path="/aplicacao/bd/username/text()"
                                             withText="${userOAMD2}"/>
                                    <replace path="/aplicacao/bd/senha/text()"
                                             withText="${pwdOAMD2}"/>
                                </xmltask>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>1.10</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <complianceLevel>1.8</complianceLevel>
                    <Xlint>ignore</Xlint>
                    <forceAjcCompile>true</forceAjcCompile>
                    <basedir>src/main/java</basedir>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjtools</artifactId>
                        <version>${aspectj.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <finalName>${project.artifactId}</finalName>
    </build>
</project>

