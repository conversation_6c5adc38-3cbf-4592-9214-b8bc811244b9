version: '3.7'
services: 
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    ports: 
      - ${ZW_PORT}:8080
    depends_on:
      - postgres
  treino:
    image: registry.gitlab.com/plataformazw/treino/tomcat:master
    ports: 
      - ${TREINO_PORT}:8080
    depends_on:
      - postgres
    environment: 
      URL_ZW_INTEGRACAO: http://*********:$ZW_PORT/ZillyonWeb

  postgres:
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    environment:
      INIT_DB: teste
      IP_HOST: postgres
      URL_ZW: http://zw:8080/ZillyonWeb
      URL_TREINO: http://treino:8080/TreinoWeb
      BDZILLYON_VERSION: 1469


