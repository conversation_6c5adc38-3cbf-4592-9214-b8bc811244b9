# Análise do Sistema Atual e Implementação do "Monitor Rede"

## Arquitetura Atual Identificada

### 1. Sistema de WODs e Ranking
- **WodController**: Gerencia WODs com endpoints para CRUD e consultas
- **RankingController**: Endpoint atual para ranking por WOD específico (`/psec/ranking`)
- **RankingJSON**: Estrutura de dados do ranking contendo:
  - Posição, nome do aluno, matrícula, foto
  - Dados do WOD (tempo, peso, repetições, rounds)
  - Informações de nível e unidade (através do usuário)
- **ScoreTreino**: Bean principal com resultados dos treinos
- **WodService**: Interface com métodos de ranking e consultas

### 2. Estrutura de Dados Relevante
- **ScoreTreino**: Contém usuário, WOD, tempo, peso, repetições, rounds, posição
- **Usuario**: Vinculado ao cliente e empresa
- **Empresa**: Possui `codZW` (código da unidade) e informações da academia
- **RankingJSON**: DTO que já possui os campos necessários para o monitor

## Implementação Necessária

### Front-end
1. **Novo Item de Menu**: "Monitor Rede"
2. **Nova Tela**: Similar à atual, mas com dados agregados de rede
3. **Campos Adicionais**:
   - Nome da unidade (já disponível via empresa)
   - Colocação no ranking da unidade (novo cálculo necessário)

### Back-end - Novos Endpoints

#### Endpoint Principal
```java
GET /psec/ranking/rede?wodId={id}&periodo={dias}
```

#### Endpoint de Consulta Inter-Zona
```java
GET /psec/ranking/unidade?wodId={id}&periodo={dias}
```

## Desafios Técnicos Identificados

### Arquitetura Multi-Zona
- Cada unidade da rede está em bancos/zonas diferentes
- Necessidade de consultas HTTP entre zonas
- Latência e sobrecarga de rede proporcional ao número de unidades

### Performance e Escalabilidade
- Com 100 unidades, consultas a cada 30s = 3.000 requisições/minuto
- Necessário sistema de cache inteligente
- Possível implementação de rate limiting

## Soluções Propostas

### Cache Distribuído
```
- TTL configurável (30s-1min)
- Cache por WOD + período
- Invalidação inteligente
```

### Consultas Otimizadas
```
- Batch requests quando possível
- Consultas assíncronas
- Circuit breaker para zonas indisponíveis
```

### Estrutura de Dados
```java
// Extensão do RankingJSON atual
public class RankingRedeJSON extends RankingJSON {
    private String nomeUnidade;
    private Integer codigoUnidade;
    private Integer colocacaoUnidade;
    private Date dataExecucao;
}
```

## Considerações de Implementação

### Configuração de Rede
- Tabela de relacionamento entre unidades da rede
- Configuração de URLs/endpoints das outras zonas
- Autenticação entre zonas

### Monitoramento
- Logs de performance das consultas inter-zona
- Métricas de cache hit/miss
- Alertas para zonas indisponíveis

### Fallback
- Dados em cache quando zona indisponível
- Indicação visual de dados parciais
- Timeout configurável para consultas

## Tarefas de Implementação

1. **Criar endpoint para ranking de rede**
   - Implementar endpoint `/psec/ranking/rede` que consulta ranking de WOD agregando dados de todas as unidades da rede da academia logada
   - Deve retornar lista consolidada com nome do aluno, unidade, WOD executado, tempo/pontuação, data e colocação no ranking da unidade

2. **Implementar serviço de consulta inter-zonas**
   - Criar serviço que realiza consultas HTTP para outras zonas/chaves da rede, coletando dados de ranking de WODs
   - Implementar cache e otimizações para evitar sobrecarga dos servidores

3. **Estender RankingJSON para dados de rede**
   - Adicionar campos necessários ao RankingJSON: nomeUnidade, codigoUnidade, colocacaoUnidade
   - Manter compatibilidade com estrutura atual

4. **Implementar lógica de identificação de rede**
   - Criar mecanismo para identificar quais unidades pertencem à mesma rede da academia logada
   - Baseado em configurações ou relacionamentos entre empresas

5. **Otimizar consultas de ranking por unidade**
   - Implementar cálculo eficiente da colocação no ranking por unidade
   - Evitar recálculos desnecessários e utilizar índices apropriados

6. **Implementar controle de cache e atualização**
   - Criar sistema de cache para dados de ranking de rede com TTL configurável (sugestão: 30s-1min)
   - Reduzir carga nos servidores e melhorar performance

## Recomendações

1. **Implementação Incremental**: Começar com 2-3 unidades para validar arquitetura
2. **Cache Agressivo**: TTL de 1-2 minutos para reduzir carga
3. **Consulta Assíncrona**: Não bloquear interface durante coleta de dados
4. **Configuração Flexível**: Permitir ajuste de timeouts e TTL por ambiente
5. **Monitoramento**: Implementar métricas desde o início

## Próximos Passos

A implementação deve seguir a ordem das tarefas criadas, começando pela extensão do RankingJSON e criação do endpoint principal, seguido pela implementação do sistema de consultas inter-zona com cache otimizado.

## Arquivos Relevantes Identificados

- `src/main/java/br/com/pacto/controller/json/ranking/RankingController.java`
- `src/main/java/br/com/pacto/controller/json/wod/WodController.java`
- `src/main/java/br/com/pacto/controller/json/crossfit/RankingJSON.java`
- `src/main/java/br/com/pacto/service/intf/wod/WodService.java`
- `src/main/java/br/com/pacto/bean/wod/ScoreTreino.java`
- `src/main/java/br/com/pacto/bean/empresa/Empresa.java`
